# EditorConfig is awesome: https://EditorConfig.org

# 表示是最顶层的配置文件
root = true

# 所有文件适用
[*]
# 编码字符集
charset = utf-8
# 缩进风格是空格
indent_style = space
# 缩进大小为2
indent_size = 2
# 换行符使用 lf
end_of_line = lf
# 文件末尾插入一个空行
insert_final_newline = true
# 删除行尾空格
trim_trailing_whitespace = true

# markdown文件
[*.md]
# markdown文件不删除行尾空格
trim_trailing_whitespace = false

# Vue文件
[*.{vue,js,jsx,ts,tsx}]
# 每行最大长度
max_line_length = 120
# 分号结尾
quote_type = single

# JSON文件
[*.json]
indent_size = 2

# YAML文件
[*.{yml,yaml}]
indent_size = 2 