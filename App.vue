<script>
export default {
  onLaunch: function () {
    // setTimeout(() => {
    // const res = uni.getSystemInfoSync()
    // plus.screen.lockOrientation("landscape");
    // plus.navigator.setFullscreen(true)
    // this.fullScreen();
    // }, 300);
    // #ifdef APP-PLUS
    this.plusReady(this.getAndroidEnv);
		// #endif
  },
  onShow: function () {
    plus.navigator.setFullscreen(true);
    plus.navigator.hideSystemNavigation();
  },
  onHide: function () {},
  methods: {
    plusReady(cb) {
      if (window.plus) {
        cb();
      } else {
        document.addEventListener('plusready', cb);
      }
    },
    getAndroidEnv() {
      plus.runtime.getProperty(plus.runtime.appid, function (wgtinfo) {
        console.log('wgtinfo: ', wgtinfo);
        // 如读取ENV_TYPE
        if (wgtinfo.metaData?.ENV_TYPE) {
          uni.setStorageSync('ENV_TYPE', wgtinfo.metaData.ENV_TYPE || 'TEST');
        }
        uni.setStorageSync('ENV_TYPE', wgtinfo.metaData?.ENV_TYPE || 'TEST');
      });
    },
    // fullScreen: function() {
    // 	try {
    // 		var Color = plus.android.importClass("android.graphics.Color");
    // 		plus.android.importClass("android.view.Window");
    // 		var mainActivity = plus.android.runtimeMainActivity();
    // 		var window_android = mainActivity.getWindow();
    // 		var WindowManager = plus.android.importClass("android.view.WindowManager");
    // 		var View = plus.android.importClass("android.view.View");
    // 		//设置为全透明
    // 		window_android.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS | WindowManager.LayoutParams
    // 			.FLAG_TRANSLUCENT_NAVIGATION);
    // 		window_android.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View
    // 			.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION | View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
    // 		window_android.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
    // 		window_android.setNavigationBarColor(Color.TRANSPARENT);
    // 		//设置为全透明结束
    // 	} catch (e) {
    // 		console.log(e);
    // 	}

    // 	//window_android.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION);  //设置为半透明

    // 	// plus.android.invoke(window_android,"setNavigationBarColor",Color.parseColor("#000")); //为虚拟按钮添加背景色
    // }
  },
};
</script>

<style lang="scss">
/* 引入全局样式 */
@import '@/uni_modules/uview-ui/index.scss';
@import '@/styles/directives.scss';

page {
  height: 100%;
  width: 100%;
}
</style>
