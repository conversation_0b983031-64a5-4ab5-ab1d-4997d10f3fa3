# 菜单权限控制功能实现

## 需求描述
非数字化门店，只能看到【用户】【AI 咨询】菜单，登录时会返回 `is_opc === '1'` 则为数字化，否则为非数字化。

## 实现方案

### 1. 修改登录逻辑 (`pages/login/index.vue`)
- **问题修复**: 移除了阻止登录流程继续的 `return` 语句
- **功能**: 登录成功后，用户信息（包含 `is_opc` 字段）会被存储到本地存储中

### 2. 添加数字化状态判断工具 (`utils/runtime.js`)
新增了以下函数：
```javascript
// 获取 is_opc 字段值
export function getIsOpc() {
  return getUserInfo().is_opc || '0'
}

// 判断是否为数字化门店
export function isDigitalStore() {
  return getIsOpc() === '1'
}
```

### 3. 实现菜单过滤逻辑 (`pages/index/index.vue`)
- **菜单过滤**: 添加了 `filteredMenus` 计算属性
  - 数字化门店 (`is_opc === '1'`): 显示所有菜单
  - 非数字化门店 (`is_opc !== '1'`): 只显示【用户】和【AI咨询】菜单

- **路由权限控制**: 在路由监听器中添加权限检查
  - 非数字化门店尝试访问不允许的菜单时，会自动重定向到【AI咨询】菜单

## 功能特性

### ✅ 数字化门店 (is_opc === '1')
- 可以看到所有菜单：【用户】【产品】【AI咨询】【预约】【日会】
- 可以正常访问所有功能模块

### ✅ 非数字化门店 (is_opc !== '1')
- 只能看到：【用户】【AI咨询】菜单
- 尝试访问其他菜单时会自动重定向到【AI咨询】
- 登录后默认进入【用户】菜单（符合权限要求）

## 技术实现细节

### 菜单ID映射
- `'0'`: AI咨询
- `'1'`: 用户  
- `'2'`: 产品
- `'3'`: 预约
- `'4'`: 日会

### 权限控制逻辑
```javascript
// 菜单过滤
filteredMenus() {
  if (isDigitalStore()) {
    return this.menus; // 显示所有菜单
  } else {
    return this.menus.filter(menu => menu.id === '1' || menu.id === '0'); // 只显示用户和AI咨询
  }
}

// 路由权限检查
if (!isDigitalStore()) {
  const allowedMenuIds = ['1', '0']; // 用户和AI咨询
  if (!allowedMenuIds.includes(targetMenuId)) {
    targetMenuId = '0'; // 重定向到AI咨询
  }
}
```

## 测试验证
已通过完整的单元测试验证：
- ✅ 菜单过滤逻辑正确
- ✅ 路由权限控制正确
- ✅ 数字化门店可访问所有菜单
- ✅ 非数字化门店只能访问指定菜单
- ✅ 非法访问会正确重定向

## 使用说明
1. 登录时服务器返回的用户信息中需要包含 `is_opc` 字段
2. `is_opc === '1'` 表示数字化门店，可以访问所有功能
3. `is_opc !== '1'` 表示非数字化门店，只能访问【用户】和【AI咨询】功能
4. 系统会自动根据用户类型显示相应的菜单和控制访问权限
