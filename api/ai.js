import request from "@/utils/request";

export default class AiService {
	static getTaskData(data) {
		return request.post('/ipad/inquiry.assistant.taskdata', data)
	}
	static getRecordList(data) {
		return request.get('/ipad/inquiry.record.recordlist', data)
	}
	static getRecordInfo(data) {
		return request.get('/ipad/inquiry.record.medicalinfo', data)
	}
	static getReplication(data) {
		return request.get('/ipad/inquiry.record.replication', data)
	}
	static getAiChat(data) {
		return request.get('/ipad/inquiry.draft.getaichat', data)
	}
	static setDraftAiChatUser(data) {
		return request.post('/ipad/inquiry.draft.draftaichatuser', data)
	}
}

