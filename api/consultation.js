import request from "@/utils/request";

export default class ConsultationService {
  // 体格检查、既往史map
  static getAssistantOptions() {
    return request.get('/ipad/inquiry.assistant.healthmap')
  }

  // 获取症状树-主诉、望闻问切
  static getSymptomTreeV4() {
    return request.get('/ipad/inquiry.assistant.symptoms')
  }

  // 获取智能问诊
  static getIntelligentConsultation(data) {
    return request.post('/ipad/inquiry.assistant.recommendsymptoms', data)
  }

  // 搜索疾病
  static searchDisease(data) {
    return request.get('/ipad/inquiry.assistant.diseasesearch', data)
  }

  // 获取问诊 是否进行中
  static getConsultationStatus() {
    return request.get('/ipad/index.getConsultationStatus')
  }

  // 问诊建单
  static createConsultation(data) {
    return request.post('/ipad/index.createConsultation', data)
  }

  // 关键字查询患者列表
  static getPatientList(params) {
    return request.get('/ipad/patient.patient.search', params)
  }

  // 获取七牛token
  static getUpToken(data) {
    return request.get('/ipad/qiniu.getuptoken', data)
  }

  static getGoodsList(params) {
    return request.get('/ipad/goods.index.list', params)
  }

  static getGoodsOptions() {
    return request.get('/ipad/goods.index.options')
  }

  static commitAssistantMedical(params) {
    return request.post('/ipad/inquiry.assistant.commit', params)
  }

  // 上传影像资料
  static uploadRecordSaveimages(params) {
    return request.post('/ipad/inquiry.record.saveimages', params)
  }

  static getPatientInfoByMrId(params) {
    return request.get('/ipad/inquiry.record.getpatientbymr', params)
  }

  static getRecordInfo(params) {
    return request.get('/ipad/inquiry.record.info', params)
  }

  // 获取影像信息
  static getRecordImages(params) {
    return request.get('/ipad/inquiry.record.images', params)
  }

  // 完成问诊
  static getPresSavefinish(params) {
    return request.post('/ipad/medical.pres.savefinish', params)
  }


  static getDraftData(params) {
    return request.get('/ipad/inquiry.draft.getpres', params)
  }

  // 删除问诊
  static deleteRecord(params) {
    return request.post('/ipad/inquiry.record.deleterecord', params)
  }

  // 获取舌象对比
  static getTongueDiff(params) {
    return request.get('/ipad/inquiry.record.tonguediff', params)
  }
}

