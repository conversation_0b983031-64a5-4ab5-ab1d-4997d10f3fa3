import request from "@/utils/request";

class LoginService {
	/**
	 * 登录
	 * @param {Object} data - 登录参数
	 * @returns {Promise} 登录结果
	 */
	static login(data) {
		return request.post('/ipad/index.login', data)
	}

	/**
	 * 获取诊所列表
	 * @param {Object} data - 查询参数
	 * @param {Object} options - 请求配置
	 * @returns {Promise} 诊所列表
	 */
	static getClinicList(data, options) {
		return request.get('/ipad/index.clinicList', data, options)
	}

	// 获取验证码
	static getAuthCode(data, options) {
		return request.post('/ipad/mobile.sendauthcode', data, options)
	}
	// 获取最后一次诊断记录（登录时调用）
	static getDayLastRecord(data, options) {
		return request.get('/ipad/inquiry.record.daylastmrid', data, options)
	}

  // 刷新token
  static refreshToken(data, options) {
    return request.post('/ipad/member.refreshtoken', data, options)
  }
  // 退出登录
  static logout(data, options) {
    return request.post('/ipad/member.logout', data, options)
  }
  // 二维码登录
  static qrLogin(data, options) {
    return request.post('/ipad/index.qrcodelogin', data, options)
  }

}


export default LoginService;
