import request from "@/utils/request";

export default class MeetingService {

  static getMeetingDailyInfo(data) {
    return request.get('/ipad/meetings.daily.index', data)
  }
  static getMeetingDailyMorning(data) {
    return request.get('/ipad/meetings.daily.morning', data)
  }
  static getMeetingDailyEvening(data) {
    return request.get('/ipad/meetings.daily.evening', data)
  }
  static getMeetingDailyUserList(data) {
    return request.get('/ipad/meetings.daily.dailyuserlist', data)
  }
  static getMeetingDailyTarget(data) {
    return request.get('/ipad/meetings.daily.getdailytarget', data)
  }
  static setMeetingDailyTarget(data) {
    return request.post('/ipad/meetings.daily.setdailytarget', data)
  }
  static submitDailyMorning(data) {
    return request.post('/ipad/meetings.daily.submitmorning', data)
  }
  static submitDailyEvening(data) {
    return request.post('/ipad/meetings.daily.submitevening', data)
  }
}
