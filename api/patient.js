import request from "@/utils/request";

export default class patientService {
	// 关键字查询患者列表
	static getPatientList(params) {
		return request.get('/ipad/patient.patient.search', params)
	}
	// 获取建单人列表
	static getClinicMemberList(params) {
		return request.get('/ipad/inquiry.record.memberlist', params)
	}
	// 获取左侧问诊记录
	static getInquiryRecordList(params) {
		return request.get('/ipad/inquiry.record.recordlist', params)
	}

	// 建单
	static createInquiryRecord(data) {
		return request.post('/ipad/inquiry.record.create', data)
	}
	// 创建用户
	static editPatientInfo(data) {
		return request.post('/ipad/patient.patient.patientedit', data)
	}

  // 查询用户中心用户信息
  static getUserExistMobile(params) {
    return request.get('/ipad/patient.patient.existmobileuc', params)
  }



}

