import request from "@/utils/request";

export default class payService {
	// 获取支付枚举
	static getPayOptions(params) {
		return request.get('/ipad/order.pay.options', params)
	}

  // 提交预付款
  static getPayShop(params) {
    return request.post('/ipad/order.pay.shop', params)
  }

  // 确认支付
  static getPayConfirm(params) {
    return request.post('/ipad/order.pay.confirm', params)
  }

  // 获取支付状态
  static getPayPaystate(params) {
    return request.get('/ipad/order.pay.paystate', params)
  }

  // 获取订单信息
  static getPayInfo(params) {
    return request.get('/ipad/order.pay.info', params)
  }


}

