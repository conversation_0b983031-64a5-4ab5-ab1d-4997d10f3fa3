import request from "@/utils/request";

export default class productService {
	// 用户列表（创建订单使用）
	static getUserUserList(params) {
		return request.get('/ipad/user.index.userlist', params)
	}

  // 平台商品列表
  static getGoodsPlatGoods(params) {
    return request.get('/ipad/goods.index.platgoods', params)
  }

  // 创建订单
  static getOrderCreate(params) {
    return request.post('/ipad/order.order.create', params)
  }

  // 查询订单状态
  async getOrderPayStatus(params) {
    return await this.$http.get('/ipad/order.pay.queryordertrade', { params });
  }
}

