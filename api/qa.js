import request from "@/utils/request";

export default class qaService {
	// 获取检查单枚举
	static getQAOption(params) {
		return request.get('/ipad/inquiry.qa.option', params)
	}
	// 获取检查单列表
	static getQAContent(params) {
		return request.get('/ipad/inquiry.qa.content', params)
	}
	// 获取检查单详情
	static getQADetail(params) {
		return request.get('/ipad/inquiry.qa.detail', params)
	}

	// 建单
	static commitQAContent(data) {
		return request.post('/ipad/inquiry.qa.commit', data)
	}

}

