import request from "@/utils/request";

export default class ReserveService {

  static getUserReserveList(data) {
    return request.get('/ipad/reserve.reserve.list', data)
  }
  static getUserReserveOptions(data) {
    return request.get('/ipad/reserve.reserve.options', data)
  }
  static getUserReserveDetail(data) {
    return request.get('/ipad/reserve.reserve.show', data)
  }
  static getUserReserveTimeRange(data) {
    return request.get('/ipad/reserve.reserve.timerange', data)
  }
  static cancelUserReserve(data) {
    return request.post('/ipad/reserve.reserve.cancel', data)
  }
  static arrivalUserReserve(data) {
    return request.post('/ipad/reserve.reserve.arrival', data)
  }
  static updateUserReserve(data) {
    return request.post('/ipad/reserve.reserve.update', data)
  }
  static getReservehistorylist(data) {
    return request.post('/ipad/reserve.reserve.reservehistorylist', data)
  }

  static getReserveTimerange(data) {
    return request.get('/ipad/reserve.reserve.timerange', data)
  }

  static getAvailableTimePieces(data) {
    return request.get('/ipad/reserve.reserve.availableTimePieces', data)
  }

  static getReserveTechnicians(data) {
    return request.get('/ipad/reserve.reserve.technicians', data)
  }

  static getReserveUpdate(data) {
    return request.post('/ipad/reserve.reserve.update', data)
  }

  static getReserveConfirm(data) {
    return request.post('/ipad/reserve.reserve.confirm', data)
  }

  static getReserveFinished(data) {
    return request.post('/ipad/reserve.reserve.finished', data)
  }
}
