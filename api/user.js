import request from "@/utils/request";

export default class UserService {

  static getUserTableList(data) {
    return request.get('/ipad/user.index.list', data)
  }
  static getUserOptions(data) {
    return request.get('/ipad/user.index.option', data)
  }
  static getUserOrderList(data) {
    return request.get('/ipad/order.order.list', data)
  }
  static getUserDetail(data) {
    return request.get('/ipad/user.index.detail', data)
  }
  // 用户卡券-列表
  static getUserCardList(data) {
    return request.get('/ipad/card.user.batchlist', data)
  }
  // 用户卡券-选项描述
  static getUserCardOptions(data) {
    return request.get('/ipad/card.user.optiondesc', data)
  }
  // 用户卡券-批次详情
  static getUserCardDetail(data) {
    return request.get('/ipad/card.user.batchdetail', data)
  }
  // 通兑券列表
  static getUserExchangeList(data) {
    return request.get('/ipad/exchange.card.list', data)
  }
  // 通兑券详情
  static getUserExchangeDetail(data) {
    return request.get('/ipad/exchange.card.info', data)
  }
  // 兑换卡卷
  static exchangeCardByDetail(data) {
    return request.post('/ipad/exchange.card.exchange', data)
  }
  // 问诊检查
  static checkUserHasMr(data) {
    return request.get('/ipad/medical.pres.checkusermr', data)
  }
  //
  // static setDraftAiChatUser(data) {
  //   return request.post('/ipad/inquiry.draft.draftaichatuser', data)
  // }

  // 初诊表枚举
  static getRecordFirstvisitoption(data) {
    return request.get('/ipad/inquiry.record.firstvisitoption', data)
  }

  // 保存初诊表
  static Savefirstvisitrecord(data) {
    return request.post('/ipad/inquiry.record.savefirstvisitrecord', data)
  }

  // 保存初诊表
  static getFirstvisitdetail(data) {
    return request.get('/ipad/inquiry.record.firstvisitdetail', data)
  }
}
