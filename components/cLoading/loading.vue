<template>
  <view v-if="show" class="table-loading">
    <view class="loading-loader">
      <view class="loader"></view>
      <!--            <view class="loader-text">正在加载...</view>-->
    </view>
  </view>
</template>

<script>
export default {
  name: 'CLoading',
  props: {
    show: <PERSON><PERSON><PERSON>
  }
}
</script>
<style scoped lang="scss">
.table-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
  .loading-loader {
    width: 300rpx;
    height: 200rpx;
    //padding: 64rpx 0;
    border-radius: 12rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    //background: rgba(0, 0, 0, 0.5);
    .loader-text {
      color: #fff;
      font-size: 30rpx;
      margin-top: 36rpx;
    }
  }
}
.loader {
  font-size: 6rpx;
  width: 1em;
  height: 1em;
  border-radius: 50%;
  position: relative;
  text-indent: -9999em;
  animation: mulShdSpin 1.1s infinite ease;
  transform: translateZ(0);
}
@keyframes mulShdSpin {
  0%,
  100% {
    box-shadow: 0em -2.6em 0em 0em #000, 1.8em -1.8em 0 0em rgba(0,0,0, 0.2), 2.5em 0em 0 0em rgba(0,0,0, 0.2), 1.75em 1.75em 0 0em rgba(0,0,0, 0.2), 0em 2.5em 0 0em rgba(0,0,0, 0.2), -1.8em 1.8em 0 0em rgba(0,0,0, 0.2), -2.6em 0em 0 0em rgba(0,0,0, 0.5), -1.8em -1.8em 0 0em rgba(0,0,0, 0.7);
  }
  12.5% {
    box-shadow: 0em -2.6em 0em 0em rgba(0,0,0, 0.7), 1.8em -1.8em 0 0em #000, 2.5em 0em 0 0em rgba(0,0,0, 0.2), 1.75em 1.75em 0 0em rgba(0,0,0, 0.2), 0em 2.5em 0 0em rgba(20,0,0, 0.2), -1.8em 1.8em 0 0em rgba(20,0,0, 0.2), -2.6em 0em 0 0em rgba(20,0,0, 0.2), -1.8em -1.8em 0 0em rgba(20,0,0, 0.5);
  }
  25% {
    box-shadow: 0em -2.6em 0em 0em rgba(20,0,0, 0.5), 1.8em -1.8em 0 0em rgba(20,0,0, 0.7), 2.5em 0em 0 0em #000, 1.75em 1.75em 0 0em rgba(20,0,0, 0.2), 0em 2.5em 0 0em rgba(20,0,0, 0.2), -1.8em 1.8em 0 0em rgba(20,0,0, 0.2), -2.6em 0em 0 0em rgba(20,0,0, 0.2), -1.8em -1.8em 0 0em rgba(20,0,0, 0.2);
  }
  37.5% {
    box-shadow: 0em -2.6em 0em 0em rgba(20,0,0, 0.2), 1.8em -1.8em 0 0em rgba(20,0,0, 0.5), 2.5em 0em 0 0em rgba(20,0,0, 0.7), 1.75em 1.75em 0 0em #000, 0em 2.5em 0 0em rgba(20,0,0, 0.2), -1.8em 1.8em 0 0em rgba(20,0,0, 0.2), -2.6em 0em 0 0em rgba(20,0,0, 0.2), -1.8em -1.8em 0 0em rgba(20,0,0, 0.2);
  }
  50% {
    box-shadow: 0em -2.6em 0em 0em rgba(20,0,0, 0.2), 1.8em -1.8em 0 0em rgba(20,0,0, 0.2), 2.5em 0em 0 0em rgba(20,0,0, 0.5), 1.75em 1.75em 0 0em rgba(20,0,0, 0.7), 0em 2.5em 0 0em #000, -1.8em 1.8em 0 0em rgba(20,0,0, 0.2), -2.6em 0em 0 0em rgba(20,0,0, 0.2), -1.8em -1.8em 0 0em rgba(20,0,0, 0.2);
  }
  62.5% {
    box-shadow: 0em -2.6em 0em 0em rgba(20,0,0, 0.2), 1.8em -1.8em 0 0em rgba(20,0,0, 0.2), 2.5em 0em 0 0em rgba(20,0,0, 0.2), 1.75em 1.75em 0 0em rgba(20,0,0, 0.5), 0em 2.5em 0 0em rgba(20,0,0, 0.7), -1.8em 1.8em 0 0em #000, -2.6em 0em 0 0em rgba(20,0,0, 0.2), -1.8em -1.8em 0 0em rgba(20,0,0, 0.2);
  }
  75% {
    box-shadow: 0em -2.6em 0em 0em rgba(20,0,0, 0.2), 1.8em -1.8em 0 0em rgba(20,0,0, 0.2), 2.5em 0em 0 0em rgba(20,0,0, 0.2), 1.75em 1.75em 0 0em rgba(20,0,0, 0.2), 0em 2.5em 0 0em rgba(20,0,0, 0.5), -1.8em 1.8em 0 0em rgba(20,0,0, 0.7), -2.6em 0em 0 0em #000, -1.8em -1.8em 0 0em rgba(20,0,0, 0.2);
  }
  87.5% {
    box-shadow: 0em -2.6em 0em 0em rgba(20,0,0, 0.2), 1.8em -1.8em 0 0em rgba(20,0,0, 0.2), 2.5em 0em 0 0em rgba(20,0,0, 0.2), 1.75em 1.75em 0 0em rgba(20,0,0, 0.2), 0em 2.5em 0 0em rgba(20,0,0, 0.2), -1.8em 1.8em 0 0em rgba(20,0,0, 0.5), -2.6em 0em 0 0em rgba(20,0,0, 0.7), -1.8em -1.8em 0 0em #000;
  }
}
</style>
