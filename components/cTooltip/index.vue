<script>
export default {
  name: 'cTooltip',
  props: {
    text: String,
  },
  data() {
    return {
      show: false,
      isCanShowModal: false,
      retryCount: 0,
      maxRetries: 5,
      onReady: false
    };
  },
  mounted() {
    this.onReady = true
  },
  watch: {
    onReady: {
      handler(val) {
        if (!val || this.text.length === 0) {
          this.isCanShowModal = false;
          return;
        }
        setTimeout(() => {
          this.$nextTick(() => {
            this.checkEllipsis();
          })
        }, 36)
      },
      immediate: true,
    }
  },
  methods: {
    checkEllipsis() {
      const query = uni.createSelectorQuery().in(this);
      query
        .select('#ellipsis-box')
        .boundingClientRect()
        .exec(res => {
          if (!res || !res[0]) {
            return;
          }
          const ellipsisBox = res[0].width;
          const query2 = uni.createSelectorQuery().in(this);
          query2
            .select('#ellipsis')
            .boundingClientRect()
            .exec(res2 => {
              if (!res2 || !res2[0]) {
                return;
              }
              const ellipsis = res2[0].width;
              this.$nextTick(() => {
                this.isCanShowModal = ellipsis > ellipsisBox;
              })
            });
        });
    },
    openToolTip() {
      if (this.isCanShowModal) {
        this.show = true;
      }
    },
    onClose() {
      this.show = false;
    }
  }
}
</script>

<template>
  <view v-if="text" id="ellipsis-box" ref="ellipsis-box" class="ellipsis-box">
    <view
      ref="ellipsis"
      id="ellipsis"
      class="ellipsis"
      @longpress.stop.prevent="openToolTip"
      :style="{
        width: isCanShowModal ? '100%' : 'fit-content',
        overflow: isCanShowModal ? 'hidden' : 'unset',
        textOverflow: isCanShowModal ? 'ellipsis' : 'unset'
      }"
    >
      {{ text }}
    </view>
    <u-modal :show="show" :closeOnClickOverlay="true" :showConfirmButton="false" :zoom="true" :asyncClose="true" @close="onClose">
      <view class="tooltip-content">
        {{ text }}
      </view>
    </u-modal>
  </view>
</template>

<style scoped lang="scss">
.ellipsis-box {
  width: fit-content;
  max-width: 100%;
  overflow: hidden;
}
.ellipsis {
  width: fit-content;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.tooltip-content {
  padding: 12rpx 8rpx;
  white-space: pre-wrap;
}
</style>
