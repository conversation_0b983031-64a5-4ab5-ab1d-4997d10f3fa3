<template>
  <view>
    <uni-popup ref="popup" type="center" :is-mask-click="false">
      <view class="popup-box">
        <view class="content-head">
          <view class="title">选择预约时间</view>
          <img class="close-icon" src="https://static.rsjxx.com/image/2025/0610/134619_68219.png" @click="handleClose">
        </view>
        <view class="content">
          <picker-view :value="current_index" :indicator-style="indicatorStyle" class="picker-view" @change="pickerChange">
            <picker-view-column v-for="(c_item, c_index) in columns" :key="c_index+'column'">
              <view class="item" :class="{'active-item': Number(current_index[c_index] || 0) === index }" v-for="(item,index) in c_item" :key="index">{{item.desc}}</view>
            </picker-view-column>
          </picker-view>
        </view>
        <button size="default" class="confirm-btn" type="primary" @click="confirm">确定</button>
      </view>
    </uni-popup>
  </view>
</template>

<script>
export default {
  name: 'choose-time',
  props: {
    value: {
      type: Boolean,
      default: () => false
    },
    // 配置的每列
    columns: {
      type: Array,
      default: () => [
        [{ id: 1, desc: '5月21日 周三' }, { id: 2, desc: '5月22日 周四' }, { id: 3, desc: '5月23日 周五' }, { id: 4, desc: '5月24日 周六' }],
        [{ id: 1, desc: '19点' }, { id: 2, desc: '20点' }, { id: 3, desc: '21点' }, { id: 4, desc: '22点' }],
        [{ id: 1, desc: '10分' }, { id: 2, desc: '20分' }, { id: 3, desc: '30分' }, { id: 4, desc: '40分' }],
      ]
    },
    // 配置的初始化索引
    index: {
      type: Array,
      default: () => []
    }
  },
  components: {},
  mixins: [],
  data() {
    return {
      indicatorStyle: `height: 44px;background: rgba(21,91,212,0.06);`,
      current_index: [],
    };
  },
  computed: {},
  watch: {
    value (val) {
      this.$nextTick(() => {
        if (val) {
          this.$refs.popup.open()
          // 初始化索引，没有配置全部为第一项
          if (this.index.length != this.columns.length) {
            this.current_index = []
            this.columns.forEach(item => this.current_index.push(0))
          }else{
            this.current_index = this.index
          }
        }else{
          this.$refs.popup.close()
        }
      })
    }
  },
  created() {},
  mounted() {},
  methods: {
    pickerChange (e) {
      this.current_index = e.detail.value
    },
    confirm () {
      console.log("=>(index.vue:73)this.current_index", this.current_index);
      // 抛出各自的索引
      this.$emit('success', this.current_index)
      this.handleClose()
    },
    handleClose () {
      this.clearData()
      this.$emit('input', false)
    },
    clearData () {
      this.current_index = []
    },
  },
};
</script>

<style lang="scss" scoped>
.popup-box {
  width: 520px;
  height: 400px;
  background: #FFFFFF;
  border-radius: 4px;
  word-break: break-all;
  .content-head {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #EBEDF0;
    padding: 16px 20px;
    box-sizing: border-box;
    .title {
      flex: 1;
      font-weight: 600;
      font-size: 16px;
      color: #333333;
      line-height: 24px;
    }
    .close-icon {
      width: 12px;
      min-width: 12px;
      height: 12px;
      margin-left: 10px;
    }
  }

  .content {
    padding: 40px 20px;
    .picker-view {
      height: 200px;
      //color: #AAAAAA;

      .uni-picker-view-content {
        .item {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 44px;
          font-weight: 400;
          font-size: 14px;
          color: #1b1b1b;
          line-height: 20px;
        }
        .active-item {
          font-weight: 500;
          font-size: 16px;
          color: #155BD4;
        }
      }
    }
  }

  .confirm-btn {
    width: 160px;
    height: 40px;
    background: #155BD4;
    border-radius: 4px;
    font-weight: 400;
    font-size: 14px;
    color: #FFFFFF;
    line-height: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
