<template>
  <view>
    <uni-popup ref="popup" type="center" :is-mask-click="false">
      <view class="popup-box">
        <view class="content-head">
          <view class="title">选择预约时间</view>
          <img class="close-icon" src="https://static.rsjxx.com/image/2025/0610/134619_68219.png" @click="handleClose">
        </view>
        <view class="content">
          <view class="date-head">
            <scroll-view scroll-x :scroll-into-view="dateScrollId" scroll-with-animation class="date-scroll">
              <view class="date-item-box">
                <view class="date-item" :id="`date-${index}`" :class="{ 'date-item--active': date_index === index }" v-for="(item, index) in dates" :key="item" @click="dateChange(index)">
                  <view class="week-day">{{ getDayInfo(item).weekDay }}</view>
                  <view class="date">{{ getDayInfo(item).date }}</view>
                </view>
              </view>
            </scroll-view>
          </view>

          <scroll-view scroll-y class="time-scroll" scroll-with-animation :scroll-into-view="timeScrollId">
            <view class="time-item-box">
              <view class="time-item" :id="`time-${index}`" :class="{ 'time-item--active': checked_day === `${dates[date_index]} ${item}`, 'time-item--disabled': !timeIsCanReserve(item) }" v-for="(item, index) in times" :key="item" @click="timeChange(index)">
                {{ item }}
              </view>
            </view>
          </scroll-view>
          <c-loading :show="getTimeLoading" />
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import ReserveService from '@/api/reserve'
import dayjs from "dayjs";
import CLoading from "../cLoading/loading.vue";
export default {
  name: 'choose-time',
  props: {
    value: {
      type: Boolean,
      default: () => false
    },
    physio_id: {
      type: String,
      default: () => ''
    },
    reserve_date: {
      type: String,
      default: () => ''
    },
    reserve_time: {
      type: String,
      default: () => ''
    },
  },
  components: {CLoading},
  mixins: [],
  data() {
    return {
      getTimeLoading: false,
      date_index: 0,
      time_index: '',
      dateScrollId: '',
      timeScrollId: '',
      dates: [],
      times: [],
      available_time_pieces: [],
    };
  },
  computed: {
    checked_day () {
      return `${this.reserve_date} ${this.reserve_time}`
    },
    timeIsCanReserve () {
      return time => {
        return this.available_time_pieces.includes(time)
      }
    },
    // 转化星期
    computedWeekDesc() {
      return day => {
        switch (day) {
          case 1:
            return '周一';
          case 2:
            return '周二';
          case 3:
            return '周三';
          case 4:
            return '周四';
          case 5:
            return '周五';
          case 6:
            return '周六';
          case 0:
            return '周日';
        }
      };
    },
    getDayInfo () {
      return date => {
        return {
          weekDay: this.computedWeekDesc(dayjs(date).day()),
          date: dayjs(date).format('MM月DD日')
        }
      }
    },
  },
  watch: {
    value (val) {
      this.$nextTick(() => {
        if (val) {
          this.$refs.popup.open()
          this.getReserveTimerange()
        }else{
          this.$refs.popup.close()
        }
      })
    }
  },
  created() {},
  mounted() {},
  methods: {
    init () {
      this.$nextTick(() => {
        let d_index = this.dates.findIndex(item => item === this.reserve_date)
        let t_index = this.times.findIndex(item => item === this.reserve_time)
        if (d_index != -1) {
          this.dateScrollId = `date-${d_index}`
          this.date_index = d_index
        }else{
          this.dateScrollId = `date-0`
          this.date_index = 0
        }
        if (t_index != -1) {
          this.timeScrollId = `time-${t_index}`
          this.time_index = t_index
        }else{
          this.timeScrollId = `time-0`
          this.time_index = ''
        }
      })
    },
    async dateChange (index) {
      this.date_index = index
      await this.getAvailableTimePieces()
      if (this.dates[index] === this.reserve_date) {
        this.timeScrollInit(`time-${this.time_index}`)
      }else{
        this.timeScrollInit()
      }
    },
    timeScrollInit (id = 'time-0') {
      this.timeScrollId = ''
      this.$nextTick(() => {
        this.timeScrollId = id
      })
    },
    timeChange (index) {
      if (!this.timeIsCanReserve(this.times[index])) return
      this.time_index = index
      this.$emit('choosed', { reserve_date: this.dates[this.date_index], reserve_time: this.times[this.time_index]})
      this.handleClose()
    },
    handleClose () {
      this.clearData()
      this.$emit('input', false)
    },
    clearData () {
      this.date_index = 0
      this.time_index = ''
      this.dateScrollId = 'date-0'
      this.timeScrollId = ''
    },
    getReserveTimerange () {
      this.getTimeLoading = true;
      ReserveService.getReserveTimerange().then( res => {
        this.dates = res.dates
        this.times = res.times
        this.getAvailableTimePieces()
        this.init()
      } ).finally(() => {
        this.getTimeLoading = false;
      })
    },

    getAvailableTimePieces () {
      this.getTimeLoading = true;
      let params = {
        reserve_date: this.dates[this.date_index],
        physio_id: this.physio_id
      }
      ReserveService.getAvailableTimePieces(params).then( res => {
        this.available_time_pieces = res.available_time_pieces || []
      } ).finally(() => {
        this.getTimeLoading = false;
      })
    },
  },
};
</script>

<style lang="scss" scoped>
.popup-box {
  width: 1480rpx;
  height: 1040rpx;
  background: #FFFFFF;
  border-radius: 8rpx;
  word-break: break-all;
  .content-head {
    display: flex;
    align-items: center;
    border-bottom: 2rpx solid #EBEDF0;
    padding: 32rpx 40rpx;
    box-sizing: border-box;
    .title {
      flex: 1;
      font-weight: 600;
      font-size: 32rpx;
      color: #333333;
      line-height: 48rpx;
    }
    .close-icon {
      width: 24rpx;
      min-width: 24rpx;
      height: 24rpx;
      margin-left: 20rpx;
    }
  }

  .content {
    // 日期滚动范围
    .date-head {
      box-shadow: 0rpx 6rpx 12rpx 0rpx rgba(20,21,22,0.04);
      padding: 0 60rpx;
      margin-bottom: 30rpx;
      .date-scroll {
        height: 128rpx;
        .date-item-box {
          display: flex;
          align-items: center;
          .date-item {
            min-width: fit-content;
            margin: 28rpx 40rpx;
            position: relative;
            .week-day {
              display: flex;
              justify-content: center;
              align-items: center;
              font-weight: 400;
              font-size: 26rpx;
              color: #333333;
              line-height: 36rpx;
            }
            .date {
              margin-top: 4rpx;
              display: flex;
              justify-content: center;
              align-items: center;
              font-weight: 300;
              font-size: 22rpx;
              color: #333333;
              line-height: 32rpx;
            }
          }
          .date-item--active {
            .week-day, .date {
              color: #155BD4;
            }
            &:after {
              content: ' ';
              width: 112rpx;
              position: absolute;
              bottom: -28rpx;
              left: 50%;
              transform: translateX(-50%);
              height: 4rpx;
              background: #155BD4;
            }
          }
        }
      }
    }

    // 时刻滚动范围
    .time-scroll {
      width: 100%;
      height: 760rpx;
      .time-item-box {
        height: 100%;
        padding: 0rpx 80rpx 0rpx;
        display: flex;
        flex-wrap: wrap;
        .time-item {
          margin-right: 70rpx;
          font-weight: 400;
          font-size: 26rpx;
          color: #333333;
          line-height: 36rpx;
          width: 84rpx;
          min-width: 84rpx;
          height: 84rpx;
          display: flex;
          justify-content: center;
          align-items: center;
          &:nth-child(9n) {
            margin-right: 0rpx;
          }
        }
        .time-item--active {
          color: #155BD4;
          border-radius: 50%;
          background: rgba(21,91,212,0.08);
        }
        .time-item--disabled {
          color: #BBBBBB !important;
        }
      }
    }
  }
}
</style>
