<template>
  <uni-popup ref="popup" type="dialog">
    <view class="confirm-dialog">
      <view class="dialog-header" v-if="title">
        <text class="dialog-title">{{ title }}</text>
        <uni-icons v-if="showClose" type="closeempty" size="16" color="#AAAAAA" @click="handleClose"></uni-icons>
      </view>
      <view class="dialog-content">
        <text class="dialog-message">{{ message }}</text>
      </view>
      <view class="dialog-footer">
        <button class="ui-btn-drawer-base ui-btn-drawer-default flex-1" @click="handleCancel" v-if="showCancel">{{ cancelText }}</button>
        <button class="ui-btn-drawer-base ui-btn-drawer-primary ui-btn-distance-l flex-1" type="primary" @click="handleConfirm">{{ confirmText }}</button>
      </view>
    </view>
  </uni-popup>
</template>

<script>
export default {
  name: "ConfirmDialog",
  props: {
    // 标题
    title: {
      type: String,
      default: '提示'
    },
    // 内容
    message: {
      type: String,
      default: ''
    },
    // 确认按钮文字
    confirmText: {
      type: String,
      default: '确定'
    },
    // 取消按钮文字
    cancelText: {
      type: String,
      default: '取消'
    },
    // 是否显示取消按钮
    showCancel: {
      type: Boolean,
      default: true
    },
    // 是否显示关闭按钮
    showClose: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    // 打开弹窗
    open() {
      this.$refs.popup.open();
    },
    // 关闭弹窗
    close() {
      this.$refs.popup.close();
    },
    // 处理确认
    handleConfirm() {
      this.$emit('confirm');
      this.close();
    },
    // 处理取消
    handleCancel() {
      this.$emit('cancel');
      this.close();
    },
    // 处理关闭
    handleClose() {
      this.$emit('close');
      this.close();
    }
  }
}
</script>

<style lang="scss" scoped>
.confirm-dialog {
  width: 600rpx;
  background: #FFFFFF;
  border-radius: 12rpx;
  overflow: hidden;

  .dialog-header {
    position: relative;
    padding: 32rpx;
    text-align: center;
    border-bottom: 2rpx solid #EBEDF0;

    .dialog-title {
      font-weight: 600;
      font-size: 32rpx;
      color: #333333;
      line-height: 48rpx;
    }

    .uni-icons {
      position: absolute;
      right: 32rpx;
      top: 50%;
      transform: translateY(-50%);
      cursor: pointer;
    }
  }

  .dialog-content {
    padding: 48rpx 32rpx;
    min-height: 120rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    .dialog-message {
      font-size: 28rpx;
      color: #666666;
      line-height: 40rpx;
      text-align: center;
    }
  }

  .dialog-footer {
    padding: 0 32rpx 32rpx;
    display: flex;
    justify-content: center;
  }
}
</style>
