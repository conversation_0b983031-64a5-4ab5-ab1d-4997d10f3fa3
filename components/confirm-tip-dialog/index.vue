<script>

export default {
  name: 'confirm-tip-dialog',
  props: {
    value: <PERSON><PERSON><PERSON>,
    loading: <PERSON><PERSON><PERSON>,
    title: String,
    okText: {
      type: String,
      default: '确定'
    },
    cancelText: {
      type: String,
      default: '取消'
    },
    namespace: String
  },
  data() {
    return {
      okLoading: false,
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(flag) {
        if (flag) {
          this.$nextTick(() => {
            if (typeof this.$refs.cancelReservePop?.open !== 'function') return
            this.$refs.cancelReservePop.open()
          })
          return
        }
        this.$nextTick(() => {
          if (typeof this.$refs.cancelReservePop?.close !== 'function') return
          this.$refs.cancelReservePop.close()
        })
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('input', false)
    },
    confirm() {
      if (this.loading) {
        this.asyncConfirm()
        return
      }
      this.$emit('on-ok', this.namespace)
    },
    async asyncConfirm() {
      this.okLoading = true
      await this.$emit('on-ok', this.namespace)
      this.okLoading = false
    },
    handlePopStatusChange(e) {
      if (!e.show) {
        this.handleClose()
      }
    }
  }
}
</script>

<template>
  <uni-popup ref="cancelReservePop" type="center" @change="handlePopStatusChange">
    <view class="cancel-reserve-container">
      <view class="title">{{ title || '提示' }}</view>
      <view class="btn">
        <button class="ui-btn-drawer-base ui-btn-drawer-default flex-1" @click="handleClose">{{ cancelText }}</button>
        <button
          class="ui-btn-drawer-base ui-btn-drawer-primary ui-btn-distance-l flex-1"
          :loading="okLoading"
          :loading-text="okText"
          @click="confirm"
        >
          {{ okText }}
        </button>
      </view>
    </view>
  </uni-popup>
</template>

<style scoped lang="scss">
.cancel-reserve-container {
  width: fit-content;
  height: fit-content;
  min-height: 300rpx;
  background: #FFFFFF;
  border-radius: 8rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32rpx 40rpx;
  .title {
    height: 48rpx;
    font-weight: 500;
    font-size: 34rpx;
    color: #333333;
    line-height: 48rpx;
    padding-bottom: 48rpx;
    margin: auto;
  }
  .btn {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;
  }
}
</style>
