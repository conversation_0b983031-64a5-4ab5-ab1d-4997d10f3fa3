<template>
  <uni-drawer ref="showRight" mode="right" :mask-click="maskClick" @change="change" :width="width">
    <view class="loadingDom" v-if="loading"></view>
    <view class="drawer-wrapper">
      <view class="drawer-header">
        <slot name="title">
          <view class="pop-title">{{ title }}</view>
        </slot>
        <uni-icons class="delete-icon" type="closeempty" size="16" color="#AAAAAA" @click="closeDrawer"></uni-icons>
      </view>
      <view class="drawer-content">
        <scroll-view :scroll-y="true" :style="{ 'height': footerHide ? 'calc(100vh - 200rpx)' : 'calc(100vh - 368rpx)' } ">
          <slot name="content"></slot>
        </scroll-view>
      </view>
      <view class="drawer-footer" v-if="!footerHide">
        <slot name="footer">
          <view><button class="drawer-footer-btn" type="primary" :loading="loading" @click="confirm">确定</button></view>
        </slot>
      </view>
    </view>
  </uni-drawer>
</template>

<script>
export default {
  name: 'UploadImage',
  components: {},
  mixins: [],
  props: {
    width: {
      type: Number,
      default: 740
    },
    value: {
      type: Boolean,
      default: () => false
    },
    // 弹窗标题
    title: {
      type: String,
      default: '标题',
    },
    // loading,同时唤起蒙层，禁止操作
    loading: {
      type: Boolean,
      default: () =>  false
    },
    // 隐藏底部区域
    footerHide: {
      type: Boolean,
      default: () =>  false
    },
    // 是否允许点击空白关闭抽屉
    maskClick: {
      type: Boolean,
      default: () => false
    },
  },
  data() {
    return {
    };
  },
  computed: {},
  watch: {
    value (val) {
      if (val) {
        this.showDrawer()
      }else{
        this.closeDrawer()
      }
    },
  },
  created() {},
  mounted() {},
  methods: {
    // 确定
    confirm () {
      this.$emit('on-success')
    },
    change (val) {
      this.$emit('change', val)
      if ( !val ){
        this.closeDrawer()
      }
      this.$emit('on-change', val)
    },
    showDrawer() {
      this.$refs.showRight.open();
      this.$emit('on-open')
    },
    closeDrawer() {
      this.$refs.showRight.close();
      this.$emit('input', false)
      this.$emit('on-close')
    }
  },
};
</script>

<style scoped lang="scss">
.drawer-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .drawer-header {
    min-height: 120rpx;
    @include flex($justify: space-between);
    padding: 36rpx 40rpx;
    box-shadow: $ui-draw-shadow-base;

    .pop-title {
      font-weight: 600;
      font-size: 32rpx;
      color: #333333;
      line-height: 48rpx;
      margin-right: 80rpx;
    }

    .delete-icon {
      position: absolute;
      right: 40rpx;
    }
  }

  .drawer-content {
    margin: 36rpx 40rpx;
    flex: 1;
    overflow-y: auto;
  }

  .drawer-footer {
    padding: 40rpx;
    border-top: 2rpx solid rgba(235, 237, 240, 0.8);
    display: flex;
    justify-content: flex-end;
    padding-bottom: 40rpx + constant(safe-area-inset-bottom);
    padding-bottom: 40rpx +env(safe-area-inset-bottom);
    .drawer-footer-btn {
      height: 720rpx;
      line-height: 72rpx;
      align-self: flex-end;
    }
  }
}
.loadingDom {
  height: 100%;
  width: 100%;
  position: absolute;  /* 绝对定位使得元素覆盖在其他内容上 */
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.1);
  z-index: 9999;
}
</style>

