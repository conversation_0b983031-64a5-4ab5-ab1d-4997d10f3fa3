<template>
  <view>
    <cus-drawer v-model="drawerVisible" title="咨询预览" @on-open="onOpen" @on-close="onClose" :loading="confirmLoading">
      <view slot="content" class="content">
        <view class="content-title">商品费</view>
        <uni-table border stripe emptyText="暂无更多数据" >
          <!-- 表头行 -->
          <uni-tr class="header-tr" style="position: sticky;top: 48rpx;background: #fff;">
            <uni-th v-for="(head_item, head_index) in columns" :width="head_item.width" :key="head_index" :align="head_item.align || 'center'">{{ head_item.label }}</uni-th>
          </uni-tr>
          <!-- 表格数据行 -->
          <uni-tr v-for="(item, index) in goodsList" :key="index">
            <uni-td>
              <view class="goods-info">
                <image @click="preview(item.row.main_img)" class="goods-img" :src="item.main_img + '-B.200'"/>
                <view class="goods-info-box">
                  <view class="goods-name">{{ item.name }}</view>
                  <view class="goods-spec" v-if="item.goods_type === '10'"> {{ getAttrsName(item.attrs, item.attr_id) }}</view>
                </view>
              </view>
            </uni-td>
            <uni-td>{{ item.goods_type_text }}</uni-td>
            <uni-td>¥{{ getPrice(item) }}</uni-td>
            <uni-td>{{ item.quantity }}</uni-td>
            <uni-td>
              <view v-if="['15', '20', '25', '30'].includes(item.goods_type)" class="goods-price">¥{{getRowPrice(item)}}</view>
              <view v-else-if="item.attr_id && ['10', '40'].includes(item.goods_type)"
                    class="goods-price" >¥{{getRowPrice(item)}}</view>
            </uni-td>
          </uni-tr>
        </uni-table>
      </view>

      <view slot="footer" class="footer">
        <view class="moeny-box">
          <view class="goods-num">共{{ goodsList.length}}件，</view>
          <view class="money-label">合计：</view>
          <view class="money">¥ {{getTotalPrice}}</view>
        </view>
        <view class="btn-group">
          <button class="ui-btn-drawer-base ui-btn-drawer-default" type="default"  @click="closeDrawer">取消</button>
          <button class="ui-btn-drawer-base ui-btn-drawer-primary ui-btn-distance-l" type="primary" :loading="confirmLoading" @click="confirm">确诊无误，结束咨询</button>
        </view>
      </view>
    </cus-drawer>
  </view>
</template>

<script>
import cusDrawer from '@/components/cus-drawer/index.vue'
import {$operator} from "@/utils/operation";
import ConsultationService from "@/api/consultation";

export default {
  name: 'finish-consult',
  components: {
    cusDrawer,
  },
  mixins: [],
  props: {
    value: {
      type: Boolean,
      default: () => false
    },
    goodsList: {
      type: Array,
      default: () => []
    },
    mr_id: {
      type: String,
      default: ''
    },
    record_info: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      confirmLoading: false,
      drawerVisible: false,
      columns: [
        { label: '商品', key: 'info', align: 'left', width: 240 },
        { label: '类型', key: 'goods_type_text', align: 'left', width: 60 },
        { label: '单价', key: 'price', align: 'left', width: 60 },
        { label: '数量', key: 'num', align: 'left', width: 60 },
        { label: '总价', key: 'total', align: 'left', width: 60 },
      ],
    };
  },
  computed: {
    getAttrsName() {
      return (list, id) => {
        return list?.[id]?.spec || '';
      };
    },
    getTotalPrice() {
      let total = 0;
      if (this.goodsList.length) {
        total = this.goodsList.reduce((acc, cur) => {
          let price = 0;
          if (['10', '40'].includes(cur.goods_type)) {
            price = +cur.attrs?.[cur.attr_id]?.price || 0;
          } else {
            price = +cur.price || 0;
          }
          return $operator.add(acc, $operator.multiply(cur.quantity || 0, price || 0) || 0);
        }, 0);
      }
      return $operator.toPrecision(total, 2).toFixed(2);
    },
    getRowPrice() {
      return item => {
        let price = 0;
        if (['10', '40'].includes(item.goods_type)) {
          if (!item.attr_id) return '-';
          price = item.attrs[item.attr_id]?.price;
        } else {
          price = item?.price;
        }
        if (!price && +price !== 0) return '-';
        return $operator.multiply(item.quantity || 0, price || 0).toFixed(2);
      };
    },
    getPrice() {
      return item => {
        let price = 0;
        if (['10', '40'].includes(item.goods_type)) {
          if (!item.attr_id) return '-';
          price = item.attrs[item.attr_id]?.price;
        } else {
          price = item?.price;
        }
        if (!price && +price !== 0) return '-';
        return Number(price || 0).toFixed(2);
      };
    },
  },
  watch: {
    value (val) {
      if ( val ) {
        this.drawerVisible = true
      }else{
        this.drawerVisible = false
      }
    },
  },
  created() {},
  mounted() {},
  methods: {
    preview (url) {
      uni.previewImage({
        urls: [url],
        current: 0
      })
    },
    finish () {
      let params = {
        mr_id: this.mr_id,
        ...this.record_info,
        listof: this.handleListof()
      }
      this.confirmLoading = true
      ConsultationService.getPresSavefinish(params).then( res => {
        this.$emit('success', res.order_id)
        this.confirmLoading = false
        this.closeDrawer()
      } ).finally(() => this.confirmLoading = false)
    },
    // 10实物 15虚拟 20套餐  25通兑券
    handleListof () {
      let listof = {};
      this.goodsList.forEach(item => {
        if (['10', '40'].includes(item.goods_type)) {
          listof = { ...listof, ...{ [item.attr_id]: item.quantity } };
        } else {
          listof = { ...listof, ...{ [item.attr_id]: item.quantity } };
        }
      });
      return listof
    },
    confirm () {
      this.finish()
    },
    onOpen () {
    },
    onClose () {
      this.closeDrawer()
    },
    closeDrawer () {
      this.$emit('input', false)
      this.clearData()
    },
    clearData () {},
  },
};
</script>

<style lang="scss" scoped>
.content {
  .content-title {
    font-weight: 600;
    font-size: 32rpx;
    color: #333333;
    line-height: 48rpx;
    text-align: left;
    position: sticky;
    top: 0rpx;
    background: #fff;
    z-index: 2;
  }
  .table-box {
    position: relative;
  }
}
.footer {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .moeny-box {
    display: flex;
    align-items: baseline;
    .goods-num {
      font-weight: 400;
      font-size: 26rpx;
      color: #999999;
      line-height: 40rpx;
    }
    .money-label {
      font-weight: 400;
      font-size: 26rpx;
      color: #333;
      line-height: 40rpx;
    }
    .money {
      font-weight: 600;
      font-size: 28rpx;
      color: #EE3838;
      line-height: 40rpx;
    }
  }
  .btn-group {
    display: flex;
  }
}
.ml20 {
  margin-left: 20rpx;
}
::v-deep .uni-table {
  th,td {
    border: none;
  }
  .header-tr {
    z-index: 2;
    background: #fff;
    box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(20,21,22,0.06);
  }
}
::v-deep .uni-table-scroll {
  overflow-x: unset;
  border: none !important;
  border-bottom: 2rpx solid #EBEDF0;
}

.goods-info {
  display: flex;
  align-items: center;
  .goods-img {
    width: 80rpx;
    min-width: 80rpx;
    height: 80rpx;
    margin-right: 16rpx;
    border-radius: 8rpx;
  }
  .goods-info-box {
    .goods-name {
      font-weight: 400;
      font-size: 28rpx;
      color: #333333;
      line-height: 40rpx;
    }
    .goods-spec {
      margin-top: 8rpx;
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      line-height: 32rpx;
    }
  }
}
</style>
