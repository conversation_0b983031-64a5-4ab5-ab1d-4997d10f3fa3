<template>
  <view>
    <uni-popup ref="popup" type="center" :is-mask-click="false">
      <view class="popup-box">
        <view class="head-box">
          <view class="title">诊疗安全及隐私告知确认书</view>
          <image class="close-icon" src="https://static.rsjxx.com/image/2025/0624/201128_58237.png" mode="aspectFit" @click="handleClose"/>
        </view>
        <view class="content-box">
          <scroll-view class="scroll-box" scroll-y>
            <view>尊敬的用户:</view>
            <view>为保障诊疗安全并依法保护您的个人信息，根据《中华人民共和国个人信息保护法》《中华人民共和国民法典》等法律法规，请确认以下内容:</view>
            <view>一、诊疗安全告知</view>
            <view>以下情况超出本机构诊疗范围，若有相关病史或者以下情况请主动告知工作人员:</view>
            <view>病史:</view>
            <view class="weight">严重心脑血管疾病（高血压Ⅲ期、严重心脏病、心脏术后等）；糖尿病伴严重并发症、慢性肾衰 ；传染性疾病、不明原因高热 / 消瘦；精神类疾病（癫痫、精神分裂症等）；
              妊娠期 ；重大术后 2 个月内、体内植入器械（钢板 / 起搏器等）；严重皮肤病、静脉曲张、骨质疏松；急性炎症期;</view>
            <view>情况:</view>
            <view class="weight">酒醉、过饱 / 过饥、经期、大出血后、过敏发作期、情绪剧烈波动等。</view>
            <view>二、隐私与信息管理</view>
            <view>1. 仅收集诊疗必需的病历、健康信息，用于制定诊疗方案及履行法定义务。</view>
            <view>2. 严格遵循 " 合法、正当、必要 " 原则，未经授权不超范围使用，可能通过电话或短信发送复
              诊提醒等必要医疗服务信息（非商业性质） </view>
            <view>3. 您的信息将加密存储，未经同意不向第三方提供（法律法规要求除外）。</view>
            <view>三、法律责任声明</view>
            <view>1. 隐瞒病史或虚假陈述导致不良后果，由本人承担全部责任。</view>
            <view>2. 您有权查阅、更正个人信息，具体流程请咨询前台导诊。</view>

          </scroll-view>
          <view class="info-box">
            <view class="info-item">
              <view class="item-label">姓名：</view>
              <view class="item-value" style="width: 270rpx; height: 80rpx;text-align: center;">
                <image v-if="sign_url" style="width: 100%; height: 100%" :src="sign_url" mode="heightFix" />
              </view>
            </view>

            <view class="info-item">
              <view class="item-label">时间：</view>
              <view class="item-value">{{ sign_time }}</view>
            </view>
          </view>

          <view class="btn-box" v-if="!disabled">
            <template class="flex flex-items-center" v-if="sign_url">
              <button class="ui-btn-drawer-base ui-btn-drawer-default" style="width: 320rpx;" type="default" @click="reset">重新签署</button>
              <button class="ui-btn-drawer-base ui-btn-drawer-primary ui-btn-distance-l" style="width: 320rpx;" type="primary" @click="handleClose">已完成签署</button>
            </template>
            <button class="ui-btn-drawer-base ui-btn-drawer-primary" style="width: 320rpx;" type="primary" @click="confirm" v-else>签署确认书</button>
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 签名 -->
    <sign v-model="signVisible" ref="signRef" @getSign="getSign"></sign>
  </view>
</template>

<script>
import sign from './sign.vue'
import dayjs from "dayjs";
export default {
  name: 'agreement',
  props: {
    value: {
      type: Boolean,
      default: () => false
    },
    disabled: {
      type: Boolean,
      default: () => false
    },
    url: {
      type: String,
      default: () => ''
    },
    time: {
      type: String,
      default: () => ''
    },
  },
  components: {
    sign
  },
  mixins: [],
  data() {
    return {
      signVisible: false,
      sign_url: '' , // 签名
      sign_time: '',
    };
  },
  computed: {},
  watch: {
    value (val) {
      if (val) {
        this.$refs.popup.open()
        this.sign_url = this.url
        this.sign_time = this.time
      }else{
        this.$refs.popup.close()
      }
    }
  },
  created() {},
  mounted() {},
  methods: {
    getSign (url) {
      this.sign_url = url
      this.sign_time = dayjs().format('YYYY-MM-DD HH:mm:ss')
      this.$emit('getSign', { sign_url: this.sign_url, sign_time: this.sign_time })
    },
    reset () {
      this.signVisible = true
      this.$refs?.signRef?.reset()
    },
    confirm () {
      this.signVisible = true
    },
    handleClose () {
      this.clearData()
      this.$emit('input', false)
    },
    clearData () {
    },
  },
};
</script>

<style lang="scss" scoped>
.popup-box {
  width: 1600rpx;
  border-radius: 8rpx;
  background: #FFFFFF;
  .head-box {
    padding: 32rpx 40rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #EBEDF0;
    .title {
      font-weight: 600;
      font-size: 32rpx;
      color: #333333;
      line-height: 48rpx;
    }
    .close-icon {
      width: 24rpx;
      min-width: 24rpx;
      height: 24rpx;
    }
  }
  .content-box {
    height: 1060rpx;
    padding: 32rpx 40rpx;

    .scroll-box {
      height: 760rpx;
      view {
        font-size: 26rpx;
        color: #444;
        line-height: 40rpx;
        text-align: justify;
      }
      .weight {
        font-weight: bold;
        color: #333;
      }
    }

    .info-box {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      .info-item {
        margin-bottom: 16rpx;
        display: flex;
        align-items: center;
        &:last-child {
          margin-bottom: 0rpx;
        }
        .item-label {
          width: 100rpx;
          min-width: fit-content;
          font-weight: 400;
          font-size: 26rpx;
          color: #333333;
          line-height: 40rpx;
          text-align: right;
        }
        .item-value {
          width: 270rpx;
          font-weight: 400;
          font-size: 26rpx;
          color: #333333;
          line-height: 40rpx;
        }
      }
    }

    .btn-box {
      margin-top: 12rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.ml24 {
  margin-left: 24rpx !important;
}
</style>
