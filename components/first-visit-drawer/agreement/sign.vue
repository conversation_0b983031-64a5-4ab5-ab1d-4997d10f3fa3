<template>
  <uni-popup ref="popup" type="center" :is-mask-click="false">
    <view class="popup-box">
      <view class="head-box">
        <view class="title">客户签字</view>
        <image class="close-icon" src="https://static.rsjxx.com/image/2025/0624/201128_58237.png" mode="aspectFit" @click="handleClose"/>
      </view>
      <view class="content-box">
        <view style="height: 800rpx">
          <spSignBoard
            ref="sign-panel"
            sid="protocolPopupSign"
            :horizontal="true"
            :showMark="false"
          />
        </view>

        <view class="btn-box">
          <button class="ui-btn-drawer-base ui-btn-drawer-default" style="width: 320rpx;" type="default" @click="reset">重新签署</button>
          <button class="ui-btn-drawer-base ui-btn-drawer-primary ui-btn-distance-l" style="width: 320rpx;" type="primary" @click="confirm">完成签字</button>
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<script>
import spSignBoard from '@/components/sp-sign-board/sp-sign-board.vue'
export default {
  name: 'sign',
  props: {
    value: {
      type: Boolean,
      default: () => false
    },
  },
  components: {
    spSignBoard
  },
  mixins: [],
  data() {
    return {
      isSign: false,
    };
  },
  computed: {},
  watch: {
    value (val) {
      if (val) {
        this.$refs.popup.open()
      }else{
        this.$refs.popup.close()
      }
    }
  },
  created() {},
  mounted() {},
  methods: {
    reset () {
      this.$refs["sign-panel"]?.reset()
    },
    confirm () {
      this.$refs["sign-panel"].confirm(data => {
        if (data) {
          this.$emit('getSign', data.base64 )
          this.handleClose()
        }
      })
    },
    handleClose () {
      this.clearData()
      this.$emit('input', false)
    },
    clearData () {
    },
  },
};
</script>

<style lang="scss" scoped>
.popup-box {
  width: 1600rpx;
  border-radius: 8rpx;
  background: #FFFFFF;
  .head-box {
    padding: 32rpx 40rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #EBEDF0;
    .title {
      font-weight: 600;
      font-size: 32rpx;
      color: #333333;
      line-height: 48rpx;
    }
    .close-icon {
      width: 24rpx;
      min-width: 24rpx;
      height: 24rpx;
    }
  }
  .content-box {
    height: 1060rpx;
    padding: 32rpx 40rpx;

    .btn-box {
      margin-top: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
