<template>
  <view>
    <cus-drawer v-model="drawerVisible" :title="title" @on-open="onOpen" @on-close="onClose">
      <view slot="title" class="title">{{ title }}</view>

      <view slot="content" class="content">
        <uni-forms>
          <view class="item-box">
            <view class="item-title" style="margin-top: 0rpx;">如何得知榕树家中医诊所？</view>
            <uni-forms-item>
              <view class="item-content">
                <radio-group class="source-group-box" @change="sourceChange">
                  <label class="group-box-label" v-for="(item, key) in source" :key="'source'+key">
                    <radio :disabled="isDisabled" :value="key" :checked="key === source_checked" style="transform:scale(0.7)" />
                    <view :style="{ 'color': key === source_checked && !isDisabled ? '#155BD4' : '#333' }">{{ item.desc }}</view>
                  </label>

                </radio-group>
              </view>
            </uni-forms-item>
          </view>

          <view class="item-box" v-for="item in recordItems" :key="item.propKey">
            <template v-if="item.isSpecialMark">
              <view class="item-title">身体疼痛</view>
              <view class="item-content">
                <uni-forms-item>
                  <view class="pain-tip">点击灰色背景区域，标记身体疼痛部位</view>
                  <view class="edit-img-box">
                    <image v-if="canvasPath && !isDisabled" class="edit-img" :src="canvasPath" @click="editImg"/>
                    <image v-else class="edit-img" :src="bgUrl" @click="editImg"/>
                  </view>
                </uni-forms-item>
              </view>
            </template>
            <template v-else>
              <view class="item-title">
                <view>{{ item.label }}</view>
                <view v-if="item.required" style="color: red;">*</view>
              </view>
              <view class="item-content">
                <uni-forms-item >
                  <uni-easyinput :disabled="isDisabled" v-model="patientRecord[item.propKey]" type="textarea" auto-height v-if="item.isTextarea"
                                 class="record-textarea" :placeholder="item.placeholder"></uni-easyinput>
                  <view :class="['cus-input', isDisabled ? 'cus-input--disabled' : '']" @click="recordItemHandler(item)" v-else>
                    <text class="input-value" v-if="patientRecord[item.propKey]">
                      {{ patientRecord[item.propKey] }}
                    </text>
                    <text class="cus-input-placeholder" v-else>{{ item.placeholder }}</text>
                  </view>
                </uni-forms-item>
              </view>
            </template>
          </view>

          <!-- 1:男 2:女 -->
          <view class="item-box" v-if="row && row.sex === '2'">
            <view class="item-title">女性生理期</view>
            <uni-forms-item>
              <view class="item-content">
                <view class="inner-box-item">
                  <view class="inner-box-label">是否停经：</view>
                  <view class="inner-box-value">
                    <radio-group class="period-box" @change="periodChange">
                      <label class="period-item" v-for="(item, key) in menstrual" :key="'period'+key">
                        <radio :disabled="isDisabled" :value="key" :checked="key === period" style="transform:scale(0.7)" />
                        <view :style="{ 'color': key === period && !isDisabled ? '#155BD4' : '#333' }">{{item.desc}}</view>
                      </label>
                    </radio-group>
                  </view>
                </view>

                <view class="inner-box-item" v-if="period === '2'">
                  <view class="inner-box-label">停经岁数：</view>
                  <view class="inner-box-value">
                    <uni-easyinput :disabled="isDisabled" :maxlength="3" type="number" done trim class="period-input" :clearable="false" v-model="period_age" placeholder="请输入" @input="ageChange"></uni-easyinput>
                    <view class="period_suffix">岁</view>
                  </view>
                </view>
              </view>
            </uni-forms-item>
          </view>
        </uni-forms>
        <c-loading :show="visitLoading" />
      </view>

      <view slot="footer" class="footer">
        <view class="check-box" @click="agreementAction">
          <image class="check-icon" :src="sign_url ? checked : unChecked" mode="aspectFill" />
          <span class="label">我已阅读并同意</span>
          <a class="agreement">《诊疗安全及隐私告知确认书》</a>
        </view>
        <view class="flex">
          <button class="ui-btn-drawer-base ui-btn-drawer-default" type="default"  @click="closeDrawer">取消</button>
          <button v-if="!isDisabled" class="ui-btn-drawer-base ui-btn-drawer-primary ui-btn-distance-l" type="primary" :loading="confirmLoading" @click="confirm">确认登记</button>
        </view>
      </view>
    </cus-drawer>
    <!-- 标记疼痛 -->
    <mark-pain-modal v-model="markVisible" :draw_path="draw_path" @getCanvasPath="getCanvasPath"></mark-pain-modal>
    <!-- 签署确认书 -->
    <agreement :disabled="isDisabled" ref="agreement" v-model="agreementVisible" @getSign="getSign" :url="sign_url" :time="sign_time"></agreement>
    <record-input-drawer v-model="inputDrawerVisible" :form-data="patientRecord" :record-item="editRecordItem" source="2"
                         @saveRecordItem="saveRecordItem"></record-input-drawer>
  </view>
</template>

<script>
import ConsultationService from "@/api/consultation";
import utils from "@/utils/utils";
import cusDrawer from '@/components/cus-drawer/index.vue'
import RecordInputDrawer from "@/pages/ai-consult/components/user-profile/components/RecordInputDrawer.vue";
import markPainModal from './mark/markPainModal.vue'
import agreement from './agreement/agreement.vue'
import S from "@/utils/utils";
import UserService from "@/api/user";
import { base64ToPath } from 'image-tools'
import CLoading from "../cLoading/loading.vue";

export default {
  name: 'index',
  components: {
    CLoading,
    cusDrawer,
    markPainModal,
    agreement,
    RecordInputDrawer
  },
  mixins: [],
  props: {
    value: {
      type: Boolean,
      default: () => false
    },
    title: {
      type: String,
      default: '榕树家初诊信息登记表'
    },
    row: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      drawerVisible: false,
      confirmLoading: false,
      visitLoading: false,
      checked: 'https://static.rsjxx.com/image/2025/0623/160027_85062.png',
      unChecked: 'https://static.rsjxx.com/image/2025/0623/160027_5916.png',
      source_checked: '', // 来源
      source: [], // 来源
      menstrual: {}, // 经期
      period: '', // 是否停经
      period_age: null, // 停经岁数

      // 标记疼痛
      default_bg: 'https://static.rsjxx.com/image/2025/0625/111507_90251.png',
      bgUrl: 'https://static.rsjxx.com/image/2025/0625/111507_90251.png',
      isEditImg: false,
      markVisible: false,
      canvasPath: '', // canvas标记后的图
      draw_path: '', // canvas绘图层的图
      agreementVisible: false, // 签署确认书弹窗
      sign_url: '',
      sign_time: '',

      recordItems: [
        {
          propKey: 'engrave_text',
          label: '主诉（今天看诊的主要症状）',
          placeholder: '请输入主诉',
          required: true,
          showDayList: true,
          componentName: 'ChiefComplaint'
        },
        {
          propKey: 'diagnostic_methods',
          label: '其他症状',
          placeholder: '请输入其他症状',
          componentName: 'OtherSymptoms'
        },
        {
          propKey: 'mark',
          isSpecialMark: true,
          label: '身体疼痛',
          placeholder: '',
          componentName: ''
        },
        {
          propKey: 'present_medical_history',
          label: '现病史',
          placeholder: '请输入现病史',
          componentName: 'MedicalHistory',
          isTextarea: true
        },
        {
          propKey: 'past',
          label: '既往史',
          placeholder: '请输入既往史',
          componentName: 'PastHistory'
        },
        {
          propKey: 'physique',
          label: '体格检查',
          placeholder: '请输入体格检查',
          componentName: 'PhysicalExamination'
        }
      ],
      patientRecord: {
        present_medical_history: '', //现病史
        engrave_text: '', //主诉
        diagnostic_methods: '', //望闻问切
        past: '', //既往史
        physique: '', // 体格检查
      },
      editRecordItem: {
        propKey: '',
        label: '',
        placeholder: '',
        required: false,
        showDayList: false
      },
      inputDrawerVisible: false,
    };
  },
  computed: {
    isDisabled () {
      return this.row?.first_visit_status === '2'
    }
  },
  watch: {
    value (val) {
      if ( val ) {
        this.drawerVisible = true
        this.init()
      }else{
        this.drawerVisible = false
      }
    },
  },
  created() {},
  mounted() {},
  methods: {
    init () {
      this.getOptions().then( () => {
        if (this.row?.first_visit_status === '2') {
          this.getFirstvisitdetail()
        }
      } )
    },
    getSign ({ sign_url, sign_time }) {
      this.sign_url = sign_url
      this.sign_time = sign_time
    },
    sourceChange (val) {
      this.source_checked = val.detail.value
    },
    ageChange (val) {
      this.period_age = val
    },
    periodChange (e) {
      this.period = e.detail.value
      if (this.period !== '2') {
        this.period_age = null
      }
    },
    saveRecordItem(value, propKey) {
      this.patientRecord[propKey] = value;
    },
    //   选择主诉
    recordItemHandler(item) {
      if (this.isDisabled) return;
      this.editRecordItem = {
        ...item,
        value: this.patientRecord[item.propKey]
      }
      this.inputDrawerVisible = true;
    },
    agreementAction () {
      this.agreementVisible = true
    },
    async getCanvasPath (path, draw_path) {
      this.canvasPath = path
      this.draw_path = draw_path
    },
    editImg () {
      if (this.isDisabled) {
        this.previewImage(this.bgUrl)
      }else{
        this.markVisible = true
      }
    },
    previewImage(imgUrl) {
      if (!imgUrl) return
      uni.previewImage({
        urls: [imgUrl]
      })
    },
    validData () {
      if (!this.patientRecord.engrave_text) {
        uni.showToast({
          title: '请填写主诉',
          icon: 'none'
        })
        return false
      }

      if (!this.sign_url) {
        uni.showToast({
          title: '请先签署确认书',
          icon: 'none'
        })
        return false
      }
      return true
    },
    confirm () {
      if (this.validData() && !this.confirmLoading) {
        this.save()
      }
    },
    onOpen () {
    },
    onClose () {
      this.closeDrawer()
    },
    closeDrawer () {
      this.clearData()
      this.$emit('input', false)
    },
    clearData () {
      this.confirmLoading = false
      this.source_checked = []
      this.canvasPath = ''
      this.draw_path = ''
      this.period = ''
      this.period_age = null
      this.patientRecord = {
        present_medical_history: '', //现病史
        engrave_text: '', //主诉
        diagnostic_methods: '', //望闻问切
        past: '', //既往史
        physique: '', // 体格检查
      }
      this.sign_url = ''
      this.sign_time = ''
      this.bgUrl = this.default_bg
    },

    base64ToQiniu (base64) {
      return new Promise((resolve, reject) => {
        this.getToken().then(async (res) => {
          const result = await this.uploadFilePromise({url: base64, token: res.token, key: res.key});
          if ( result.key ) {
            let url =  `${res.domain}/${result.key}`
            resolve(url)
          }else{
            uni.showToast({
              title: '图片上传失败',
              icon: 'none'
            })
            reject()
          }
        })
      })
    },

    getToken() {
      return new Promise((resolve, reject) => {
        let params = {
          ext: 'png',
          random: utils.random(6)
        }
        ConsultationService.getUpToken(params).then(res => {
          resolve(res)
        })
      })
    },

    uploadFilePromise({url, token, key}) {
      return new Promise(async (resolve, reject) => {
        let local_path = await base64ToPath(url)
        uni.uploadFile({
          url: "https://upload.qiniup.com",
          filePath: local_path,
          name: "file",
          formData: {
            token,
            key,
          },
          success: (res) => {
            setTimeout(() => {
              resolve(JSON.parse(res.data || '{}'));
            }, 0);
          },
        });
      });
    },

    getOptions () {
      return new Promise(resolve => {
        UserService.getRecordFirstvisitoption().then( res => {
          this.source = res.source || {}
          this.menstrual = res.menstrual || {}
          resolve()
        } )
      })
    },

    async save () {
      try {
        this.confirmLoading = true
        let mark_url = this.canvasPath ? await this.base64ToQiniu(this.canvasPath) : ''
        let sign_url = await this.base64ToQiniu(this.sign_url)
        let params = {
          is_agree: 1,
          uid: this.row?.uid,
          source: this.source_checked,
          ...this.patientRecord,
          additional_images: mark_url ? [mark_url] : [],
          patient_sign_img: sign_url,
          patient_sign_time: this.sign_time,
          menstrual_period: {
            menstrual_option: this.row?.sex === '2' ? this.period : '',
            menstrual_stop_age: this.row?.sex === '2' ? this.period_age : null,
          }
        }
        UserService.Savefirstvisitrecord(params).then( res => {
          this.$emit('success')
          this.closeDrawer()
        } ).finally( () => this.confirmLoading = false)
      }catch (e) {
        this.confirmLoading = false
      }
    },

    getFirstvisitdetail () {
      this.visitLoading = true
      let params = {
        uid: this.row.uid,
      }
      UserService.getFirstvisitdetail(params).then( res => {
        let detail = res.detail || {}
        let medical_data = res.detail?.medical_data || {}
        this.source_checked = medical_data.source
        this.patientRecord = {
          present_medical_history: medical_data.present_medical_history, //现病史
          engrave_text: medical_data.engrave_text, //主诉
          diagnostic_methods: medical_data.diagnostic_methods, //望闻问切
          past: medical_data.past, //既往史
          physique: medical_data.physique, // 体格检查
        }
        // 标记疼痛
        let url = medical_data?.additional_images[0]
        if (url) {
          this.bgUrl = url
        }
        // 签署确认书
        this.sign_url = medical_data?.patient_sign_img || ''
        this.sign_time = medical_data?.patient_sign_time
        // 经期
        let menstrual_period = res.detail?.medical_data?.menstrual_period || {}
        this.period = menstrual_period.menstrual_option
        let age = Number(menstrual_period.menstrual_stop_age || 0)
        this.period_age = age ? age : null
      } ).finally(() => {
        this.visitLoading = false
      })
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .drawer-header {
  box-shadow: none !important;
  padding-bottom: 0rpx !important;
}
.title {
  width: 100%;
  font-weight: bold;
  font-size: 40rpx;
  color: #333333;
  line-height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.content {
  padding-bottom: 80rpx;
  .item-box {
    .item-title {
      font-weight: 600;
      font-size: 30rpx;
      color: #333333;
      line-height: 44rpx;
      display: flex;
      align-items: center;
    }
    .item-content {
      margin-top: 20rpx;
    }
    .pain-tip {
      margin-top: -12rpx;
      font-weight: 400;
      font-size: 24rpx;
      color: #FFAA00;
      line-height: 36rpx;
    }
    .edit-img-box {
      display: flex;
      justify-content: center;
      .edit-img {
        margin-top: 24rpx;
        width: 980rpx;
        height: 462rpx;
      }
    }
  }

  .source-group-box {
    margin-top: 32rpx;
    display: flex;
    align-items: center;
    .group-box-label {
      margin-left: 48rpx;
      display: flex;
      align-items: center;

      font-weight: 400;
      font-size: 28rpx;
      color: #333333;
      line-height: 40rpx;
    }
  }

  .inner-box-item {
    display: flex;
    align-items: center;
    margin-left: 24rpx;
    margin-top: 0rpx;
    margin-bottom: 8rpx;
    &:first-child {
      margin-top: 0rpx;
    }
    .inner-box-label, .inner-box-value {
      font-weight: 400;
      font-size: 28rpx;
      color: #333333;
      line-height: 40rpx;
    }
    .inner-box-value {
      display: flex;
      align-items: center;
    }
    .period-box {
      display: flex;
      .period-item {
        &:first-child{
          margin-left: 0rpx;
        }
        margin-left: 20rpx;
        display: flex;
        align-items: center;
      }
    }
    ::v-deep .period-input {
      .is-input-border {
        border: none !important;
        border-bottom: 1px solid #EBEDF0 !important;
      }
      .is-disabled {
        background: transparent !important;
      }
    }
  }
}

.footer {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .check-box {
    display: flex;
    align-items: center;
    .check-icon {
      width: 28rpx;
      min-width: 28rpx;
      height: 28rpx;
    }
    .label, .agreement {
      font-weight: 400;
      font-size: 22rpx;
      color: #666666;
      line-height: 32rpx;
    }
    .label {
      margin-left: 12rpx;
    }
    .agreement {
      color: #155BD4;
    }
  }
}
.ml20 {
  margin-left: 40rpx;
}

::v-deep .record-textarea {
  .is-disabled {
    background-color: #F5F6F8 !important;
    color: rgb(213, 213, 213);

    .uni-textarea-placeholder {
      color: rgb(213, 213, 213);
      line-height: 44rpx;
    }
  }
}

.cus-input {
  display: flex;
  align-items: center;
  padding: 10rpx 24rpx;
  border: 1px solid #E5E5E5;
  border-radius: 8rpx;
  font-size: 26rpx;
  line-height: 44rpx;

  &:not(.cus-input--disabled):active {
    border-color: #115bd4;
    background: #F5F6F8;
  }

  .cus-input-placeholder {
    color: #999999;
  }

  .input-value {
    color: #333333;
  }

  &--disabled {
    background: #F5F6F8;

    .input-value,
    .cus-input-placeholder {
      color: rgb(213, 213, 213)
    }
  }
}
</style>
