<template>
  <view class="canvas-wrapper">
    <view class="canvas-head">
      <view class="tip">请在身体感到疼痛的区域，进行涂抹标记</view>
      <view class="panel">
        <view class="panel-item" :class="{ 'panel-item--active': type === 'pen' }" @click="changeType('pen')">
          <image class="pen-img" mode="aspectFit"e v-if="type === 'pen'" src="@/static/image/canvas/activePen.png"/>
          <image class="pen-img" mode="aspectFit" v-else src="@/static/image/canvas/pen.png"/>
        </view>
        <view class="panel-item" :class="{ 'panel-item--active': type === 'rubber' }" @click="changeType('rubber')">
          <image class="rubber-img" mode="aspectFit" v-if="type === 'rubber'" src="@/static/image/canvas/activeRubber.png"/>
          <image class="rubber-img" mode="aspectFit" v-else src="@/static/image/canvas/rubber.png"/>
        </view>
      </view>
    </view>
    <view class="canvas-box">
      <!-- canvas底图 canvas-id: 用于画布标识，canvas_id_bg: 用于节点信息获取 -->
      <canvas class="canvas" canvas-id="canvas_id_bg" id="canvas_id_bg"/>
      <!-- canvas回显层 -->
      <canvas class="canvas" canvas-id="canvas_id_hx" id="canvas_id_hx"/>
      <!-- canvas保存层 -->
      <canvas class="canvas" canvas-id="canvas_id_save" id="canvas_id_save"/>
      <!-- canvas绘图层 -->
      <canvas
        class="canvas draw-canvas"
        canvas-id="canvas_id_draw"
        id="canvas_id_draw"
        @touchstart="touchstart"
        @touchmove="touchmove"
        @touchend="touchend"
        @touchcancel="touchend" />
    </view>

    <view class="footer-btn">
      <button class="ui-btn-drawer-base ui-btn-drawer-default" style="width: 320rpx;" type="default" @click="resetCanvas">重置标记区域</button>
      <button class="ui-btn-drawer-base ui-btn-drawer-primary ui-btn-distance-l" style="width: 320rpx;" type="primary" @click="confirm">提交</button>
    </view>
  </view>
</template>

<script>
import { pathToBase64 } from 'image-tools'

function preventScroll(e) {
  e.preventDefault(); // 阻止触摸滚动
};
export default {
  name: 'chj-imgEdit',
  components: {},
  mixins: [],
  props: {
    draw_path: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      path: '', // 底图图片
      bg_ctx: null,
      draw_ctx: null,
      hx_ctx: null,
      save_ctx: null,
      canvas_x: 700,
      canvas_y: 332,
      pen_size: 20,
      pen_color: 'red',
      type: 'pen'
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    // 保存
    async confirm () {
      const bg_path = await this.canvasGetImagePath('canvas_id_bg')
      await this.drawImg(this.save_ctx, bg_path)
      const draw_path = await this.canvasGetImagePath('canvas_id_draw')
      await this.drawImg(this.save_ctx, draw_path)
      let tempFilePath = await this.canvasGetImagePath('canvas_id_save');
      let baes64 = await pathToBase64(tempFilePath)
      this.$emit('getCanvasPath',baes64 , draw_path)
    },
    drawImg (ctx,path) {
      return new Promise(resolve => {
        ctx.drawImage(path,0,0, this.canvas_x, this.canvas_y)
        ctx.draw(true, () => {
          resolve()
        })
      })
    },
    canvasGetImagePath (canvasId) {
      return new Promise((res, rej) => {
        uni.canvasToTempFilePath({
          canvasId,
          success: function(data) {
            if (!data.tempFilePath) {
              uni.showModal({
                title: '提示',
                content: '获取图片失败',
                showCancel: false
              })
              return
            }
            // 在H5平台下，tempFilePath 为 base64
            res(data.tempFilePath);
          },
          fail: (r) => {
            rej(r)
          }
        },this);
      })
    },
    changeType (type) {
      this.type = type
      this.init()
      // 切换橡皮或者笔
      if(this.type == 'rubber'){
        this.draw_ctx.globalCompositeOperation = 'destination-out';
      }else{
        this.draw_ctx.globalCompositeOperation = 'source-over';
      }
    },
    /**
     * @desc 初始化配置
     * */
    open (options) {
      // 设置底图
      this.path = options.path

      // 初始化canvas
      this.$nextTick( () => {
        this.initCanvas()
      } )
    },

    /**
     * @desc 清除canvas
     * */
    resetCanvas () {
      this.draw_ctx.draw()
      this.draw_ctx.clearRect(0,0,this.canvas_x,this.canvas_y)
      this.hx_ctx.clearRect(0,0,this.canvas_x,this.canvas_y)
      this.hx_ctx.draw()
      this.save_ctx.clearRect(0,0,this.canvas_x,this.canvas_y)
      this.save_ctx.draw()
      this.init()
    },

    /**
     * @desc 初始化canvas
     * */
    initCanvas () {
      // 创建底图层
      this.bg_ctx = uni.createCanvasContext('canvas_id_bg', this)
      // 清空所有canvas
      this.bg_ctx.clearRect(0,0,this.canvas_x,this.canvas_y)
      this.bg_ctx.drawImage(this.path, 0,0, this.canvas_x, this.canvas_y)
      this.bg_ctx.draw()

      // 创建绘图层
      this.draw_ctx = uni.createCanvasContext('canvas_id_draw', this)
      this.draw_ctx.clearRect(0,0, this.canvas_x, this.canvas_y)
      // 存在绘图层，回显绘图层
      if (this.draw_path) {
        this.draw_ctx.drawImage(this.draw_path, 0,0, this.canvas_x, this.canvas_y)
      }
      this.draw_ctx.draw()

      // 创建回显层
      this.hx_ctx = uni.createCanvasContext('canvas_id_hx', this)
      this.hx_ctx.clearRect(0,0,this.canvas_x, this.canvas_y)
      this.hx_ctx.draw()

      // 创建保存层
      this.save_ctx = uni.createCanvasContext('canvas_id_save', this)
      this.save_ctx.clearRect(0,0,this.canvas_x, this.canvas_y)
      this.save_ctx.draw()

      this.init()
    },

    touchstart (e) {
      this.drawLine(e.touches[0])
      this.drawEraserHX(e.touches[0]);
    },
    touchmove (e) {
      // 画线回显
      this.drawLine(e.touches[0]);
      // 画橡皮回显
      this.drawEraserHX(e.touches[0]);
    },
    drawEraserHX ({x,y}) {
      if (this.type != 'rubber') return
      this.hx_ctx.setFillStyle('#fff')
      this.hx_ctx.arc(x,y,this.pen_size/2,0,2*Math.PI);
      this.hx_ctx.fill();
      this.hx_ctx.draw();
    },
    touchend (e) {
      this.draw_ctx.draw(true)
      // 清空回显canvas
      this.hx_ctx.clearRect(0,0, this.canvas_x, this.canvas_y)
      this.hx_ctx.draw()
    },

    drawLine ({x,y}) {
      if (this.type != 'pen' && this.type != 'rubber') return
      this.draw_ctx?.lineTo(x,y)
      this.draw_ctx?.stroke()
      this.draw_ctx?.draw(true)
      this.draw_ctx?.moveTo(x,y)
    },

    /**
     * @desc 初始化画布绘图设置
     * */
    init () {
      // 设置线条大小
      this.draw_ctx.setLineWidth(this.pen_size)
      // 设置线条的交点样式
      this.draw_ctx.setLineJoin('round')
      // 设置线条端点样式
      this.draw_ctx.setLineCap('round')
      // 设置线条颜色
      this.draw_ctx.setStrokeStyle('rgba(238, 56, 56 ,1)')
      if (this.type === 'pen') {
        // 设置线条阴影
        this.draw_ctx.setShadow(0, 0, this.pen_size, '#EE3838')
      }else{
        this.draw_ctx.setShadow(0, 0, 0, '#EE3838')
      }
    },

    /**
     * @desc 获取节点信息
     * */
    getNodeInfo (selector) {
      return new Promise( resolve => {
        uni.createSelectorQuery().in(this).select(selector).fields({
          size: true, // 返回节点尺寸
        }, data => {
          resolve(data)
        }).exec()
      })
    },

    /**
     * @desc 获取图片信息
     * */
    getImageInfo (src) {
      return new Promise( (res, rej) => {
        uni.getImageInfo({
          src,
          success (data) {
            res(data)
          },
          fail(err){
            rej(err);
          }
        })
      } )
    },
  },
};
</script>

<style lang="scss" scoped>
.canvas-wrapper {
  width: 1400rpx;
  .canvas-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 30rpx;
    .tip {
      font-weight: 400;
      font-size: 28rpx;
      color: #333333;
      line-height: 40rpx;
    }
    .panel {
      display: flex;
      align-items: center;
      .panel-item {
        .pen-img, {
          width: 60rpx;
          height: 60rpx;
        }
        .rubber-img {
          margin-left: 20rpx;
          width: 44rpx;
          height: 44rpx;
        }
      }
      .panel-item--active {
        color: #155BD4;
      }
    }
  }
  .canvas-box {
    position: relative;
    width: 1400rpx;
    height: 664rpx;
    .canvas {
      height: 100%;
      width: 100%;
      position: absolute;
      top: 0rpx;
      left: 0rpx;
    }
    .draw-canvas {
      z-index: 2;
    }
  }

  .footer-btn {
    margin-top: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    .cancel-btn, .confirm-btn {
      margin: 0;
      width: 320rpx;
      height: 80rpx;
      background: #FFFFFF;
      border-radius: 8rpx;
      border: 1px solid #DCDFE6;
      font-weight: 400;
      font-size: 28rpx;
      color: #666666;
      line-height: 40rpx;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .confirm-btn {
      margin-left: 24rpx;
      background: #155BD4;
      color: #fff;
    }
  }
}
</style>
