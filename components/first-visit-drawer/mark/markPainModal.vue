<template>
  <uni-popup ref="popup" type="center" :is-mask-click="false">
    <view class="popup-box">
      <view class="head-box">
        <view class="title">标记疼痛部位</view>
        <image class="close-icon" src="https://static.rsjxx.com/image/2025/0624/201128_58237.png" mode="aspectFit" @click="handleClose"/>
      </view>
      <view class="edit-box">
        <canvas-panel ref="canvasPanel" :draw_path="draw_path" @getCanvasPath="getCanvasPath" />
      </view>
    </view>
  </uni-popup>
</template>

<script>
import canvasPanel from './canvas-panel.vue'

export default {
  name: 'markPainModal',
  props: {
    value: {
      type: Boolean,
      default: () => false
    },
    draw_path: {
      type: String,
      default: ''
    }
  },
  components: {
    canvasPanel
  },
  mixins: [],
  data() {
    return {
      source_bg: 'https://static.rsjxx.com/image/2025/0625/111507_90251.png',
    };
  },
  computed: {},
  watch: {
    value (val) {
      if (val) {
        this.$nextTick(() => {
          this.init()
        })
        this.$refs.popup.open()
      }else{
        this.$refs.popup.close()
      }
    }
  },
  created() {},
  mounted() {},
  methods: {
    getCanvasPath (path, draw_path) {
      this.$emit('getCanvasPath', path, draw_path)
      this.handleClose()
    },
    init () {
      uni.downloadFile({
        url: this.source_bg,
        success: (res) => {
          this.$refs.canvasPanel?.open({
            // 底图路径
            path: res.tempFilePath,
          });
        },
      })

    },
    handleClose () {
      this.clearData()
      this.$emit('input', false)
    },
    clearData () {
    },
  },
};
</script>

<style lang="scss" scoped>
.popup-box {
  border-radius: 8rpx;
  background: #FFFFFF;
  .head-box {
    padding: 32rpx 40rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #EBEDF0;
    .title {
      font-weight: 600;
      font-size: 32rpx;
      color: #333333;
      line-height: 48rpx;
    }
    .close-icon {
      width: 24rpx;
      min-width: 24rpx;
      height: 24rpx;
    }
  }
  .edit-box {
    height: 1000rpx;
    padding: 32rpx 40rpx;

  }
}
</style>
