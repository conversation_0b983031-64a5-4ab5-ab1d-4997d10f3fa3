<template>
  <div class="patientPhoto-wrapper">
    <div class="photo-box" v-if="photoList.length">
      <!-- 收缩时得图片展示 -->
      <div class="flex photo-wrapper-block" v-if="!isExpand">
        <scroll-view scroll-x>
          <div class="photo-wrapper flex">
            <div class="custom-img" v-for="(item, index) in photoList" :key="'photo' + index">
              <image
                :src="item.url+'-B.300'"
                class="cursor custom-img"
                @click="previewPhotoList(index)"
              >
              </image>
              <image src="https://static.rsjxx.com/image/2025/0227/153950_50856.png" class="delete-icon"
                     @click="deleteImg(item)" v-if="isCanDelete"></image>

              <p class="img-delete" v-if="item.url == tongue_ai.on_image">舌面</p>
              <p class="img-delete" v-if="item.url == tongue_ai.lower_image">舌底</p>
            </div>
          </div>
        </scroll-view>

        <div class="more-img cursor" @click="expand">
          <p class="more-img-title">查看更多</p>
          <p class="more-img-total">共{{ photoList.length }}张</p>
        </div>
      </div>

      <!-- 展开时得图片展示 -->
      <div class="expand-img-block" v-if="isExpand">
        <div class="expand-block" v-if="tongue_on_images.length">
          <p class="expand-block-title">舌面照片：</p>
          <div class="flex flex-wrap">
            <div class="custom-img" v-for="(item, index) in tongue_on_images" :key="'photo' + index">
              <image
                :src="item+'-B.300'"
                class="cursor custom-img"
                @click="previewSingleList('tongue_on_images', index)"
              >
              </image>
              <image src="https://static.rsjxx.com/image/2025/0227/153950_50856.png"
                     @click="deleteImg({type: 'tongue_on_images', url: item})" v-if="isCanDelete" class="delete-icon"></image>
              <p class="img-delete" v-if="item == tongue_ai.on_image">舌面</p>
            </div>
          </div>
        </div>

        <div class="expand-block" v-if="tongue_lower_images.length">
          <p class="expand-block-title">舌底照片：</p>
          <div class="flex flex-wrap">
            <div class="custom-img" v-for="(item, index) in tongue_lower_images" :key="'photo' + index">
              <image
                :src="item+'-B.300'"
                class="cursor custom-img"
                @click="previewSingleList('tongue_lower_images', index)"
              >
              </image>
              <image src="https://static.rsjxx.com/image/2025/0227/153950_50856.png"
                     @click="deleteImg({ type: 'tongue_lower_images', url: item })" v-if="isCanDelete" class="delete-icon"></image>
              <p class="img-delete" v-if="item == tongue_ai.lower_image">舌底</p>
            </div>
          </div>
        </div>

        <div class="expand-block" v-if="tongue_diag_images.length">
          <p class="expand-block-title">舌诊照片：</p>
          <div class="flex flex-wrap">
            <div class="custom-img" v-for="(item, index) in tongue_diag_images" :key="'photo' + index">
              <image
                :src="item+'-B.300'"
                class="cursor custom-img"
                @click="previewSingleList('tongue_diag_images', index)"
              >
              </image>
              <image src="https://static.rsjxx.com/image/2025/0227/153950_50856.png"
                     @click="deleteImg({ type: 'tongue_diag_images', url: item })" v-if="isCanDelete" class="delete-icon"></image>
            </div>
          </div>
        </div>

        <div class="expand-block" v-if="position_images.length">
          <p class="expand-block-title">部位照片：</p>
          <div class="flex flex-wrap">
            <div class="custom-img" v-for="(item, index) in position_images" :key="'photo' + index">
              <image
                :src="item+'-B.300'"
                class="cursor custom-img"
                @click="previewSingleList('position_images', index)"
              >
              </image>
              <image src="https://static.rsjxx.com/image/2025/0227/153950_50856.png"
                     @click="deleteImg({ type: 'position_images', url: item })" v-if="isCanDelete" class="delete-icon"></image>
            </div>
          </div>
        </div>

        <div class="expand-block" v-if="additional_images.length">
          <p class="expand-block-title" style="margin-top: 20rpx;">附加照片(医疗影像/处方/检查单等)：</p>
          <div class="flex flex-wrap">
            <div class="custom-img" v-for="(item, index) in additional_images" :key="'photo' + index">
              <image
                :src="item+'-B.300'"
                class="cursor custom-img"
                @click="previewSingleList('additional_images', index)"
              >
              </image>
              <image src="https://static.rsjxx.com/image/2025/0227/153950_50856.png"
                     @click="deleteImg({ type: 'additional_images', url: item })" v-if="isCanDelete" class="delete-icon"></image>
            </div>
          </div>
        </div>

        <p class="stow"><span class="cursor" @click="expand">收起</span></p>
      </div>
    </div>
    <div v-else style="margin-top: 7px">{{ emptyText }}</div>
  </div>
</template>

<script>
import ConsultationService from "@/api/consultation";
import {debounce, cloneDeep} from "lodash";

export default {
  name: 'patientPhoto',
  components: {},
  mixins: [],
  props: {
    tongue_image: {
      type: Object,
      default: () => {
      }
    },
    emptyText: {
      type: String,
      default: '-'
    },
    mr_id: {
      type: String,
      default: ''
    },
    // 可以删除，同时响应复制
    isCanDelete: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isExpand: false, // 是否展示更多图片
      tongue_on_images: [], // 舌面照片
      tongue_lower_images: [], // 舌底照片
      tongue_diag_images: [], // 舌诊照片
      position_images: [], // 部位照片
      additional_images: [], // 附加照片
      tongue_ai: {}, // 舌面舌底
    };
  },
  computed: {
    photoList() {
      let resultList = [];
      let typeList = [
        'tongue_on_images',
        'tongue_lower_images',
        'tongue_diag_images',
        'position_images',
        'additional_images',
      ];
      typeList.forEach(listKey => {
        this[listKey] && this[listKey].forEach(url => {
          resultList.push({
            type: listKey,
            url,
          });
        });
      });

      // 移除舌面舌底数据
      resultList = resultList.filter(item => {
        return item.url !== this.tongue_ai.on_image && item.url !== this.tongue_ai.lower_image;
      });

      // 如果存在舌面舌底，先加入舌底，再加入舌面，确保顺序
      if (this.tongue_ai.lower_image) {
        resultList.unshift({
          type: 'tongue_lower_images',
          url: this.tongue_ai.lower_image,
        });
      }

      if (this.tongue_ai.on_image) {
        resultList.unshift({
          type: 'tongue_on_images',
          url: this.tongue_ai.on_image,
        });
      }
      return resultList;
    },
  },
  watch: {
    tongue_image: {
      immediate: true,
      deep: true,
      handler(val = {}) {
        if (Object.keys(val)?.length > 0) {
          this.init(cloneDeep(val))
        }
      },
    },
  },
  created() {
  },
  mounted() {
    if ( this.isCanDelete ) {
      uni.$on('importAiRecordEvent', this.importAiRecordEvent);
    }
  },
  methods: {
    uploadRecordSaveimages: debounce(function () {
      let params = {
        images: {
          tongue_on_images: this.tongue_on_images,
          tongue_lower_images: this.tongue_lower_images,
          additional_images: this.additional_images,
          position_images: this.position_images,
        },
        mr_id: this.mr_id
      }
      this.saveImage(params,true).then(res => {
        this.$emit('deleteChange')
      })
    }, 200),
    saveImage(params,is_delete = false) {
      return new Promise(resolve => {
        ConsultationService.uploadRecordSaveimages(params).then(res => {
          resolve('1')
          if (is_delete) {
            // uni.showToast({
            //   title: '删除成功',
            //   icon: 'none'
            // })
          }
        })
      })
    },
    // 删除图片
    deleteImg(data) {
      let list = this[data.type] || []
      let index = list.findIndex(item => item === data.url)
      this[data.type]?.splice(index, 1)
      this.uploadRecordSaveimages()
    },

    importAiRecordEvent (obj) {
        let tongue_image = obj.tongue_image || {}
        if (Object.keys(tongue_image)?.length > 0) {
          let params = {
            images: {
              tongue_on_images: tongue_image.tongue_on_images || {},
              tongue_lower_images: tongue_image.tongue_lower_images || {},
              additional_images: tongue_image.additional_images || {},
              position_images: tongue_image.position_images || {},
            },
            mr_id: this.mr_id
          }
          this.saveImage(params).then(res => {
            // !data.isCopyAll && uni.showToast({
            //   title: '导入成功',
            //   icon: 'none',
            // });
            this.init(cloneDeep(tongue_image))
          })
        }
    },

    // 预览收起来的图片
    previewPhotoList(index) {
      let list = this.getPrivewImages('photoList')
      this.preview(list, index)
    },
    // 预览展开的图片
    previewSingleList(type, index) {
      let list = this.getPrivewImages(type, index)
      this.preview(list, index)
    },
    preview(list, index) {
      uni.previewImage({
        urls: list,
        current: index
      })
    },
    init(res) {
      // 获取图片
      this.tongue_on_images = res.tongue_on_images;
      this.tongue_lower_images = res.tongue_lower_images;
      this.position_images = res.position_images;
      this.additional_images = res.additional_images;
      this.tongue_diag_images = res.tongue_diag_images || [];

      // 舌面舌底照片
      this.tongue_ai = res.tongue_ai;
    },
    reset() {
      if (this.isExpand) {
        this.isExpand = !this.isExpand
      }
    },

    // 预览对应当前图片列表
    getPrivewImages(type) {
      let bigImageList = [];
      this[type].map(item => {
        if (type == 'photoList') {
          // bigImageList.push(`${item.url}?imageView2/2/w/1000/q/75`);
          bigImageList.push(`${item.url}`);
        } else {
          // bigImageList.push(`${item}?imageView2/2/w/1000/q/75`);
          bigImageList.push(`${item}`);
        }
      });
      return bigImageList
    },

    // 展开更多/收起
    expand() {
      this.isExpand = !this.isExpand;
      if (this.isExpand == false) {
        this.$emit('isStow');
      }
    },
  },
  filters: {},
  beforeDestroy() {
    this.importAiRecordEvent && uni.$off('importAiRecordEvent', this.importAiRecordEvent);
  },
};
</script>

<style lang="scss" scoped>
.patientPhoto-wrapper {
  width: 100%;

  .box-top {
    .box-top-l {
      .box-top-l-title {
        font-size: 28rpx;
        font-weight: 500;
        color: #000000;
        line-height: 40rpx;
      }

      .box-top-l-tip {
        font-size: 24rpx;
        font-weight: 400;
        color: #aaaaaa;
        line-height: 24rpx;
      }
    }

    .box-top-r {
      margin-top: -36rpx;
      // width: 46px;
      //height: 32px;
      background: #f9f9fa;
      border-radius: 8rpx;
      // box-shadow: 0rpx 0rpx 10rpx 10rpx rgba(21, 91, 212, 0.1);
      padding: 12rpx;
      box-sizing: border-box;

      .scan-wrapper {
        .scan-title-tip {
          display: flex;
          align-items: center;
          width: 96rpx;
          min-width: 96rpx;
          font-size: 24rpx;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.45);
          line-height: 34rpx;
        }

        img {
          width: 28rpx;
          height: 28rpx;
        }

        .custom-right {
          // margin-left: 9px;
          margin-right: 8rpx;
          width: 10rpx;
          height: 18rpx;
          color: #c8c8c8;
          font-weight: 600;
          line-height: 20rpx;
        }
      }
    }
  }

  .photo-box {
    .photo-box-title {
      font-size: 28rpx;
      font-weight: 400;
      color: #666666;
      line-height: 44rpx;
    }

    .photo-wrapper-block {
      height: 128rpx;
      margin-top: 16rpx;
      position: relative;

      .photo-wrapper {
        border-radius: 8rpx;
        //width: 100%;
        padding: 16rpx 120rpx 0rpx 0rpx;
        overflow-y: scroll;

        &::-webkit-scrollbar {
          display: none;
        }
      }

      .more-img {
        width: 134rpx;
        min-width: fit-content;
        height: 112rpx;
        position: absolute;
        right: 0rpx;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding-left: 20rpx;
        background: linear-gradient(90deg, #fafafb 0%, #fafafb 30%, #fafafb 100%);

        .more-img-title {
          font-size: 26rpx;
          font-weight: 400;
          color: #000000;
          line-height: 44rpx;
        }

        .more-img-total {
          margin-top: 8rpx;
          font-size: 24rpx;
          font-weight: 300;
          color: #ccc;
          line-height: 24rpx;
        }
      }
    }

    // 图片展开的模块
    .expand-img-block {
      background: #fafafb;
      padding: 24rpx 16rpx;
      margin-top: 16rpx;
      // 收起
      .stow {
        display: flex;
        justify-content: center;
        align-items: center;
        background: #fafafb;
        border-radius: 8rpx;
        font-size: 26rpx;

        &:hover {
          color: rgba(21, 91, 213, 1);
        }
      }

      .expand-block-title {
        font-size: 24rpx;
        font-weight: 300;
        color: #666666;
        line-height: 24rpx;
        margin-bottom: 16rpx;
      }
    }

    // 图片的样式
    .custom-img {
      position: relative;
      width: 96rpx;
      height: 96rpx;
      border-radius: 8rpx;
      margin-right: 20rpx;
      margin-bottom: 13rpx;

      img {
        width: 96rpx;
        height: 96rpx;
      }

      .delete-icon {
        width: 32rpx;
        height: 32rpx;
        position: absolute;
        top: -16rpx;
        right: -16rpx;
      }

      .img-delete {
        height: 28rpx;
        width: 100%;
        position: absolute;
        color: #fff;
        bottom: 0;
        left: 0;
        background: rgba(0, 0, 0, 0.3);
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 0rpx 0rpx 8rpx 8rpx;
        padding: 8rpx 0;
        //cursor: pointer;
        font-weight: 400;
        font-size: 22rpx;
        color: #FFFFFF;
        line-height: 24rpx;
        //&::before {
        //  position: absolute;
        //  left: 0;
        //  bottom: 0;
        //  width: 100%;
        //  height: 48px;
        //  content: '';
        //}
      }

      &:hover .btn-delete {
        display: block;
        top: -18rpx;
        right: -12rpx;
      }

      // &:hover {
      //  .img-delete {
      //    opacity: 1;
      //  }
      // }
    }

    &:nth-of-type(7n) {
      .custom-img {
        margin-right: 0 !important;
      }
    }
  }

  .empty {
    text-align: center;
    font-size: 28rpx;
    font-weight: 400;
    color: #666666;
    line-height: 44rpx;
  }
}

.mr6 {
  margin-right: 12rpx;
}

.mt8 {
  margin-top: 16rpx;
}

.cursor {
  cursor: pointer;
}
</style>

<style lang="scss">
.scan-block {
  min-width: 130px !important;

  .scan-block-content {
    text-align: center;

    .scan-tip {
      margin-top: 6rpx;
      font-size: 24rpx;
      font-weight: 400;
      color: #333;
      line-height: 32rpx;
      margin-top: 16rpx;
    }
  }
}

.flex {
  display: flex;
}
</style>
