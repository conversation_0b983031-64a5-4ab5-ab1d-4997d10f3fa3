<template>
  <view class="content">
    <view class="pay-money">
      <span>待支付金额:</span>
      <span class="money">¥{{ Number(payment_fee || 0).toFixed(2) }}</span>
    </view>
    <view class="pay-qr">
      <tki-qrcode
        ref="qrcode"
        cid="codeUrl"
        :val="codeUrl"
        :size="200"
        unit="rpx"
        background="#fff"
        foreground="#000"
        pdground="#000"
        :onval="true"
        :loadMake="true"
      />
    </view>
    <span class="scan">用户扫描二维码付款</span>
  </view>
</template>

<script>
import PayService from "@/api/pay";
import TkiQrcode from '../../../tki-qrcode/tki-qrcode.vue'

export default {
  name: 'collectionCode',
  components: {
    TkiQrcode
  },
  mixins: [],
  props: {
    codeUrl: {
      type: String,
      default: '',
    },
    // 微信待支付金额
    payment_fee: {
      type: [String, Number],
      default: '',
    },
    order_id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      drawerVisible: false,
      timer: null,
    };
  },
  computed: {

  },
  watch: {
  },
  created() {},
  mounted() {},
  methods: {
    // ************微信付款***********
    //  清除定时器
    clearIntervalEvent() {
      clearInterval(this.timer);
      this.timer = null;
    },
    waitPay() {
      this.clearIntervalEvent();
      setTimeout(() => {
        this.timer = setInterval(() => {
          PayService.getPayPaystate({ order_id: this.order_id }).then(res => {
            if (res.has_pay === '1') {
              this.clearIntervalEvent();
              this.$emit('payOk');
            }
          });
        }, 1000);
      }, 300);
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  .pay-money {
    font-weight: 400;
    font-size: 28rpx;
    color: #333333;
    line-height: 36rpx;
    display: flex;
    align-items: center;
    .money {
      font-weight: 500;
      font-size: 36rpx;
      color: #F74441;
      line-height: 36rpx;
    }
  }
  .pay-qr {
    margin-top: 40rpx;
    width: 400rpx;
    height: 400rpx;
    border-radius: 8rpx;
  }
  .scan {
    margin-top: 80rpx;
    width: 332rpx;
    height: 64rpx;
    background: rgba(54,162,31,0.08);
    border-radius: 32rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #36A21F;
    line-height: 32rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
