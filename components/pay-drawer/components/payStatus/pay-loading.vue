<template>
    <view class="content">
      <div class="pay-loading" v-if="drawerVisible">
        <div class="pay-loading-icon">
          <img src="https://static.rsjxx.com/image/2025/0603/115106_78871.png" />
        </div>
        <div class="loading-text"></div>
      </div>
    </view>
</template>

<script>
import PayService from "@/api/pay";
export default {
  name: 'payloading',
  components: {
  },
  mixins: [],
  props: {
    order_id: {
      type: String,
      default: ''
    },
    drawerVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      timer: null,
    };
  },
  computed: {

  },
  watch: {
    value (val) {
      if ( val ) {
        this.drawerVisible = true
      }else{
        this.drawerVisible = false
      }
    },
  },
  created() {},
  mounted() {},
  methods: {
    //  清除定时器
    clearIntervalEvent() {
      clearInterval(this.timer);
      this.timer = null;
    },
    waitPay() {
      this.clearIntervalEvent();
      setTimeout(() => {
        this.timer = setInterval(() => {
          PayService.getPayPaystate({ order_id: this.order_id }).then(res => {
            if (res.has_pay === '1') {
              this.clearIntervalEvent();
              this.$emit('payOk');
            }
          });
        }, 1000);
      }, 300);
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .pay-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .pay-loading-icon{
      margin-bottom: 48rpx;
      img {
        width: 128rpx;
        height: 128rpx;
      }
    }
  }

  /* HTML: <div class="loader"></div> */
  .loading-text {
    width: fit-content;
    font-weight: 500;
    font-family: monospace;
    font-size: 48rpx;
    clip-path: inset(0 100% 0 0);
    animation: l5 2s steps(11) infinite;
  }

  .loading-text:before {
    content: '正在支付中...';
  }

  @keyframes l5 {
    to {
      clip-path: inset(0 -1ch 0 0);
    }
  }
}
</style>
