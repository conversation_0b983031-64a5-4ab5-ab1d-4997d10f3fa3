<!-- 当点击了扫码下一步，b扫c c扫b的逻辑都在这里出来 -->
<template>
  <view>
    <cus-drawer v-model="drawerVisible" :title="title" @on-open="onOpen" footerHide @change="onChange" :width="800">
      <view slot="content" class="content">
        <!-- 收款码支付 -->
        <collection-code ref="collectionCode" v-if="scanType === '2'" :payment_fee="payment_fee" :codeUrl="codeUrl" :order_id="order_id" @payOk="payOk"></collection-code>
        <!-- 扫码枪支付 -->
        <pay-loading ref="payCode" :drawerVisible="drawerVisible" v-if="scanType === '1'" :order_id="order_id" @payOk="payOk"></pay-loading>
      </view>
    </cus-drawer>
  </view>
</template>

<script>
import cusDrawer from '@/components/cus-drawer/index.vue'
import collectionCode from './collection-code.vue'
import payLoading from './pay-loading.vue'
export default {
  name: 'scan-pay-process',
  components: {
    cusDrawer,
    collectionCode,
    payLoading
  },
  mixins: [],
  props: {
    value: {
      type: Boolean,
      default: () => false
    },
    title: {
      type: String,
      default: '收款'
    },
    scanType: {
      type: String,
      default: ''
    },
    // 微信待支付金额
    payment_fee: {
      type: [String, Number],
      default: '',
    },
    order_id: {
      type: String,
      default: ''
    },
    codeUrl: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      drawerVisible: false,
      successVisible: false,
      successData: {
        payment_fee: 0,
        pay_platform_text: '',
        pay_time: '',
        remark: '',
      },
    };
  },
  computed: {

  },
  watch: {
    value (val) {
      if ( val ) {
        this.drawerVisible = true
      }else{
        this.drawerVisible = false
      }
    },
  },
  created() {},
  mounted() {},
  methods: {
    // 支付成功
    payOk () {
      this.$emit('payOk')
    },
    confirm () {
    },
    onOpen () {
     this.$nextTick(() => {
       if (this.scanType === '1') {
         const payCode = this.$refs?.payCode
         payCode.waitPay()
       }
       if (this.scanType === '2') {
         const collectionCode = this.$refs?.collectionCode
         console.log("=>(scan-pay-process.vue:104) collectionCode", collectionCode);
         collectionCode.waitPay()
       }
     })
    },
    onChange (val) {
      console.log("=>(scan-pay-process.vue:102) val", val);
      if (!val) {
        this.closeDrawer()
      }
    },

    closeDrawer () {
      this.$emit('input', false)
      this.$emit('close')
      this.clearData()
    },
    clearData () {
      const payCode = this.$refs.payCode
      payCode?.clearIntervalEvent();
      const collectionCode = this.$refs.collectionCode
      console.log("=>(scan-pay-process.vue:116) collectionCode", collectionCode);
      collectionCode?.clearIntervalEvent();
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
</style>


