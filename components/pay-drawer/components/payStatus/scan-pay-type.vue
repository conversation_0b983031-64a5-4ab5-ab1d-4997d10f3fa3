<template>
  <view class="scan-wrapper">
    <view class="scan-box">
      <view class="scan-item" :class="{ 'scan-item--active': value === item.scanType }" v-for="(item, index) in typeList"
            :key="index" @click="changeScanType(item)">
        <view class="scan-title">{{ item.title }}</view>
        <image class="scan-img" :src="item.src"/>
        <view class="desc">
          <span>{{ item.scanType === '2' ? '用户使用' : '用户出示' }}</span>
          <image class="pay-icon" :src="value === item.scanType ? imgIcon.lightWx : imgIcon.grayWx"/>
          <span>或</span>
          <image class="pay-icon" :src="value === item.scanType ? imgIcon.lightZfb : imgIcon.grayZfb"/>
          <span>{{ item.scanType === '2' ? '的扫码功能' : '的收款码' }}</span>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'scanPayType',
  components: {},
  mixins: [],
  props: {
    // 选择扫码类型
    value: {
      type: String,
      default: '1'
    }
  },
  data() {
    return {
      typeList: [
        { scanType: '1', title: '商家扫用户', src: 'https://static.rsjxx.com/image/2025/0603/143403_16558.png' },
        { scanType: '2', title: '用户扫商家', src: 'https://static.rsjxx.com/image/2025/0603/143403_68878.png' },
      ],
      imgIcon: {
        lightWx: 'https://static.rsjxx.com/image/2025/0603/144446_94919.png',
        grayWx: 'https://static.rsjxx.com/image/2025/0603/144446_55474.png',
        lightZfb: 'https://static.rsjxx.com/image/2025/0603/144446_11925.png',
        grayZfb: 'https://static.rsjxx.com/image/2025/0603/144446_17418.png',
      }
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    changeScanType (item) {
      this.$emit('input', item.scanType)
    },
  },
};
</script>

<style lang="scss" scoped>
.scan-wrapper {
  margin-top: 40rpx;
  width: 100%;
  position: relative;
  &::before {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-bottom: solid 24rpx #FAFAFB;
    border-right: solid 24rpx transparent;
    border-left: solid 24rpx transparent;
    left: 50%;
    top: -24rpx;
    margin-left: -272rpx;
  }
  .scan-box {
    background: #FAFAFB;
    display: flex;
    padding: 24rpx;

    .scan-item--active {
      border-color: #1157E5 !important;
      .scan-title, .desc {
        color: #1157E5 !important;
      }
    }

    .scan-item {
      flex: 1;
      height: 414rpx;
      background: #FFFFFF;
      border-radius: 8rpx;
      border: 2rpx solid #E3E5EB;
      &:first-child {
        margin-right: 24rpx;
      }

      .scan-title {
        margin-top: 48rpx;
        font-weight: 600;
        font-size: 30rpx;
        color: #666666;
        line-height: 44rpx;
        text-align: center;
      }

      .scan-img {
        margin-top: 40rpx;
        width: 312rpx;
        height: 188rpx;
      }

      .desc {
        margin-top: 24rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: 400;
        font-size: 22rpx;
        color: #999999;
        line-height: 32rpx;
        .pay-icon {
          width: 24rpx;
          min-width: 24rpx;
          height: 24rpx;
          margin: 0 6rpx;
        }
      }
    }
  }
}
</style>
