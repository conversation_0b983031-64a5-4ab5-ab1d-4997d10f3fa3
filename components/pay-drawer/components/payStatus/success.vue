<template>
  <view>
    <cus-drawer v-model="drawerVisible" :title="title" @change="onChange" @on-open="onOpen" footerHide :width="800">
      <view slot="content" class="content">
        <img class="icon" src="https://static.rsjxx.com/image/2025/0603/141536_75367.png" />
        <view class="text">收款成功</view>
        <view class="money-box">
          <span class="money-desc">已收金额</span>
          <span class="money">¥ {{ Number(successData.payment_fee || 0).toFixed(2) }}</span>
        </view>
        <view class="btn-group">
          <u-button class="default-btn" type="default" @click="$emit('input', false)">关闭</u-button>
          <u-button class="primary-btn" type="primary" v-if="showOrderBtn" @click="seeOrder">查看订单</u-button>
        </view>
      </view>
    </cus-drawer>
  </view>
</template>

<script>
import cusDrawer from '@/components/cus-drawer/index.vue'
export default {
  name: 'success',
  components: {
    cusDrawer,
  },
  mixins: [],
  props: {
    value: {
      type: Boolean,
      default: () => false
    },
    title: {
      type: String,
      default: '收款'
    },
    successData: {
      type: Object,
      default: () => {}
    },
    showOrderBtn: {
      type: Boolean,
      default: () => false
    }
  },
  data() {
    return {
      drawerVisible: false,
    };
  },
  computed: {

  },
  watch: {
    value (val) {
      if ( val ) {
        this.drawerVisible = true
      }else{
        this.drawerVisible = false
      }
    },
  },
  created() {},
  mounted() {},
  methods: {
    // 查看订单
    seeOrder () {
      this.$emit('seeOrder')
      this.$emit('input', false)
      this.clearData()
    },

    confirm () {
    },
    onChange (val) {
      if (!val) {
        this.closeDrawer()
      }
    },
    onOpen () {
    },
    closeDrawer () {
      this.$emit('input', false)
      this.$emit('close')
      this.clearData()
    },
    clearData () {
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .icon {
    width: 128rpx;
    height: 128rpx;
  }
  .text {
    margin-top: 48rpx;
    font-weight: 500;
    font-size: 48rpx;
    color: #333333;
    line-height: 64rpx;
  }
  .money-box {
    margin-top: 24rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    .money-desc {
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      line-height: 32rpx;
    }
    .money {
      margin-left: 32rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: #333333;
      line-height: 32rpx;
    }
  }

  .btn-group {
    margin-top: 48rpx;
    display: flex;
    align-items: center;
    .default-btn {
      width: 272rpx;
      height: 80rpx;
      background: #FFFFFF;
      border-radius: 8rpx;
      border: 2rpx solid rgba(51,51,51,0.15);
      font-weight: 400;
      font-size: 28rpx;
      color: rgba(51,51,51,0.65);
      line-height: 44rpx;
      text-align: center;
    }
    .primary-btn {
      margin-left: 24rpx;
      width: 272rpx;
      height: 80rpx;
      background: #FFFFFF;
      border-radius: 8rpx;
      border: 2rpx solid #1157E5;
      font-weight: 400;
      font-size: 28rpx;
      color: #1157E5;
      line-height: 44rpx;
      text-align: center;
    }
  }
}
</style>
