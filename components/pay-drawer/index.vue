<template>
  <view>
    <cus-drawer v-model="drawerVisible" title="收款" @on-open="onOpen" @change="onChange" :width="800">
      <view slot="content" class="content">
        <view class="content-left">
          <scroll-view scroll-y>
            <view class="item-box">
              <view class="label-title">订单金额：</view>
              <view class="order-money-box">
                <view class="label-text">应付金额</view>
                <div style="display: flex; align-items: center">
                <span
                  class="underline-money"
                  v-show="pay_activeId == 'user_recharge' && is_recharge_buy === 'yes' && getDiscountFee"
                >¥{{ Number(order_info.payment_fee||0).toFixed(2) }}</span>
                  <view class="pay-money">¥{{ Number(origin_payment).toFixed(2) }}</view>
                </div>
              </view>
            </view>

            <view class="item-box">
              <view class="label-title">优惠金额：</view>
              <input v-model="extra_discount" max="10" @blur="blur" class="cus-number-input" type="digit" placeholder="请输入优惠金额（非必填）" />
            </view>

            <view class="actually-pay-box">
              <view class="actually-pay">
                <view class="actually-label">实际支付：</view>
                <view class="actually-money">¥{{ Number(real_pay || 0).toFixed(2) }}</view>
              </view>
            </view>
          </scroll-view>
        </view>
        <view class="content-right">
          <scroll-view scroll-y>
            <view class="label-text">收款方式</view>
            <view class="methods-box">
              <view class="methods-item"
                    :class="{'methods-item--active': methods_item.type === pay_activeId, 'methods-item--disabled': false }"
                    @click="changeMehtods(methods_item)" v-for="(methods_item, methods_index) in getPaymentMethods"
                    :key="methods_index">
                <view>{{ methods_item.label }}</view>
                <view class="methods-itme-money" v-if="methods_item.type === 'user_recharge'">¥{{ Number(getCurrentItem.balance || 0).toFixed(2) }}</view>
                <div class="triangle" v-if="methods_item.type === pay_activeId">
                  <u-icon name="checkmark" color="#fff"></u-icon>
                </div>
                <div
                  class="pay-discount"
                  :class="{ 'pay-discount--active': methods_item.type === 'user_recharge' && pay_activeId == 'user_recharge' }"
                  v-if="getDiscountFee != 0 && methods_item.type === 'user_recharge'"
                >
                  <span class="discount-text">减<span>{{ Math.abs(getDiscountFee) }}</span></span>
                </div>
              </view>
            </view>
            <!-- 扫码支付 -->
            <scan-pay-type v-model="scanType" v-if="pay_activeId === 'scan'"></scan-pay-type>

            <!-- 线下支付 -->
            <view class="offline-pay-box" v-if="pay_activeId === 'cash'">
              <radio-group @change="radioChange" class="pay-group">
                <label class="pay-item" :class="{'pay-item--active': item.kw === current}" v-for="(item, index) in walletList" :key="item.kw">
                  <view>
                    <radio :value="item.kw" :checked="item.kw === current" />
                  </view>
                  <image class="cash-logo" :src="item.imageUrl" />
                  <view>{{item.desc}}</view>
                </label>
              </radio-group>
            </view>

            <!-- 储值支付 -->
            <view class="stored-box" v-if="pay_activeId === 'user_recharge'">
              <uni-data-select
                class="cus-select"
                v-model="storedCurrent"
                :localdata="user_wallet_list"
                :page-size="100"
                :clear="false"
                @change="storedChange"
              >
              </uni-data-select>
              <view class="card-box" :style="{'background-image': `url(${ (getCurrentItem.user_role === 'SELF' || getCurrentItem.user_role == '') ? self_card : share_card })`}">
                <view class="balance">
                  <view class="unit">¥</view>
                  <view class="stored-money">{{ Number(getCurrentItem.balance||0).toFixed(2) }}</view>
                </view>
                <view class="belonging-person">持卡人：{{ getCurrentItem.real_name || '-' }}</view>
              </view>
            </view>


            <!-- 收款备注 -->
            <view class="remark-box">
              <view class="label-text">收款备注</view>
              <view class="textarea-box">
                <textarea class="cus-textarea" maxlength="50" v-model="remark" placeholder-style="color:#BBBBBB" placeholder="请输入内容"/>
              </view>
            </view>
          </scroll-view>
        </view>
      </view>

      <view slot="footer" class="footer">
        <view class="btn-group">
          <button class="ui-btn-drawer-base ui-btn-drawer-default" type="default" @click="closeDrawer">取消</button>
          <button class="ui-btn-drawer-base ui-btn-drawer-primary ui-btn-distance-l" type="primary"  @click="payNext" v-if="pay_activeId === 'scan'">下一步</button>
          <button class="ui-btn-drawer-base ui-btn-drawer-primary ui-btn-distance-l" type="primary" v-else :loading="confirmLoading" @click="confirm" :disabled="pay_activeId === 'user_recharge' && Number(getCurrentItem.balance|| 0) < Number(real_pay)">
            {{pay_activeId === 'user_recharge' && Number(getCurrentItem.balance|| 0) < Number(real_pay) ? '余额不足' :'确认支付'}}
          </button>
        </view>
      </view>
    </cus-drawer>

    <!-- 正在支付中 -->
    <pay-loading v-model="loadingVisible"></pay-loading>
    <!-- 扫码支付流程 -->
    <scan-pay-process v-model="payProcessVisible" :scanType="scanType" :codeUrl="codeUrl" :payment_fee="payment_fee" :order_id="order_id"  @payOk="payOk"></scan-pay-process>
    <!-- 支付成功 -->
    <success v-model="successVisible" :showOrderBtn="showOrderBtn" :successData="successData" @close="successClosed" @seeOrder="successSeeOrder"></success>
  </view>
</template>

<script>
import cusDrawer from '@/components/cus-drawer/index.vue'
import PayService from "@/api/pay";
import {$operator} from "@/utils/operation";
import collectionCode from './components/payStatus/collection-code.vue'
import payLoading from './components/payStatus/pay-loading.vue'
import success from './components/payStatus/success.vue'
import scanPayType from './components/payStatus/scan-pay-type.vue'
// import {debounce, throttle} from "lodash";
import {isDigitalStore} from "@/utils/runtime";
import scanPayProcess from './components/payStatus/scan-pay-process.vue'
export default {
  name: 'finish-consult',
  components: {
    cusDrawer,
    collectionCode,
    payLoading,
    success,
    scanPayType,
    scanPayProcess
  },
  mixins: [],
  props: {
    value: {
      type: Boolean,
      default: () => false
    },
    order_id: {
      type: String,
      default: ''
    },
    showOrderBtn: {
      type: Boolean,
      default: () => false
    }
  },
  data() {
    return {
      confirmLoading: false,
      drawerVisible: false,
      extra_discount: '', // 优惠金额
      paymentMethods: [
        { label: '扫码付款', type: 'scan' },
        {label: '线下收款', type: 'cash'},
        { label: '储值余额支付', type: 'user_recharge' },
      ], // 收款方式枚举
      pay_activeId: '',
      self_card: 'https://static.rsjxx.com/image/2025/0121/180201_58756.png',
      share_card: 'https://static.rsjxx.com/image/2025/0121/180411_45145.png',
      remark: '',
      storedCurrent: '', // 持卡人
      user_wallet_list: [], // 持卡人信息
      walletList: {},
      current: 'cash', // 默认现金收款
      order_info: {},
      OfflinePaymentIcon: {
        CASH: require('@/static/image/pay/collection/cash.png'), // 现金收款
        OFFLINE_WXCODE: require('@/static/image/pay/collection/weixin.png'), // 微信收款
        OFFLINE_ALIPAY: require('@/static/image/pay/collection/zhifubao.png'), // 支付宝收款
        OFFLINE_LAKALA: require('@/static/image/pay/collection/lakala.png'), // 拉卡拉收款
        OFFLINE_UNION_PAY: require('@/static/image/pay/collection/union.png'), // 银联收款
        OFFLINE_MEITUAN: require('@/static/image/pay/collection/meituan.png'), // 美团团购
        OFFLINE_DOUYIN: require('@/static/image/pay/collection/douyin.png'), // 抖音团购
        OFFLINE_YB: require('@/static/image/pay/collection/yibao.png'), // 抖音团购
        OFFLINE_JD: require('@/static/image/pay/collection/jd.png'), // 京东
        OFFLINE_HJK: require('@/static/image/pay/collection/hjk.png'), // 好聚客
      },
      is_recharge_buy: 'no', // 是否是充值购买
      stored_total_fee: '', // 储值金额应当支付的金额

      scanType: '1', // 选择扫码付款时，选择的扫码类型
      wxCollectionVisible: false, // 微信收款码弹窗
      loadingVisible: false, // 支付中loading
      successVisible: false, // 支付成功
      payProcessVisible: false, // 扫码支付流程
      successData: {},
      codeUrl: '',
      payment_fee: '',
    };
  },
  computed: {
    getPaymentMethods() {
      const basePayment = [
        {label: '线下收款', type: 'cash'},
        {label: '储值余额支付', type: 'user_recharge'},
      ]
      const opcPayment = [
        {label: '扫码付款', type: 'scan'},

        {label: '储值余额支付', type: 'user_recharge'},
      ]
      return isDigitalStore() ? opcPayment : basePayment
    },
    origin_payment () {
      if ( this.is_recharge_buy === 'yes' && +this.getDiscountFee && this.pay_activeId === 'user_recharge' ) {
        return $operator.subtract(Number(this.order_info.payment_fee || 0), Number(this.getDiscountFee || 0));
      }else{
        return this.order_info?.payment_fee || 0
      }
    },
    getDiscountFee() {
      if (this.is_recharge_buy === 'no') {
        return 0;
      } else {
        return $operator.subtract(Number(this.order_info.payment_fee), Number(this.stored_total_fee));
      }
    },
    getCurrentItem () {
      let item = this.user_wallet_list.find(item => item.value === this.storedCurrent)
      return item || {}
    },
    real_pay () {
      let money = $operator.subtract(Number(this.order_info.payment_fee || 0), Number(this.extra_discount || 0))
      if ( this.pay_activeId === 'user_recharge' && +this.getDiscountFee && this.is_recharge_buy == 'yes' ) {
        money = $operator.subtract(Number(money || 0), Number(this.getDiscountFee || 0))
      }
      return  money <= 0 ? 0.01 : money
    },
  },
  watch: {
    value (val) {
      if ( val ) {
        this.drawerVisible = true
      }else{
        this.drawerVisible = false
        this.confirmLoading = false
      }
    },
  },
  created() {
  },
  mounted() {
  },
  methods: {
    payOk () {
      this.confirmPay()
    },
    // 支付成功弹窗关闭
    successClosed () {
      this.successVisible = false
      this.payProcessVisible = false
      this.closeDrawer()
      this.$emit('success')
    },
    // 支付成功查看订单
    successSeeOrder () {
      this.$emit('seeOrder')
    },
    init () {
      this.getOptions()
      this.getOrderInfo()
    },
    getOptions () {
      PayService.getPayOptions().then( res => {
        this.handlerWalletList(res.offline_pay_desc || {})
      } )
    },
    handlerWalletList (offline_pay_desc) {
      this.walletList = {}
      for (let pay_key in offline_pay_desc) {
        this.$set(this.walletList, [pay_key],{
          ...offline_pay_desc[pay_key],
          imageUrl: this.OfflinePaymentIcon[pay_key?.toUpperCase()],
        })
      }
    },
    // 提交预付款
    prePay (code) {
      return new Promise((resolve,reject) => {
        let params = {
          order_id: this.order_id,
          // pay_platform: this.pay_activeId,
          ap_pay_code_info: this.pay_activeId === 'scan' && this.scanType === '1' ? code : '',
          ap_pay_source: this.pay_activeId === 'scan' ? this.scanType : '',
          pay_platform: this.pay_activeId === 'scan' ? 'ap' : this.pay_activeId === 'user_recharge' ? 'user_recharge' : this.current,
          remark: this.remark,
          extra_discount: this.extra_discount,
          trade_uid: this.pay_activeId === 'user_recharge' ? this.getCurrentItem.uid : '', // 付款用户id(储值支付会用到)
        }
        this.confirmLoading = true
        PayService.getPayShop(params).then( res => {
          this.payment_fee = res.payment_fee
          resolve()
          if (this.pay_activeId === 'scan') {
            if (this.scanType === '2') {
              this.codeUrl = res.pay_params.code_url;
            }
            return
          }
          if (this.pay_activeId !== 'scan') {
            this.confirmPay()
          }
        } ).finally(() => {
          this.confirmLoading = false
        }).catch( err => {
          reject(err)
        } )
      })
    },
    // 确认支付
    confirmPay () {
      return new Promise(resolve => {
        let params = {
          order_id: this.order_id
        }
        PayService.getPayConfirm(params).then( res => {
          this.successVisible = true
          this.successData = res
          resolve()
        } ).finally(() => this.confirmLoading = false).catch(err => {
          this.confirmLoading = false;
        })
      })
    },

    // 获取订单信息
    getOrderInfo () {
      let params = {
        order_id: this.order_id,
      }
      PayService.getPayInfo(params).then( res => {
        this.order_info = res.order
        this.is_recharge_buy = res.order.is_recharge_buy;
        this.stored_total_fee = res.order.stored_total_fee;
        this.user_wallet_list = this.handlerWallet(res.user_wallet_list)
        if (this.user_wallet_list.length) {
          this.storedCurrent = this.user_wallet_list[0]?.uid
        }
      } )
    },
    handlerWallet (list = []) {
      let wallet_list = []
      list.forEach(item => {
        wallet_list.push({
          value: item.uid,
          text: `${item.real_name}(${Number(item.balance||0).toFixed(2)})`,
          ...item
        })
      })
      return wallet_list
    },
    storedChange (val) {
      this.storedCurrent = val
    },
    radioChange (val) {
      let value = val.detail.value
      this.current = value
    },
    changeMehtods (item) {
      this.pay_activeId = item.type
      // 重置扫码方式
      this.scanType = '1'
      this.extra_discount = ''
    },
    blur (e) {
      let money = e.detail.value
      let list = money.split('.')
      let price = ''
      if ( list.length > 1 ) {
        price = `${list[0]}.${list[1].slice(0,2)}`
      }else{
        price = money
      }
      if ( +price >+ +this.order_info.payment_fee ) {
        this.extra_discount = ''
        uni.showToast({
          title: '最少支付0.01',
          icon: 'none'
        })
      }else{
        this.extra_discount = price
      }
    },
    scanCode() {
      let code = ''
      uni.scanCode({
        success: res => {
          if (res.result) {
            code = res.result
            this.prePay(code).then( res => {
              this.payProcessVisible = true
            } )
          } else {
            uni.showToast({
              title: '扫码失败',
              icon: 'none',
            })
          }
        },
        fail: res => {
          uni.showToast({
            title: '扫码失败',
            icon: 'none',
          })
        }
      })
      return code
    },

    // 扫码支付下一步
    async payNext () {
      if (this.pay_activeId === 'scan' && this.scanType === '1') {
        this.scanCode()
        return
      }
      this.prePay().then( res => {
        this.payProcessVisible = true
      } )
    },
    confirm () {
      this.prePay().then( res => {
        this.confirmLoading = true
      } )
    },
    onOpen () {
      // 初始化
      this.pay_activeId = isDigitalStore() ? 'scan' : 'cash'
      this.scanType = '1'
      this.current = 'cash' // 默认现金收款
      this.init()
    },
    onChange (val) {
      if (!val) {
        this.closeDrawer()
        this.$emit('close')
      }
    },
    closeDrawer () {
      this.confirmLoading = false
      this.$emit('input', false)
      this.clearData()
    },
    clearData () {
      this.extra_discount = ''
      this.remark = ''
      this.storedCurrent = ''
      this.user_wallet_list = []
      this.walletList = {}
      this.order_info = {}
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  display: flex;
  height: 100%;
  .content-left {
    position: relative;
    background: #fafbfc;
    box-shadow: 0 4rpx 8rpx 0 hsla(0, 0%, 92.5%, .5);
    height: calc(100% - 34rpx);
    flex: 1;
    &:after {
      content: " ";
      background-image: url('https://static.rsjxx.com/image/2025/0121/160106_57997.png');
      display: inline-block;
      width: 100%;
      height: 40rpx;
      bottom: -32rpx;
      position: absolute;
      background-repeat: round;
    }
    .item-box {
      padding: 40rpx 40rpx 0 40rpx;
      .label-title {
        font-size: 26rpx;
        font-weight: 400;
        color: #333;
        line-height: 36rpx;
        margin-bottom: 20rpx;
      }

      .order-money-box {
        padding: 40rpx 32rpx;
        background: #fff;
        border-radius: 8rpx;
        border: 2rpx solid #f3f3f5;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .label-text {
          font-size: 28rpx;
          font-weight: 400;
          color: #bbb;
          line-height: 36rpx;
        }

        .pay-money {
          font-size: 44rpx;
          font-weight: 500;
          color: #333;
          line-height: 56rpx;
          margin-left: 24rpx;
        }
      }
    }

    .actually-pay-box {
      margin-top: 74rpx;
      border-top: 2rpx solid #f4f4f4;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding: 40rpx;
      .actually-pay {
        display: flex;
        align-items: center;
        .actually-label {
          ont-size: 28rpx;
          font-weight: 400;
          color: #333;
          line-height: 26rpx;
          margin-right: 16rpx;
        }
        .actually-money {
          ont-size: 32rpx;
          font-weight: 500;
          color: #f74441;
          line-height: 64rpx;
        }
      }
    }
  }
  .content-right {
    flex: 1;
    padding: 0 0rpx 0 40rpx;
    .label-text {
      font-size: 28rpx;
      font-weight: 400;
      color: #333;
      line-height: 36rpx;
      margin-bottom: 20rpx;
    }

    .methods-box {
      display: flex;
      .methods-item {
        position: relative;
        display: flex;
        flex-direction: column;
        flex-wrap: wrap;
        justify-content: center;
        align-items: center;
        width: 49%;
        height: 108rpx;
        background: #fff;
        border-radius: 8rpx;
        border: 2rpx solid #dfe1e6;
        color: #666;
        font-size: 28rpx;
        margin-right: 2%;
        &:last-child {
          margin-right: 0rpx;
        }

        .methods-itme-money {
          font-weight: 400;
          font-size: 24rpx;
          color: #AFB6BE;
          line-height: 32rpx;
          text-align: center;
        }

        .triangle {
          width: 0;
          height: 0;
          border-bottom: 50rpx solid #1157e5;
          border-left: 50rpx solid transparent;
          position: absolute;
          right: 0rpx;
          bottom: 0rpx;

          ::v-deep .u-icon__icon {
            position: absolute;
            font-size: 28rpx;
            left: -30rpx;
            bottom: -66rpx;
            color: #fff;
          }
        }

        .pay-discount {
          position: absolute;
          top: -46rpx;
          right: 0rpx;
          background: #c3cad7;
          padding: 2rpx 8rpx;
          height: 40rpx;
          border-radius: 8rpx 8rpx 0rpx 0rpx;
          color: #fff;
          font-size: 28rpx;
          font-weight: 400;
          display: flex;
          justify-content: center;
          align-items: center;

          .discount-text {
            display: inline-block;
            transform: scale(0.9);
          }
        }

        .pay-discount--active {
          background: #1157e5;
        }
      }
      .methods-item--active {
        border: 2rpx solid #1157e5;
        color: #1157e5;
        view {
          color: #1157e5 !important;
        }
      }
      .methods-item--disabled {
        background: #fafafa !important;
        color: #ccc !important;
      }
    }

    // 线下支付
    .offline-pay-box {
      margin-top: 40rpx;
      padding: 40rpx 40rpx 0rpx 40rpx;
      border: 2rpx solid #f3f3f3;
      border-radius: 8rpx;
      background: #FCFDFE;
      .pay-group {
        display: flex;
        flex-wrap: wrap;
        .pay-item {
          width: 48%;
          margin-bottom: 20rpx;
          border-radius: 8rpx;
          height: 80rpx;
          background: #fff;
          border: 2rpx solid #f0f0f0;
          display: flex;
          align-items: center;
          padding-left: 20rpx;
          box-sizing: border-box;
          font-size: 26rpx;
          &:nth-child(2n) {
            margin-left: 20rpx;
          }
        }

        .cash-logo {
          width: 50rpx;
          height: 50rpx;
          margin-right: 16rpx;
        }

        .pay-item--active {
          background: rgba(17,87,229,0.04);
          border-radius: 4px;
          border: 2rpx solid #F0F0F0;
        }
      }
    }

    // 储值支付
    .stored-box {
      .cus-select {
        margin-top: 40rpx;
      }
      .card-box {
        margin-top: 40rpx;
        height: 374rpx;
        background-repeat: no-repeat;
        background-size: cover;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;

        .balance {
          display: flex;
          align-items: center;
          justify-content: center;
          .unit {
            font-size: 40rpx;
            font-weight: normal;
            color: #ffffff;
            line-height: 48rpx;
            font-size: 40rpx;
            margin-right: 10rpx;
          }
          .stored-money {
            font-size: 80rpx;
            color: #ffffff;
            line-height: 96rpx;
          }
        }

        .belonging-person {
          font-size: 26rpx;
          color: rgba(255, 255, 255, 0.75);
          line-height: 36rpx;
          text-align: right;
          position: absolute;
          right: 32rpx;
          bottom: 32rpx;
        }
      }
    }

    .remark-box {
      margin-top: 40rpx;

      .textarea-box {
        position: relative;
        .cus-textarea {
          box-sizing: border-box;
          width: 100%;
          height: 260rpx;
          background: #FFFFFF;
          border-radius: 8rpx;
          border: 2rpx solid #DFE1E6;
          padding: 16rpx 32rpx;
          font-size: 28rpx;
        }
      }
    }
  }
}
.footer {
  .btn-group {
    display: flex;
  }
}
.ml20 {
  margin-left: 20rpx;
}
.cus-number-input {
  height: 100rpx;
  background: #fff;
  border-radius: 8rpx;
  padding: 32rpx 32rpx 32rpx 54rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  border: 2rpx solid #f3f3f5;
  position: relative;
  &:before {
    position: absolute;
    content: "¥";
    font-size: 28rpx;
    left: 32rpx;
    top: 28rpx;
    color: #333;
    font-weight: 600;
  }
}
.underline-money {
  font-size: 32rpx;
  font-weight: 400;
  color: #aaaaaa;
  line-height: 40rpx;
  text-decoration: line-through;
}
</style>
