<template>
	<view class="uni-combox">
		<view class="uni-combox-dialog" v-if="isDialogInput && showSelector">
			<view class="uni-combox-input">
				<input class="uni-combox__input" type="text"
					:placeholder="selectItem.mobile ? selectItem.mobile : placeholder" v-model="inputVal" @input="onInput"
					@blur="onBlur" :focus="'true'" style="height: 90upx;" />
<!--				<uni-icons style="line-height: 90upx;" class="uni-combox__input-arrow" type="close" size="14"-->
<!--					@click="clearSelector"></uni-icons>-->
				<view class="uni-combox__selector">
					<scroll-view scroll-y="true" class="uni-combox__selector-scroll">
						<view class="uni-combox__selector-empty" v-if="isLoading"><text>数据加载...</text></view>
						<view class="uni-combox__selector-empty" v-if="filterCandidatesLength === 0 && !isLoading">
							<text>{{ emptyTips }}</text>
						</view>
						<view v-if="!isLoading" class="uni-combox__selector-item"
							v-for="(item, index) in filterCandidates" :key="index" @click="onSelectorClick(item)">
							<text>{{ item.value }}</text>
						</view>
						{{filterCandidates}}
					</scroll-view>
				</view>
			</view>
		</view>
		<!-- 	<view v-if="label" class="uni-combox__label" :style="labelStyle">
			<text>{{ label }}</text>
		</view> -->
		<view class="uni-combox__input-box">
			<uni-easyinput v-if="isDialogInput" class="uni-combox__input" type="text" :clearable="false"
				:placeholder="selectItem.mobile ? selectItem.mobile : placeholder" v-model="inputVal" @focus="onFocus" @keyboardheightchange="onKeyBoardChange"  />
			<uni-easyinput v-else class="uni-combox__input" type="text" :clearable="false"
				:placeholder="selectItem.mobile ? selectItem.mobile : placeholder" v-model="inputVal" @focus="onFocus" @keyboardheightchange="onKeyBoardChange"
				@input="onInput" @blur="onBlur" />
			<!-- 		<uni-icons v-if="!showSelector && !inputVal" class="uni-combox__input-arrow" type="arrowdown" size="14"
				@click="toggleSelector"></uni-icons> -->
<!--			<uni-icons v-else class="uni-combox__input-arrow" type="close" size="14" @click="clearSelector"></uni-icons>-->
			<view class="uni-combox__selector" v-if="!isDialogInput && showSelector">
				<scroll-view scroll-y="true" class="uni-combox__selector-scroll">
					<view class="uni-combox__selector-empty" v-if="isLoading"><text>数据加载...</text></view>
					<view class="uni-combox__selector-empty" v-if="filterCandidatesLength === 0 && !isLoading" @click="emptyCreate">
<!--						<text>{{ emptyTips }}</text>-->
            <image src="/static/image/user/warn.png" alt="" style="width: 24px;height: 24px;" />
            <view class="mb10">该用户尚未创建，是否创建新用户？</view>
            <view class="create-button">创建用户</view>
					</view>
					<view v-if="!isLoading" class="uni-combox__selector-item" style="text-align: left;"
						v-for="(item, index) in filterCandidates" :key="index" @click="onSelectorClick(item)">
						<text class="mr20">{{ item.name }}</text>
						<text class="mr20">{{ item.sexText }}</text>
						<text class="mr20">{{ item.ageText }}</text>
						<text>{{ item.mobile }}</text>
					</view>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script>
import S from 'utils/utils'
  // todo 非公共组件，暂时模拟远程搜索
	/**
	 * Combox 组合输入框
	 * @description 组合输入框一般用于既可以输入也可以选择的场景
	 * @tutorial https://ext.dcloud.net.cn/plugin?id=1261
	 * @property {String} label 左侧文字
	 * @property {String} labelWidth 左侧内容宽度
	 * @property {String} placeholder 输入框占位符
	 * @property {Array} candidates 候选项列表
	 * @property {String} emptyTips 筛选结果为空时显示的文字
	 * @property {String} value 组合框的值
	 * @property {String} isDialogInput 弹出框输入和选择避免键盘遮挡
	 */
	export default {
		name: 'uniCombox',
		components: {
		},
		props: {
			label: {
				type: String,
				default: ''
			},
			labelWidth: {
				type: String,
				default: 'auto'
			},
			placeholder: {
				type: String,
				default: ''
			},
			candidates: {
				type: Array,
				default () {
					return [];
				}
			},
			emptyTips: {
				type: String,
				default: '无匹配项'
			},
			value: {
				type: String,
				default: ''
			},
			isDialogInput: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				showSelector: false,
				inputVal: '',
				isLoading: false,
				showDowm: true,
				selectItem: {
					key: '',
					value: ''
				}
			};
		},
		computed: {
			labelStyle() {
				if (this.labelWidth === 'auto') {
					return {};
				}
				return {
					width: this.labelWidth
				};
			},
			filterCandidates() {
				return this.candidates.filter(item => {
					return item;
				});
			},
			filterCandidatesLength() {
				return this.filterCandidates.length;
			}
		},
		watch: {
			value: {
				handler(newVal) {
					if (newVal) {
						this.candidates.filter(item => {
							if (this.value === item.key) {
								this.selectItem = item;
								this.inputVal = item.value;
							}
						});
					} else {
						this.selectItem = {
							key: '',
							value: ''
						}
						this.inputVal = ''
					}
				},
				immediate: true
			},
			candidates: {
				handler(newVal) {
					this.isLoading = false;
				},
				immediate: true
			}
		},
		methods: {
			toggleSelector() {
				this.showSelector = !this.showSelector;
			},
			clearSelector() {
				alert(22)
				this.selectItem = {
					key: '',
					value: ''
				};
				this.inputVal = '';
				this.showSelector = false;
				this.isLoading = false;
				this.$emit('clear');
			},
			onFocus() {
				this.showSelector = true;
				if (this.inputVal) {
					this.inputVal = '';
				}
			},
			onBlur() {
				setTimeout(() => {
					if (!S.isEmptyObject(this.selectItem)) {
						const selectItem = JSON.parse(JSON.stringify(this.selectItem));
						this.inputVal = selectItem.mobile;
					} else {
						this.inputVal = '';
						this.$emit('input','')
					}
					this.showSelector = false;
				}, 200);
			},
			onSelectorClick(item) {
				this.selectItem = item;
				this.inputVal = item.mobile;
				this.showSelector = false;
				this.$emit('select', item.key, item);
			},
			onInput() {
				setTimeout(() => {
					this.$emit('input', this.inputVal);
					// this.isLoading = !!this.inputVal;
				});
			},
      onKeyBoardChange(val){
        this.$emit('keyBoardChange',val.target)

      },
      emptyCreate(){
        this.$emit('emptyCreate', this.inputVal)
      },
		}
	};
</script>

<style lang="scss" scoped>
	.uni-combox {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		// height: 40upx;
		flex-direction: row;
		align-items: center;
		// border-bottom: solid 1px #DDDDDD;
	}

	.uni-combox__label {
		font-size: 28upx;
		line-height: 40upx;
		padding-right: 10px;
		color: #999999;
	}

	.uni-combox__input-box {
		position: relative;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex: 1;
		flex-direction: row;
		align-items: center;
	}

	.uni-combox__input {
		flex: 1;
		font-size: 28upx;
	}

	.uni-combox__input-arrow {
		padding-left: 15upx;
		padding-right: 15upx;
	}

	.uni-combox__selector {
		box-sizing: border-box;
		position: absolute;
		top: 50px;
		left: 0;
		width: 100%;
		background-color: #ffffff;
		border-radius: 6px;
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
		z-index: 2;
	}

	.uni-combox__selector-scroll {
		max-height: 350upx;
		box-sizing: border-box;
	}

	.uni-combox__selector::before {
		content: '';
		position: absolute;
		width: 0;
		height: 0;
		border-bottom: solid 6px #ffffff;
		border-right: solid 6px transparent;
		border-left: solid 6px transparent;
		left: 50%;
		top: -6px;
		margin-left: -6px;
	}

	.uni-combox__selector-empty,
	.uni-combox__selector-item {
		/* #ifdef APP-NVUE */
		display: flex;
		/* #endif */
		//line-height: 70upx;
    padding: 8px 0;
		font-size: 28upx;
		text-align: center;
		// border-bottom: solid 1px #dddddd;
		margin: 2px 10px;
    cursor: pointer;
	}

	.uni-combox__selector-empty:last-child,
	.uni-combox__selector-item:last-child {
		border-bottom: none;
	}

	.uni-combox-dialog {
		background: #cccccc7d;
		position: absolute;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
		z-index: 100;
	}

	.uni-combox-input {
		background: #fff;
		display: -webkit-box;
		display: -webkit-flex;
		display: flex;
		height: 90upx;
	}

	.mr20{
		margin-right: 20rpx;
	}
  .create-button{
    width: 208px;
    border-radius: 16px;
    border: 1px solid #155BD4;
    color: #155BD4;
    padding: 5px 0;
    margin: 5px auto 0;
  }
</style>
