<template>
  <view>
    <cus-drawer v-model="drawerVisible" :width="720" :title="title" :maskClick="false" footerHide @on-open="onOpen" @on-change="change" @on-close="onClose">
      <view slot="content" class="content">
        <view class="tongue-container">
          <view class="tongue-example">
            <view class="example-title">正常舌象</view>
            <view>
              <img
                @click="previewImage(tongueExample.upper + '-B.w300')"
                :src="tongueExample.upper"
              />
              <view class="tongue-box" style="margin-bottom: 96rpx">
                <view class="tongue-desc">
                  <view class="tongue-label">舌面</view>
                  <view class="tongue-content">淡红舌薄白苔</view>
                </view>
              </view>

              <img
                @click="previewImage(tongueExample.lower + '-B.w300')"
                :src="tongueExample.lower"
              />
              <view class="tongue-box">
                <view class="tongue-desc">
                  <view class="tongue-label">舌底</view>
                  <view class="tongue-content">舌底青筋正常</view>
                </view>
              </view>
            </view>
          </view>
          <scroll-view scroll-y class="time-line" @scrolltolower="tongueScroll">
            <view class="step-box">
              <view class="step-item" v-for="(list_item, index) in combination_list" :key="index">
                <view class="dot"></view>
                <view class="timeline-left">
                  <uni-dateformat :date="list_item.create_time * 1000" format="yyyy-MM-dd"></uni-dateformat>
                  <view class="current-text" v-show="list_item.is_current_inquiry">当前问诊</view>
                </view>
                <view class="timeline-right">
                  <view v-if="!list_item.tongue_back && !list_item.tongue_front" class="empty-box">本次问诊无舌像记录</view>
                  <view v-else>
                    <view class="tongue-images">
                      <img
                        v-show="list_item.tongue_front"
                        :src="list_item.tongue_front+'-B.w300'"
                        @click="previewImage(list_item.tongue_front)"
                      />
                      <img
                        v-show="list_item.tongue_back"
                        :src="list_item.tongue_back+'-B.w300'"
                        @click="previewImage(list_item.tongue_back)"
                      />
                    </view>
                    <view class="tongue-box" style="margin-bottom: 32rpx">
                      <view class="tongue-desc">
                        <view class="tongue-label">舌象</view>
                        <view class="tongue-content">
                          <view class="tongue-item">
                            {{ getDesc(list_item.tongue_symptoms) }}
                          </view>
                        </view>
                      </view>
                      <view class="tongue-desc">
                        <view class="tongue-label">脉象</view>
                        <view class="tongue-content">
                          <view class="tongue-item">
                            {{ getDesc(list_item.pulse_symptoms) }}
                          </view>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </scroll-view>
        </view>
        <c-loading :show="tableLoading" />
      </view>
    </cus-drawer>
  </view>
</template>

<script>
import cusDrawer from '@/components/cus-drawer/index.vue'
import ConsultationService from "@/api/consultation";
import CLoading from "../cLoading/loading.vue";
const init_formdata = {
  page: 1,
  page_size: 1000,
}
export default {
  name: 'tongueCompareDrawer',
  components: {
    CLoading,
    cusDrawer,
  },
  inject: ['getVm'],
  mixins: [],
  props: {
    value: {
      type: Boolean,
      default: () => false
    },
    title: {
      type: String,
      default: '舌象智能对比'
    },
    mr_id: {
      type: [Number, String],
      default: ''
    },
    last_mr_id: {
      type: [Number, String],
      default: ''
    },
  },
  data() {
    return {
      formData: {...init_formdata},
      drawerVisible: false,
      tableLoading: false,
      current_tongue_ai: [],
      tongueExample: {
        upper: 'https://static.rsjxx.com/image/2025/0609/173943_33559.png',
        lower: 'https://static.rsjxx.com/image/2025/0609/173943_78082.png'
      },
      tongue_list: [],
      total: 0,
      mr: {},
      currentData: {}
    };
  },
  computed: {
    getDesc () {
      return list => {
        return list.join('，') || '-'
      }
    },
    combination_list () {
      return [...this.current_tongue_ai, ...this.tongue_list]
    }
  },
  watch: {
    value (val) {
      if ( val ) {
        this.drawerVisible = true
      }else{
        this.drawerVisible = false
      }
    },
  },
  created() {},
  mounted() {},
  methods: {
    getList () {
      this.tableLoading = true;
      let params = {
        page_size: this.formData.page_size,
        page: this.formData.page,
        pt_id: this.mr?.pt_id
      }
      ConsultationService.getTongueDiff(params).then( res => {
        this.tongue_list = this.tongue_list.concat(res.list)
        this.total = res.total
      } ).finally(() => {
        this.tableLoading = false;
      })
    },
    tongueScroll (e) {
      if ( this.tongue_list.length < this.total ) {
        this.formData.page++
        this.getList()
      }
    },
    change (val) {
      if (val) {
        this.initData()
      }else{
        this.clearData()
      }
    },
    initData () {
      this.$nextTick(() => {
        this.mr = this.getVm().mr || {}
        this.currentData = this.getVm().$refs?.userProfile?.tongue_image || {}
        this.getList()
        this.getCurrentPhotoList()

      })
    },
    getCurrentPhotoList () {
      const { lower_image, on_image } = this.currentData?.tongue_ai;
      this.current_tongue_ai = [
        {
          is_current_inquiry: true,
          create_time: this.mr.create_time,
          tongue_back: lower_image || '',
          tongue_front: on_image || '',
          tongue_symptoms: [],
          pulse_symptoms: [],
        }
      ]
    },

    previewImage(imgUrl) {
      if (!imgUrl) return
      uni.previewImage({
        urls: [imgUrl]
      })
    },
    confirm () {
      this.finish()
    },
    onOpen () {

    },
    onClose () {
      this.closeDrawer()
    },
    closeDrawer () {
      this.$emit('input', false)
    },
    clearData () {
      this.formData = init_formdata
      this.total = 0
      this.tongue_list = []
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  height: calc(100vh - 200rpx);
  .tongue-container {
    display: flex;
    height: 100%;
    .tongue-example {
      background: #F9FAFB;
      border-radius: 8rpx;
      padding: 32rpx;
      box-sizing: border-box;
      img {
        width: 288rpx;
        height: 256rpx;
        margin-bottom: 24rpx;
      }
      .example-title {
        font-weight: 500;
        font-size: 28rpx;
        color: rgba(0,0,0,0.75);
        line-height: 40rpx;
        margin-bottom: 48rpx;
      }
    }
    .tongue-box {
      padding: 16rpx 24rpx;
      background: rgba(235,239,248,0.5);
      border-radius: 8rpx;
      .tongue-desc {
        display: flex;
        align-items: flex-start;
        .tongue-label {
          width: 52rpx;
          min-width: 52rpx;
          height: 36rpx;
          font-weight: 300;
          font-size: 26rpx;
          color: #999999;
        }
        .tongue-content {
          margin-left: 24rpx;
          flex: 1;
          display: flex;
          flex-wrap: wrap;
          font-weight: 400;
          font-size: 26rpx;
          color: #333333;
          line-height: 36rpx;
        }
      }
    }
    .step-box {
      height: 100%;
      padding: 20rpx 0rpx 0 190rpx;
      display: flex;
      flex-direction: column;
      margin-left: 48rpx;

      .step-item {
        border-left: 2rpx dashed #EBEDF0;
        position: relative;
        padding: 4rpx 0rpx 0rpx 24rpx;
        &:last-child {
          border-left: none;
        }
        .dot {
          width: 16rpx;
          height: 16rpx;
          border-radius: 50%;
          position: absolute;
          top: 0rpx;
          left: -8rpx;
          background: #EAEAEA;
        }
        //左侧时间
        .timeline-left {
          font-size: 24rpx;
          width: 188rpx;
          position: absolute;
          left: -200rpx;
          top: -14rpx;
          margin-bottom: 8rpx;
          font-weight: 500;
          font-size: 28rpx;
          color: #333333;
          line-height: 40rpx;
          .current-text {
            font-weight: 400;
            font-size: 24rpx;
            color: #999999;
            line-height: 32rpx;
            text-align: right;
            padding-right: 24rpx;
          }
        }
        .timeline-right {
          .empty-box {
            height: 136rpx;
            background: #F9F9FA;
            border-radius: 8rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 64rpx;
            font-weight: 400;
            font-size: 24rpx;
            color: #AAAAAA;
            line-height: 32rpx;
          }
          .tongue-images {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16rpx;
            img {
              border-radius: 8rpx;
              width: 360rpx;
              height: 320rpx;
              object-fit: cover;
            }
          }
        }
      }
    }
  }
}
.tongue-img {
  width: 360rpx;
  height: 320rpx;
}
</style>
