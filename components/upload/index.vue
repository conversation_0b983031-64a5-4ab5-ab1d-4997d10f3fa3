<template>
  <div>
    <view v-if="disabled && !fileList.length" class="empty">暂无数据</view>
    <u-upload
      v-else
      accept="image"
      class="cus-upload"
      @afterRead="afterRead"
      @delete="deletePic"
      @oversize="oversize"
      :fileList="fileList"
      name="file"
      width="160"
      height="160"
      :multiple="multiple"
      :maxSize="getMaxSize"
      :maxCount="maxCount"
      :disabled="disabled"
      :deletable="!disabled"
      >
      <slot></slot>
    </u-upload>
  </div>
</template>

<script>
import {cloneDeep} from "lodash";
import ConsultationService from "@/api/consultation";
import utils from "@/utils/utils";
export default {
  name: 'upload',
  components: {},
  mixins: [],
  props: {
    multiple: {
      type: Boolean,
      default: true,
    },
    // 默认多选，9张
    maxCount: {
      type: [String, Number],
      default: 9
    },
    // 默认限制3M
    maxSize: {
      type: Number,
      default: 3,
    },
    //  是否禁用
    disabled: {
      type: [Boolean, String],
      default: false,
    },
    // 绑定的图片数据
    value: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      fileList: [],
      domain: '',
    };
  },
  computed: {
    getMaxSize() {
      return this.maxSize * 1024 * 1024;
    },
  },
  watch: {
    value: {
      immediate: true,
      handler(val) {
        let list = cloneDeep(val)
        this.fileList = []
        list.forEach(item => {
          this.fileList.push({
            url: item
          })
        })
      },
    },
    fileList(val) {
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    oversize (val) {
      uni.showToast({
        title: `图片大小限制${this.maxSize}M`,
        icon: 'none'
      })
    },
    updateList() {
      let list = []
      this.fileList.forEach(item => {
        list.push(item.url)
      })
      this.$emit('input', list)
    },
    // 删除图片
    deletePic(event) {
      this.fileList.splice(event.index, 1);
      this.updateList()
    },
    // 新增图片
    async afterRead(event) {
      // 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
      let lists = [].concat(event.file);
      let fileLength = this.fileList.length
      lists.map((item) => {
        this.fileList.push({
          ...item,
          status: "uploading",
          message: "上传中",
        });
      });
      for (let i = 0; i < lists.length; i++) {
        let ext = lists[i]?.name?.lastIndexOf(".") > 0
          ? lists[i]?.name?.substring(lists[i]?.name?.lastIndexOf(".") + 1, lists[i]?.name?.length)
          : ''
        console.log("=>(index.vue:127) lists[i].url", lists[i].url);
        this.getToken(ext).then(async (res) => {
          const result = await this.uploadFilePromise({url: lists[i].url, token: res.token, key: res.key});
          let item = this.fileList[fileLength];
          if ( result.key ) {
            this.fileList.splice(fileLength,1,
              Object.assign(item, {
                status: "success",
                message: "",
                url: `${this.domain}/${result.key}`,
              }))
            fileLength++
          }else{
            this.fileList.splice(fileLength,1)
          }
          this.updateList()
        })
      }
    },
    getToken(ext) {
      return new Promise((resolve, reject) => {
        let params = {
          ext,
          random: utils.random(6)
        }
        ConsultationService.getUpToken(params).then(res => {
          this.domain = res.domain
          resolve(res)
        })
      })
    },
    uploadFilePromise({url, token, key}) {
      console.log("=>(index.vue:159) url", url);
      return new Promise((resolve, reject) => {
        uni.uploadFile({
          url: "https://upload.qiniup.com",
          filePath: url,
          name: "file",
          formData: {
            token,
            key
          },
          success: (res) => {
            setTimeout(() => {
              resolve(JSON.parse(res.data || '{}'));
            }, 0);
          },
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.cus-upload {
  ::v-deep .u-upload__button {
    width: 160rpx !important;
    min-width: 160rpx !important;
    height: 160rpx !important;

    uni-text {
      font-size: 60rpx !important;
    }
  }

  ::v-deep .u-upload__deletable {
    top: -8rpx;
    right: -10rpx;
    width: 32rpx;
    height: 32rpx;
    background: #999;
    border-radius: 50%;
  }

  ::v-deep .u-upload__deletable__icon {
    uni-text {
      color: #fff;
      font-size: 34rpx !important;
    }

    .uicon-close {
      top: 16rpx !important;
      right: -3rpx;
    }
  }
}

::v-deep .u-upload__wrap__preview, .u-upload__wrap__preview__image {
  overflow: unset;
}
.empty {
  width: 160rpx;
  height: 160rpx;
  border: 2rpx dashed #EBEDF0;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24rpx;
  background: #FAFAFB;
  color: #ae9c9c;
  margin-bottom: 30rpx;
  border-radius: 8rpx;
}
</style>
