<template>
  <view class="cus-remote-box">
    <uni-easyinput v-model="keyword" placeholder="请输入用户姓名、手机号进行搜索" suffixIcon="search"
      @input="input" @focus="focus" @blur="blur"
    ></uni-easyinput>
    <view class="selector-box" v-if="isFocus">
      <view class="search-loading" v-if="searchLoading"><text>数据加载...</text></view>
      <view class="empty-box" v-else-if="!users.length"><text>无匹配数据</text></view>
      <scroll-view scroll-y="true" v-else class="scroll-box">
        <view class="scroll-item"  @click="itemChange(item)" v-for="(item, index) in users" :key="index">
          <view class="autocomplete" style="white-space: pre-wrap">
            <view class="avatar-box">
              <img v-if="item.vip_info.length > 0 && getVipIcon(item.vip_info)" class="vip-icon" :src="getVipIcon(item.vip_info)" />
              <img
                class="avatar-icon"
                :src="item.avatar || default_avatar(item)"
              />
            </view>
            <view class="info-content">
              <span class="name">{{ item.patient_name }}</span>
              <span class="info">
                <span>{{ item.sex_text }}</span>
                <span style="margin: 0 12rpx;" v-if="item.age && item.sex_text">|</span>
                <span>{{ item.age ? `${item.age}岁` : '' }}</span>
              </span>
              <span class="mobile">{{ item.mobile }}</span>
              <span class="stage-mobile-box" v-if="item.show_staging_mobile === '1'">
                <span class="stage-icon">暂存</span>
                <span class="stage-mobile">{{ item.staging_mobile }}</span>
              </span>
            </view>
          </view>
<!--            <view class="flex flex-item-between flex-item-align" @click.stop="creatConsumer" v-else>-->
<!--              <p class="flex flex-c">-->
<!--                <span>{{ nickname }}</span>-->
<!--                <span class="tip">尚无该用户</span>-->
<!--              </p>-->
<!--              <a>创建用户</a>-->
<!--            </view>-->
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
import { debounce } from "lodash";
import productService from '@/api/product'
import S from '@/utils/utils'
export default {
  name: 'user-select-remote',
  components: {},
  mixins: [],
  props: {
  },
  data() {
    return {
      keyword: '',
      isFocus: false,
      users: [],
      searchLoading: false,
    };
  },
  computed: {
    default_avatar () {
      return item => {
        return S.defaultAvatar(item?.sex)
      }
    },
    getVipIcon() {
      const typeEnum = {
        1: {
          desc: 'vip_980',
          url: 'https://static.rsjxx.com/image/2025/0607/161825_62294.png',
        },
        4: {
          desc: 'vip_298，榕粉会员',
          url: 'https://static.rsjxx.com/image/2025/0607/161825_62294.png',
        },
        // 3: {
        //   desc: 'vip_9800',
        //   url: 'https://static.rsjxx.com/image/2025/0607/161825_65262.png'
        // }
      };
      return vipInfo => {
        const user_type = vipInfo[vipInfo.length - 1]?.user_type;
        if (user_type in typeEnum) {
          return typeEnum[user_type].url
          // return require(`@/static/image/order/${typeEnum[user_type]}.png`);
        }
        return '';
      };
    },
  },
  watch: {},
  created() {
    this.getUserList()
  },
  mounted() {},
  methods: {
    itemChange (item) {
      this.$emit('on-change', item)
    },
    input (val) {
      this.getUserList()
    },
    focus () {
      this.isFocus = true
    },
    blur () {
      setTimeout(() => {
        this.isFocus = false
      },300)
    },
    getUserList: debounce( function () {
      let params = {
        keyword: this.keyword
      }
      this.searchLoading = true
      productService.getUserUserList(params).then( res => {
        this.users = res.users || []
      } ).finally(() => {
        this.searchLoading = false
      })
    }, 200 ),
  },
};
</script>

<style lang="scss" scoped>
.cus-remote-box {
  position: relative;
  width: 832rpx;
  .selector-box {
    &::before {
      content: '';
      position: absolute;
      width: 0;
      height: 0;
      border-bottom: solid 12rpx #fff;
      border-right: solid 12rpx transparent;
      border-left: solid 12rpx transparent;
      left: 50%;
      top: -12rpx;
      margin-left: -12rpx;
    }

    .search-loading, .empty-box {
      height: 80rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #c5c8ce;
      font-size: 26rpx;
    }

    box-shadow: 0rpx 6rpx 20rpx 0rpx rgba(0,0,0,0.1);
    border-radius: 8rpx;
    position: absolute;
    background: #fff;
    //background: #ccc;
    width: 100%;
    top: 100rpx;
    left: 0rpx;

    .scroll-box {
      height: 400rpx;
      .scroll-item {
        padding: 16rpx 32rpx
      }
    }
  }
}
</style>

<style lang="scss" scoped>
.autocomplete {
  display: flex;
  align-items: center;
  .avatar-box {
    box-sizing: content-box;
    width: 64rpx;
    min-width: 64rpx;
    height: 64rpx;
    border-radius: 50%;
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    .vip-icon {
      width: 68rpx;
      min-width: 60rpx;
      height: 28rpx;
      position: absolute;
      bottom: -12rpx;
      left: -8rpx;
      z-index: 2;
    }
    .avatar-icon {
      width: 64rpx;
      height: 64rpx;
      border-radius: 50%;
      position: absolute;

    }
  }

  .info-content {
    margin-left: 24rpx;
    .name {
      font-weight: 600;
      font-size: 28rpx;
      color: #333333;
      line-height: 40rpx;
    }

    .info {
      margin-left: 24rpx;
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      line-height: 36rpx;
    }

    .mobile {
      margin-left: 16rpx;
      font-weight: 400;
      font-size: 26rpx;
      color: #606266;
      line-height: 36rpx;
    }
    .stage-mobile-box {
      margin-left: 24rpx;
      .stage-icon {
        position: relative;
        top: 0rpx;
        padding: 2rpx 8rpx;
        background: #FFF3DF;
        border-radius: 4rpx;
        font-weight: 400;
        font-size: 24rpx;
        color: #FFA300;
        line-height: 36rpx;
        //transform: scale(0.8);
      }
      .stage-mobile {
        margin-left: 12rpx;
        font-weight: 400;
        font-size: 26rpx;
        color: #333333;
        line-height: 36rpx;
      }
    }
  }
}
</style>
