import Vue from 'vue'
import App from './App'
import store from './store'
import globalMixin from './mixins/global'
import * as tools from './utils/utils'
import config from './config'
import './utils/interceptors'  // 引入全局拦截器
// import './uni.promisify.adaptor.js'
import uView from '@/uni_modules/uview-ui'

Vue.use(uView)
uni.$u.setConfig({
	// 修改$u.config对象的属性
	config: {
		// 修改默认单位为rpx，相当于执行 uni.$u.config.unit = 'rpx'
		unit: 'rpx'
	},
})
// 注册全局混入
Vue.mixin(globalMixin)

// 挂载全局工具函数
Vue.prototype.$tools = tools

// 挂载全局配置
Vue.prototype.$config = config


Vue.config.productionTip = false

App.mpType = 'app'

const app = new Vue({
  store,
  ...App
})
app.$mount()
