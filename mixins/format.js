export default {
  computed: {
    /**
     * 格式化金额
     * @param {number|string} price - 需要格式化的金额
     * @param {string} [prefix='¥'] - 货币符号
     * @param {number} [decimals=2] - 小数位数
     * @returns {string} 格式化后的金额
     */
    formatPrice() {
      return (price, prefix = '￥', decimals = 2) => {
        if (price === '' || price === null || price === undefined) {
          return '-'
        }

        // 将输入转换为字符串并处理科学计数法
        let numStr = String(price)
        if (numStr.includes('e')) {
          price = Number(price).toFixed(decimals)
          numStr = String(price)
        }

        // 分离整数部分和小数部分
        const [intPart, decimalPart = ''] = numStr.split('.')

        // 处理整数部分（可选：添加千分位分隔符）
        const formattedIntPart = intPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',')

        // 处理小数部分
        let formattedDecimalPart = decimalPart

        // 如果小数部分长度超过指定位数，需要进行舍入
        if (formattedDecimalPart.length > decimals) {
          // 获取需要进行舍入判断的数字
          const nextDigit = Number(formattedDecimalPart[decimals])

          // 截取指定位数的小数
          formattedDecimalPart = formattedDecimalPart.slice(0, decimals)

          // 如果后一位大于等于5，需要进行进位
          if (nextDigit >= 5) {
            const num = Number(`${intPart}.${formattedDecimalPart}`)
            const multiplier = Math.pow(10, decimals)
            const roundedNum = Math.round((num + 1/multiplier) * multiplier) / multiplier

            // 重新分离整数和小数部分
            const [newIntPart, newDecimalPart = ''] = String(roundedNum).split('.')
            return `${prefix}${newIntPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}${decimals > 0 ? '.' + newDecimalPart.padEnd(decimals, '0') : ''}`
          }
        }

        // 补齐小数位数
        formattedDecimalPart = formattedDecimalPart.padEnd(decimals, '0')

        // 组合最终结果
        return `${prefix}${formattedIntPart}${decimals > 0 ? '.' + formattedDecimalPart : ''}`
      }
    },

    /**
     * 格式化金额（不带货币符号）
     * @param {number|string} price - 需要格式化的金额
     * @param {number} [decimals=2] - 小数位数
     * @returns {string} 格式化后的金额
     */
    formatPriceWithoutSymbol() {
      return (price, decimals = 2) => {
        return this.formatPrice(price, '', decimals)
      }
    }
  }
}
