export default {
  data() {
    return {
      // 页面加载状态
      pageLoading: false,
      // 下拉刷新状态
      refreshing: false
    }
  },
  methods: {
    // 显示加载
    showLoading(title = '加载中') {
      uni.showLoading({
        title,
        mask: true
      })
    },
    // 隐藏加载
    hideLoading() {
      uni.hideLoading()
    },
    // 显示提示
    showToast(title, icon = 'none') {
      uni.showToast({
        title,
        icon
      })
    }
  }
} 