{
	"pages": [
		// pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/login/index",
			"style": {
				"navigationBarTitleText": "登录",
				"navigationStyle": "custom",
				"disableScroll": true
			}
		},
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "在线问诊",
				"navigationStyle": "custom",
				"disableScroll": true,
        "enablePullDownRefresh": false
			}
		}
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "医疗问诊",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8",
    "pageOrientation": "landscape",
    "app-plus": {
      "animationType": "none",
      "bounce": "none",
      "animationDuration": 300
    }
  },
	"uniIdRouter": {},
	"condition" : { //模式配置，仅开发期间生效
		"current": 0, //当前激活的模式(list 的索引项)
		"list": [
			{
				"name": "", //模式名称
				"path": "", //启动页面，必选
				"query": "" //启动参数，在页面的onLoad函数里面得到
			}
		]
	},
  "rpxCalcMaxDeviceWidth": 1080
}
