<template>
<div>
  <view class="main-wrapper">
    <view class="main-header">
      <view class="header-left">
        <uni-icons :size="24" type="bars" @click="openConsultDrawer(false)"></uni-icons>
      </view>
    </view>

    <view class="main-content">
      <view class="empty-box">
        <view class="empty-tip">当前没有正在进行中的咨询单</view>
        <button type="primary" icon="el-icon-plus" size="small" @click="openConsultDrawer(true)">+ 建单</button>
      </view>
    </view>


    <consult-drawer ref="consultDrawer"></consult-drawer>
    <!-- 完成咨询 -->
  </view>
</div>
</template>

<script>

import ConsultDrawer from "@/pages/ai-consult/components/consult-drawer/index.vue";

export default {
  name: "Empty",

  components: {ConsultDrawer},
  props: {},
  data() {

    return {};
  },
  computed: {},
  watch: {},
  created() {
  },
  mounted() {
  },
  methods: {
    openConsultDrawer(openDialog = false) {
      this.$refs.consultDrawer.showDrawer(openDialog)
    },
  },


};
</script>
<style lang="scss" scoped>
.main-wrapper {
  height: 100%;

  .main-header {
    display: flex;
    justify-content: space-between;
    position: sticky;
    top: 0;
    background: $bg-color-page;
    padding-top: $spacing-base;
    padding-bottom: $spacing-base;
    z-index: 900;

    .header-left {
      @include flex();

      .user-info {
        margin-left: 32rpx;
        @include flex();
        background: $bg-color-white;
        padding: 20rpx;
        border-radius: $border-radius-base;

        .user-name {
          font-size: 28rpx;
          color: #333333;
          line-height: 44rpx;
          margin: 0 16rpx;
        }

        .user-age {
          font-size: 24rpx;
          color: #AAAAAA;
          line-height: 36rpx;
        }
      }
    }

    .header-right {
      @include flex();

      .build-person {
        font-weight: 400;
        font-size: 28rpx;
        color: #AAAAAA;
        line-height: 44rpx;
        background: #ffffffcc;
        padding: 14rpx 32rpx;
      }
    }
  }

  .main-content {
    position: relative;
    height: calc(100% - 124rpx);
    .empty-tip{
      font-size: 28rpx;
      color: #999999;
      margin-bottom: 20rpx;
    }
  }
}
</style>
