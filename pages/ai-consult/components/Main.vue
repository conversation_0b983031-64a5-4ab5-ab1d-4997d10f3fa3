<template>
  <view class="main-wrapper">
    <view class="main-header">
      <view class="header-left">
        <uni-icons :size="24" type="bars" @click="openConsultDrawer"></uni-icons>
        <uni-tooltip content="" placement="bottom" class="cus-tooltip" style=" margin-left: 32rpx;">
          <view slot="content" style="min-width: 120px;">
            <view class="build-person">
              <view class="person-label">建单人：</view>
              <view class="person-value">{{ mr.creator && mr.creator.name || '-' }}</view>
            </view>
          </view>
          <view class="user-info">
              <img v-if="userInfo.avatar" :src="userInfo.avatar" style="width: 20px;height:20px;border-radius: 50%" >
              <uni-icons v-else :size="16" type="person" color="#CCCCCC"></uni-icons>
              <view class="user-name">{{ userInfo.user_name }}</view>
              <view class="user-age">{{ userInfo.sex === '2' ? '女' : '男' }} {{ userInfo.age }}岁</view>
          </view>
        </uni-tooltip>
      </view>
      <view class="header-right">
        <template v-if="!isReadOnly">
          <button @click="deleteAdvisory" class="ui-btn-base">删除咨询</button>
          <button @click="finishAdvisory" class="ui-btn-base ui-btn-primary ui-btn-distance-l" :loading="consultLoading" type="primary">完成咨询</button>
        </template>
        <button v-if="consultationStatus === 'PRESCRIBED' && +order_id" @click="pay" class="ui-btn-base ui-btn-primary" style="width: 200rpx" type="primary">收款</button>
      </view>
    </view>

    <view class="main-content">
      <scroll-view scroll-y style="height: 100%;" :scroll-into-view="intoView" scroll-with-animation>
        <user-profile :draftData="record_info" :mr="mr" ref="userProfile" :isReadOnly="isReadOnly"></user-profile>
        <!-- <treatment-item /> -->
        <treatment-goods :scrollToBottom="scrollToBottom" :draftData="treat_goods" :mr_id="mr_id"
          :isReadOnly="isReadOnly" ref="treatmentGoods"></treatment-goods>
        <view style="height: 200rpx;" id="scroll-view-bottom"></view>
      </scroll-view>
    </view>

    <consult-drawer ref="consultDrawer"></consult-drawer>
    <confirm-dialog ref="deleteConfirm" title="删除咨询" message="确定要删除该咨询记录吗？删除后将无法恢复"
      @confirm="handleDeleteConfirm"></confirm-dialog>

    <!-- 完成咨询 -->
    <finish-consult v-model="consultVisible" @success="getOrderId" :goodsList="store_treat_goods.list"
      :record_info="store_record_info" :mr_id="mr_id"></finish-consult>
    <!-- 支付 -->
    <pay-drawer v-model="payVisible" :order_id="order_id" @close="payChange" @success="paySuccess"></pay-drawer>
    <!-- 不添加商品完成咨询 -->
    <confirm-dialog ref="endConfirm" title="" cancelText="我再想想" confirmText="仅作为咨询提交" message="本次咨询并未添加相关商品或理疗项目，是否仅作为咨询记录进行提交？"
                    @confirm="endConsult"></confirm-dialog>
  </view>
</template>

<script>
import UserProfile from "@/pages/ai-consult/components/user-profile/index.vue";
import ConsultDrawer from "@/pages/ai-consult/components/consult-drawer/index.vue";
import TreatmentGoods from "@/pages/ai-consult/components/treatment-goods/index.vue";
import finishConsult from '@/components/finish-consult/index.vue'
import utils from "@/utils/utils";
import ConsultationService from "@/api/consultation";
import { mapState } from 'vuex';
import payDrawer from '@/components/pay-drawer/index.vue'

import ConfirmDialog from '@/components/confirm-dialog/index.vue';
import { debounce } from "lodash";
import TreatmentItem from "./treatment-item/index.vue";

export default {
  name: 'Main',
  components: {
    TreatmentItem,
    payDrawer,
    UserProfile, ConsultDrawer,
    TreatmentGoods,
    finishConsult, ConfirmDialog
  },
  data() {
    return {
      userInfo: {
        user_name: '',
        age: ''
      },
      show: false,
      mr: {},
      mr_id: '',
      intoView: '',
      consultVisible: false,
      record_info: {},
      treat_goods: {
        list: []
      },
      order_id: '',
      payVisible: false,
      consultationStatus: '',
      consultLoading: false,
    }
  },
  computed: {
    ...mapState('consultation', {
      store_record_info: state => state.record_info,
      store_treat_goods: state => state.treat_goods
    }),
    isReadOnly() {
      return ['PRESCRIBED', 'FINISHED', 'CLOSED'].includes(this.consultationStatus)
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init () {
      const query = this.$store.getters['global/getQuery']
      if (query.mr_id) {
        this.mr_id = query.mr_id;
        this.getRecordInfo(query.mr_id, query.pt_id);
        this.getDraftData()
      }
    },
    // 关闭支付弹窗
    payChange: debounce(function () {
      this.init()
    }, 400),
    paySuccess () {
      this.$store.commit('global/SET_QUERY', { menuId: '0' })
    },
    //创建生成订单id
    getOrderId(val) {
      this.order_id = val
      this.payVisible = true
    },
    pay () {
      this.payVisible = true
    },
    getDraftData() {
      if (!this.mr_id) return
      ConsultationService.getDraftData({ mr_id: this.mr_id }).then(res => {
        if (res.has_draft === '1') {
          this.record_info = res.data.record_info;
          this.treat_goods = res.data.treat_goods;
          uni.$emit('handleUpdateAiAnswerInfo', {
            record_info: res.data.record_info,
            mr_id: this.mr_id,
          });
        }else {
          this.$store.commit('consultation/UPDATE_SAVE_STATUS', true)
        }
      })
    },
    scrollToBottom() {
      this.intoView = ''
      setTimeout(() => {
        this.intoView = 'scroll-view-bottom'
      }, 300)
    },
    getRecordInfo(mr_id, pt_id) {
      if (!mr_id) return
      ConsultationService.getRecordInfo({ mr_id, pt_id }).then(res => {
        this.mr = res.mr;
        this.userInfo = res.mr.patient;
        this.consultationStatus = res.mr.status;
        this.order_id = res?.mr?.shop_order_id || ''
        uni.$emit('getRecordStatus', this.isReadOnly)
      })
    },
    /**
     * 无商品的咨询
     */
    endConsult () {
      let params = {
        mr_id: this.mr_id,
        ...this.store_record_info,
      }
      this.consultLoading = true
      ConsultationService.getPresSavefinish(params).then( res => {
        this.consultLoading = false
        this.$store.commit('global/SET_QUERY', { menuId: '0' })
      } ).finally(() => this.consultLoading = false)
    },
    finishAdvisory() {
      if (this.$refs.userProfile.validateForm() && !this.$refs.treatmentGoods.goodsList?.length) {
        this.$refs.endConfirm.open();
        return
      }
      if (this.$refs.userProfile.validateForm() && this.$refs.treatmentGoods.validateForm()) {
        this.consultVisible = true;
      }
    },
    openConsultDrawer() {
      this.$refs.consultDrawer.showDrawer()
    },
    deleteAdvisory() {
      this.$refs.deleteConfirm.open();
    },
    handleDeleteConfirm() {
      if (!this.mr_id) return
      ConsultationService.deleteRecord({
        mr_id: this.mr_id
      }).then(() => {
        uni.showToast({
          title: '删除成功',
          icon: 'none'
        });
        // 返回上一页

        // 设置路由参数
        this.$store.commit('global/SET_QUERY', {
          menuId: '0',
        })
      });
    },
  },

}
</script>

<style lang="scss" scoped>
.main-wrapper {
  height: 100%;

  .main-header {
    display: flex;
    justify-content: space-between;
    position: sticky;
    top: 0;
    background: $bg-color-page;
    padding-bottom: $spacing-base;
    z-index: 900;

    .header-left {
      @include flex();

      .user-info {
        @include flex();
        background: $bg-color-white;
        padding: 20rpx;
        border-radius: $border-radius-base;

        .user-name {
          font-size: 28rpx;
          color: #333333;
          line-height: 44rpx;
          margin: 0 16rpx;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          max-width: 200rpx;
        }

        .user-age {
          font-size: 24rpx;
          color: #AAAAAA;
          line-height: 36rpx;
        }
      }
    }

    .header-right {
      @include flex();
    }
  }

  .main-content {
    height: calc(100% - 124rpx);
  }
}
.cus-tooltip {
  ::v-deep .uni-tooltip-popup {
    background: #FFFFFF;
    box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.12);
  }

  .build-person {
    font-weight: 400;
    font-size: 13px;
    color: #333333;
    line-height: 20px;
    display: flex;
    align-items: center;
    .person-label {
      font-weight: 400;
      font-size: 13px;
      color: #BBBBBB;
      line-height: 20px;
      min-width: fit-content;
    }
    .person-value {

    }
  }
}
</style>
