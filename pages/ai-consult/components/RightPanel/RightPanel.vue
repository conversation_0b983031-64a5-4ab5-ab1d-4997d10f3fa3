<template>
	<view class="right-panel">
		<view class="tabs_head">
      <view class="tabs">
        <view
          v-for="item in tabs"
          :key="item.key"
          :class="`tab-item ${tabIndex === item.key && 'tab-item-active'}`"
          @click="changeTabs(item.key)"
        >
          {{ item.label }}
          <image v-if="item.key === 2 && isAiDone" class="label-img" src="https://static.rsjxx.com/image/2025/0116/183737_77927.png" mode="aspectFill"></image>
        </view>
      </view>
      <button class="ui-btn-base compare-btn" @click="compareTongueBtn" type="default">
        <img src="https://static.rsjxx.com/image/2025/0609/172916_76765.png">
        舌象对比</button>
    </view>
		<view class="tabs-content">
			<history v-show="tabIndex === 1" :isReadOnly="isReadOnly"></history>
      <ai v-show="tabIndex === 2" :patientInfo="patientInfo" :isReadOnly="isReadOnly"></ai>
		</view>
    <!-- 舌象智能对比 -->
    <tongue-compare-drawer v-model="compareVisible"></tongue-compare-drawer>
	</view>
</template>

<script>
	import Ai from './ai.vue'
	import History from './history.vue'
  import tongueCompareDrawer from '@/components/tongueCompareDrawer/index.vue'
	export default {
		components: {
			Ai,
			History,
      tongueCompareDrawer,
		},
    props: {
      patientInfo: {
        type: Object,
        default: () => ({})
      }
    },
		data() {
			return {
				tabs: [
					{key: 1, label: '历史'},
					{key: 2, label: 'AI诊断', desc: '已生成'}
				],
				tabIndex: 2,
        isAiDone: false,
        isReadOnly: false,
        compareVisible: false,
      }
		},
    mounted() {
      // 获取页面参数
      const params = this.$store.getters['global/getQuery'] || {}
      if (params.pt_id) {
        this.tabIndex = 1
      } else {
        this.tabIndex = 2
      }
      uni.$on('changeRightTabIndex', this.changeTabs);
      uni.$on('aiIsDone', this.changeDone);
      uni.$on('getRecordStatus', this.getRecordStatus);
    },
    beforeDestroy() {
      uni.$off('changeRightTabIndex');
      uni.$off('aiIsDone');
      uni.$off('getRecordStatus');
    },
		methods: {
      // 舌象对比
      compareTongueBtn () {
        this.compareVisible = true
      },
			changeTabs(activeId, ) {
				this.tabIndex = activeId;
			},
      changeDone(flag) {
        this.isAiDone = flag;
      },
      getRecordStatus(flag) {
        this.isReadOnly = flag
      }
		}
	}
</script>

<style lang="scss" scoped>
.right-panel {
	width: 100%;
	height: 100%;
	position: relative;
}
.tabs_head {
  background: #FFFFFF;
  box-shadow: $ui-draw-shadow-base;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 9;
  height: 90rpx;
  width: 100%;
  display: flex;
  align-items: center;
}
.tabs {
  height: 90rpx;
  display: flex;
  flex: 1;
}
.compare-btn {
  margin-right: 10px;
  color: #155BD4;
  border-color: #155BD4;
  img {
    width: 14px;
    height: 14px;
    margin-right: 4px;
  }
}
.tab-item {
	width: 120rpx;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	font-weight: 600;
	font-size: 32rpx;
	color: #AAAAAA;
	border-bottom: 4rpx solid transparent;
  position: relative;
  .label-img {
    width: 50rpx;
    height: 20rpx;
    position: absolute;
    top: 24rpx;
    right: -18rpx;
  }
}
.tab-item-active {
	color: $uni-primary;
	border-color: $uni-primary;
}
.tabs-content {
	width: 100%;
	height: 100%;
  padding-top: 90rpx;
}
</style>
