<template>
  <scroll-view :scroll-top="scrollTop" scroll-with-animation scroll-y :scroll-ancxhoring="true" id="ai-container" class="ai-container" @scroll="onScroll">
    <view :aiData="aiData" :change:aiData="renderscript.getProps" />
    <view ref="ai-inner-container" id="ai-inner-container" class="ai-inner-container" :style="{ paddingBottom: paddingBottom + 'px'}">
      <msg-ai-box node="p0" :detail="detail.p0" />
      <msgUserBox
          :detail="detail.pUser"
          v-if="isShowNode('p0', 'pUser')"
          :aiUserInfo="getAiUserInfo"
      />
      <msg-ai-box
          ref="p1"
          node="p1"
          v-if="isShowNode('pUser', 'p1')"
          :detail="detail.p1"
          @handleQueryAiByTaskName="handleQueryAiByTaskName"
      />
      <msg-ai-box
          ref="p3"
          node="p3"
          v-if="isShowNode('p1', 'p3')"
          :detail="detail.p3"
          @handleQueryAiByTaskName="handleQueryAiByTaskName"
      />
      <msg-sankey-box
          ref="p8"
          node="p8"
          v-if="isShowNode('p3', 'p8')"
          :detail="detail.p8"
          @handleQueryInfoByTaskName="handleQueryInfoByTaskName"
          @handleQueryAiByTaskName="handleQueryAiByTaskName"
      />
      <msg-pie-box
          ref="p7_p8"
          node="p7_p8"
          v-if="isShowNode('p8', 'p7_p8')"
          :detail="detail.p7_p8"
          @handleQueryInfoByTaskName="handleQueryInfoByTaskName"
          @handleQueryAiByTaskName="handleQueryAiByTaskName"
          @needScroll="handleChildScroll"
      />
      <msg-result-box
          ref="p8_result"
          :p17="detail.p17"
          :p29="detail.p29"
          :isReadOnly="isReadOnly"
          v-if="isShowNode('p11', 'p17')"
          @handleQueryInfoByTaskName="handleQueryInfoByTaskName"
          @handleQueryAiByTaskName="handleQueryAiByTaskName"
          @needScroll="handleChildScroll"
      />
      <view id="point" style="height: 1px"></view>
      <view v-if="showBackTop && !isScrolling" class="back-top" @click="backtop">
        <image src="@/static/image/ai/back-top.png"></image>
      </view>
    </view>
  </scroll-view>
</template>

<script>
	import utils from "../../../../utils/utils";
  import {cloneDeep, throttle} from "lodash";
  import AiService from "../../../../api/ai";

  import msgUserBox from "./components/msg-user-box.vue";
  import msgAiBox from "./components/msg-ai-box.vue";
  import msgPieBox from "./components/msg-pie-box.vue";
  import msgSankeyBox from "./components/msg-sankey-box.vue";
  import MsgResultBox from "./components/msg-result-box.vue";

  const initAiData = {
    p0: {
      data: 'Hi，我是榕树家中医辅助助手，输入主诉和四诊信息后，我会主动帮您辨证分析哦～',
      is_finish: true,
      done: false, // 代表当前节点是否结束，不管是异常还是正常结束都是true
      requestErr: false,
      end: false, // 代表当前节点是否是正常结束， 正常结束为true。异常类结束都是false
    },
    pUser: {
      data: '',
      img: '',
      done: false,
      requestErr: false,
      end: false, // 代表当前节点是否是正常结束， 正常结束为true。异常类结束都是false
      disabledClickDesc: false,
    },
    p1: {
      data: '',
      desc: 'p25-p1',
      showAnswer: false,
      answerText: '',
      done: false,
      answerDone: false,
      requestErr: false,
      answerRequestErr: false,
      end: false, // 代表当前节点是否是正常结束， 正常结束为true。异常类结束都是false
      disabledClickDesc: false,
    },
    p3: {
      data: '',
      desc: 'p25-p3',
      showAnswer: false,
      answerText: '',
      done: false,
      answerDone: false,
      requestErr: false,
      answerRequestErr: false,
      end: false, // 代表当前节点是否是正常结束， 正常结束为true。异常类结束都是false
      disabledClickDesc: false,
    },
    p8: {
      data: [],
      desc: 'p5',
      showAnswer: false,
      answerText: '',
      done: false,
      answerDone: false,
      requestErr: false,
      answerRequestErr: false,
      end: false, // 代表当前节点是否是正常结束， 正常结束为true。异常类结束都是false
      disabledClickDesc: false,
    },
    p7_p8: {
      p7: {
        data: [],
        done: false,
        requestErr: false,
        end: false, // 代表当前节点是否是正常结束， 正常结束为true。异常类结束都是false
        disabledClickDesc: false,
      },
      p8: {
        data: [],
        done: false,
        requestErr: false,
        end: false, // 代表当前节点是否是正常结束， 正常结束为true。异常类结束都是false
        disabledClickDesc: false,
      },
      desc: 'p11',
      showAnswer: false,
      answerText: '',
      answerDone: false,
      requestErr: false,
      answerRequestErr: false,
      end: false, // 代表当前节点是否是正常结束， 正常结束为true。异常类结束都是false
      disabledClickDesc: false,
    },
    p17: {
      data: {},
      showAnswer: false,
      answerText: '',
      done: false,
      answerDone: false,
      requestErr: false,
      end: false, // 代表当前节点是否是正常结束， 正常结束为true。异常类结束都是false
      disabledClickDesc: false,
    },
    p29: {
      data: {
        plans: [],
      },
      desc: 'p25-p29',
      showAnswer: false,
      answerText: '',
      done: false,
      answerDone: false,
      requestErr: false,
      answerRequestErr: false,
      end: false, // 代表当前节点是否是正常结束， 正常结束为true。异常类结束都是false
      disabledClickDesc: false,
    }
  };

  export default {
    components: {
      MsgResultBox,
      msgUserBox,
      msgAiBox,
      msgPieBox,
      msgSankeyBox
    },
    props: {
      patientInfo: {
        type: Object,
        default: () => ({})
      },
      isReadOnly: {
        type: Boolean,
        default: false
      }
    },
		data() {
		  return {
				aiImg: 'https://static.rsjxx.com/image/2025/0117/101026_68163.png',
				messages: [],
				eventSource: null,
				aiData: {
					mr_id: '',
					clinic_id: '',
					uid: '',
					device_id: '',
					task_name: '',
					token: '',
				},
        curNode: '', // 当前正在获取数据的节点
				userInfo: {},
        detail: {
          ...cloneDeep(initAiData),
        },
        sexMap: {
          '1': '男',
          '2': '女',
        },
        record_info: {},
        scrollTop: 0,
        oldScrollTop: 0,
        scrollViewHeight: 0,
        scrollContentHeight: 0,
        handleQueryAiByTaskNameBind: null,
        outBoxHeight: 0,
        paddingBottom: 30,
        shouldAutoScroll: true, // 控制是否自动滚动到底部
        _scrollTimer: null,
        lastContentHeight: 0, // 添加记录上次内容高度的变量
        isScrolling: false
			};
		},
    computed: {
      getAiUserInfo() {
        return {}
      },
      isShowNode() {
        return (preNode, curNode) => {
          if (curNode === 'p17') {
            return this.detail.p7_p8.answerDone;
          };
          if (!this.isStop) {
            return this.detail[preNode]?.end;
          } else if (this.curNode === curNode) {
            return true;
          } else {
            return this.detail[curNode]?.end;
          }
        };
      },
      showBackTop() {
        return this.detail.p29.answerDone && this.oldScrollTop > 1000
      }
    },
    created() {
      uni.$on('queryAiInfoEvent', this.handleQueryAiInfoEvent);
      uni.$on('handleUpdateAiAnswerInfo', this.handleUpdateAiAnswerInfo);

      // this.startHeightCheck()
		},
    mounted(){
      this.$nextTick(() => {
        const query = uni.createSelectorQuery().in(this)
        query.select('#ai-container').boundingClientRect(data => {
          this.outBoxHeight = data.height
        }).exec();
      })
    },
    beforeDestroy() {
      uni.$off('queryAiInfoEvent');
      uni.$off('handleUpdateAiAnswerInfo');
    },
		methods: {
      getAiChat() {
        if (!this.aiData.mr_id) return
        AiService.getAiChat({
          mr_id: this.aiData.mr_id,
        }).then((res) => {
          if (res?.has_draft === '1' && res?.data?.['p25-p29']) {
            uni.$emit('changeRightTabIndex', 2)
            this.$nextTick(() => {
              const keys = Object.keys(res.data);
              keys.length &&
              keys.forEach(key => {
                if (key === 'p-user') {
                  this.detail.p0.end = true
                  this.detail.p0.done = true;
                  this.detail.pUser.data = res.data?.[key] || '';
                  this.setPUser()
                }
                if (key === 'p1') {
                  this.detail.pUser.done = true;
                  this.detail.pUser.end = true;
                }
                if (['p1', 'p3', 'p17', 'p29'].includes(key)) {
                  this.detail[key].done = true;
                  this.detail[key].end = true;
                  this.detail[key].data = res.data?.[key]?.content;
                  if (key === 'p17') {
                    // const importData = {
                    //   medical_case: {
                    //     tcm_predict_text: res.data?.[key]?.content?.syndrome || '',
                    //     tcm_cure_text: res.data?.[key]?.content?.treatment || '',
                    //   },
                    // };
                    // this.$bus.$emit('importRecordTipEvent', importData);
                  }
                }
                if (key === 'p7') {
                  this.detail['p7_p8'].p7.done = true;
                  this.detail['p7_p8'].p7.end = true;
                  this.detail['p7_p8'].p7.data = res.data?.[key]?.content;
                }
                if (key === 'p8') {
                  this.detail['p7_p8'].done = true;
                  this.detail['p7_p8'].p8.done = true;
                  this.detail['p7_p8'].p8.end = true;
                  this.detail['p7_p8'].p8.data = res.data?.[key]?.content;
                  this.detail[key].done = true;
                  this.detail[key].end = true;
                  this.detail[key].data = res.data?.[key]?.content;
                }
                if (key === 'p11') {
                  this.detail.p7_p8.answerText = res?.data?.[key]?.content;
                  this.detail.p7_p8.answerDone = true;
                  this.detail.p7_p8.answerRequestErr = false;
                  this.$nextTick(() => {
                    typeof this.$refs?.p7_p8?.setData === 'function' &&  this.$refs?.p7_p8?.setData()
                  });
                }
                if (key === 'p25-p1') {
                  this.detail.p1.done = true;
                  this.detail.p1.start = true;
                  this.detail.p1.end = true;
                  this.detail.p1.answerDone = true;
                  this.detail.p1.answerText = res?.data?.[key]?.content;

                  this.$nextTick(() => {
                    typeof this.$refs?.p1?.setData === 'function' &&  this.$refs?.p1?.setData()
                  })
                }
                if (key === 'p25-p29') {
                  this.detail.p29.done = true;
                  this.detail.p29.start = true;
                  this.detail.p29.end = true;
                  this.detail.p29.answerDone = true;
                  this.detail.p29.answerText = res?.data?.[key]?.content;
                }
                if (key === 'p5') {
                  this.detail.p8.answerText = res?.data?.[key]?.content;
                  this.detail.p8.answerDone = true;
                  this.detail.p8.answerRequestErr = false;
                  this.detail.p8.end = true;
                 this.$nextTick(() => {
                   typeof this.$refs?.p8?.setData === 'function' &&  this.$refs?.p8?.setData()
                  });
                }
              });
            })
            // uni.$emit('aiIsDone', true);
            uni.$emit('getFetchAIStatus', true);
          }
        })
      },
      backtop() {
        this.scrollTop = this.oldScrollTop
        this.isScrolling = true
        this.$nextTick(function() {
          this.scrollTop = 0
          setTimeout(() => {
            this.isScrolling = false
          }, 1000)
        });
      },
      onScroll: throttle(function(e) {
        const scrollTop = e.detail.scrollTop
        const viewHeight = this.outBoxHeight // 视口高度
        const scrollHeight = e.detail.scrollHeight // 内容总高度

        // 判断是否在底部附近（允许50px的误差）
        this.shouldAutoScroll = (scrollHeight - (scrollTop + viewHeight)) <= 50

        this.oldScrollTop = scrollTop
      }, 36),
      handleUpdateAiAnswerInfo({ mr_id,record_info = {}}) {
        this.aiData.mr_id = mr_id;
        delete record_info.medical_history;
        this.record_info = record_info
        delete this.record_info.check_list_key
        this.$nextTick(() => {
          this.getAiChat();
        })
      },
      scrollToLatest(delay = 100, force = false, callback) {
        if (force || this.shouldAutoScroll) {
          if (this._scrollTimer) {
            clearTimeout(this._scrollTimer)
          }

          this._scrollTimer = setTimeout(() => {
            const query = uni.createSelectorQuery().in(this)
            query
              .select('#ai-inner-container')
              .boundingClientRect()
              .select('#point')
              .boundingClientRect()
              .exec(([container, point]) => {
                if (!container || !point) return;

                // 计算需要额外添加的高度
                let extraHeight = 0

                // 根据不同节点调整滚动位置
                if (this.curNode === 'p11') {
                  extraHeight = 300 // p11 需要更多空间显示答案
                } else if (this.curNode === 'p25-p29') {
                  extraHeight = 500 // 最终节点需要最大空间
                } else {
                  extraHeight = 150
                }

                // 使用容器高度和点位置来计算滚动位置
                const scrollHeight = Math.max(
                  point.top - container.top + extraHeight,
                  container.height + extraHeight
                )

                this.smoothScroll(this.oldScrollTop, scrollHeight, callback)
              })
          }, delay)
        }
      },
      smoothScroll(from, to, callback) {
        if (this._smoothScrollTimer) {
          clearTimeout(this._smoothScrollTimer)
        }

        const duration = 300 // 滚动持续时间(ms)
        const frames = 20 // 动画帧数
        const interval = duration / frames // 每帧间隔时间
        let currentFrame = 0

        const scroll = () => {
          currentFrame++

          // 使用缓动函数计算当前进度(0-1)
          const progress = currentFrame / frames
          const easing = 1 - Math.pow(1 - progress, 3) // 使用 easeOutCubic 缓动

          // 计算当前滚动位置
          this.scrollTop = from + (to - from) * easing

          if (currentFrame < frames) {
            this._smoothScrollTimer = setTimeout(scroll, interval)
          } else {
            // 滚动结束
            this.scrollTop = to
            this.shouldAutoScroll = true
            this._smoothScrollTimer = null
            // 执行完成回调
            callback && callback()
          }
        }

        scroll()
      },
      getAiInfo(data) {
        if (!data.text) return

        const updateData = () => {
          // 文本节点更新时需要滚动
          const needScrollNodes = ['p1', 'p3', 'p11', 'p17', 'p25-p29']

          if (data.task_name === 'p25-p1') {
            this.detail['p1'].answerText = data.text || '';
            return
          }
          if (data.task_name === 'p25-p29') {
            this.detail['p29'].answerText = data.text || '';
            // this.scrollToLatest(150)
            return
          }
          if (data.task_name === 'p5') {
            this.detail['p8'].answerText = data.text || '';
            return
          }
          if (data.task_name === 'p11') {
            this.detail.p7_p8.answerText = data.text || '';
            // this.scrollToLatest(150)
            return
          }
          if (data.task_name) {
            this.detail[data.task_name].data = data.text;
            // 只在特定节点更新时滚动
            if (needScrollNodes.includes(data.task_name)) {
              // this.scrollToLatest(150)
            }
          }
        }

        this.$nextTick(updateData)
      },
      getAiInfoEnd(data) {
        const {res, taskName } = data || {}

        res?.msg && uni.showToast({
          title: res.msg,
          icon: 'none'
        });

        if (taskName === 'p11') {
          this.detail.p7_p8.answerDone = true;
          this.detail.p7_p8.answerRequestErr = res?.done;
          this.detail.p7_p8.answerEnd = res?.done;

          // 先滚动到当前内容
          this.scrollToLatest(200, true, () => {
            // 等待滚动完成后请求下一个节点
            this.handleQueryInfoByTaskName('p17')
          })
          return;
        }

        if (taskName === 'p25-p29') {
          this.detail['p29'].answerDone = true;
          this.detail['p29'].answerRequestErr = res?.done;
          this.scrollToLatest(150, true)
          // 确保所有内容渲染完成后滚动到底部
          setTimeout(() => {
            this.paddingBottom = 30
          }, 300) // 增加延迟确保内容完全渲染
          uni.$emit('getFetchAIStatus', true);
          return
        }

        // 处理各节点结束状态
        if (taskName === 'p25-p1') {
          this.detail['p1'].answerDone = true;
          this.detail['p1'].answerRequestErr = res?.done;
          this.scrollToLatest(150)
          return
        }
        // if (taskName === 'p25-p29') {
        //   this.detail['p29'].answerDone = true;
        //   this.detail['p29'].answerRequestErr = res?.done;
        //   // 增加延迟确保内容完全渲染
        //   this.scrollToLatest(150, true) // 强制滚动到底部
        //   return
        // }
        if (taskName === 'p5') {
          this.detail['p8'].answerDone = true;
          this.detail['p8'].answerRequestErr = res?.done;
          return
        }
        if (taskName === 'p11') {
          this.detail.p7_p8.answerDone = true;
          this.detail.p7_p8.answerRequestErr = res?.done;
          this.detail.p7_p8.answerEnd = res?.done;

          // 等待子组件更新完成
          this.scrollToLatest(150, true, () => {
            this.handleQueryInfoByTaskName('p17')
          })
          return;
        }

        this.detail[taskName].done = true;
        this.detail[taskName].end = !res?.done;
        this.detail[taskName].requestErr = res?.done;
        if (!res?.done && taskName === 'p1') {
          this.handleQueryAiByTaskName('p3');
        }
        if (!res?.done && taskName === 'p3') {
          this.handleQueryInfoByTaskName('p8');
        }
      },
      handleQueryInfoByTaskName(taskName) {
        this.curNode = taskName;

        if (taskName === 'p7') {
          this.detail['p7_p8'].p7.data = cloneDeep(initAiData['p7_p8'].p7.data);
          this.detail['p7_p8'].done = false;
          this.detail['p7_p8'].end = false;
          this.detail['p7_p8'].requestErr = false;
        } else {
          this.detail[taskName].data = cloneDeep(initAiData[taskName].data);
          this.detail[taskName].done = false;
          this.detail[taskName].end = false;
          this.detail[taskName].requestErr = false;
        }
        AiService.getTaskData({
          mr_id: this.aiData.mr_id,
          clinic_id: this.aiData.clinic_id,
          task_name: taskName,
        }).then(res => {
          if (res?.is_finish === '1') {
            if (res.task_name === 'p7') {
              this.detail.p7_p8.p7.data = res.content || [];
              if (!this.detail.p7_p8.p7.done) {
                this.detail.p7_p8.p7.done = res.is_finish;
                this.detail.p7_p8.p7.end = true;
                this.detail.p7_p8.end = true;
                this.detail.p7_p8.p7.requestErr = false;
              }

              this.$nextTick(() => {
                if (this.$refs.p7_p8) {
                  this.$refs.p7_p8.disabledClickDesc = true;
                  this.$refs.p7_p8.isShowAnswerTip = true;
                  // 先滚动到当前位置
                  this.scrollToLatest(100, true, () => {
                    // 滚动完成后请求下一个节点
                    this.handleQueryAiByTaskName('p11');
                  })
                }
              })
              return;
            }

            if (res.task_name === 'p8') {
              this.detail.p8.data = res.content || [];
              this.detail.p7_p8.p8.data = res.content || [];
              if (!this.detail.p8.done) {
                this.detail.p8.done = res.is_finish;
                this.detail.p8.end = true;
                this.detail.p8.requestErr = false;
              }
              if (!this.detail.p7_p8.p8.done) {
                this.detail.p7_p8.p8.done = res.is_finish;
                this.detail.p7_p8.p8.end = true;
                this.detail.p7_p8.p8.requestErr = false;
              }

              // 先滚动到当前位置
              this.scrollToLatest(100, true, () => {
                // 滚动完成后请求下一个节点
                this.handleQueryInfoByTaskName('p7');
              })
              return;
            }

            if (res.task_name === 'p17') {
              this.detail.p17.data = res.content;
              this.detail.p17.done = res.is_finish;
              this.detail.p17.end = true;
              this.detail.p17.requestErr = false;

              this.scrollToLatest(150, true, () => {
                this.handleQueryInfoByTaskName('p29');
              })
              return;
            }

            if (res.task_name === 'p29') {
              this.detail.p29.data = res.content;
              this.detail.p29.done = res.is_finish;
              this.detail.p29.end = true;
              this.detail.p29.requestErr = false;

              this.scrollToLatest(150, true, () => {
                this.handleQueryAiByTaskName('p25-p29');
              })
            }
          }
        }).catch(err => {
          this.detail[taskName].requestErr = true;
        })
      },
      handleQueryAiInfoEvent() {
        this.setPUser(true)
        this.setDraftAiChatUser()
      },
      setPUser(flag) {
        flag && (this.detail = cloneDeep(initAiData));
        const str = Object.values(this.record_info)?.filter(str => str !== '').join('，')
        const { sex, age, avatar, name } = this.patientInfo
        const secName = this.sexMap?.[sex] ? this.sexMap?.[sex] + '，' : ''
        const ageName = age ? age + '，' : ''
        this.$set(this.detail.pUser, 'data', secName + ageName +  str)
        this.$set(this.detail.pUser, 'img', avatar)
        this.$set(this.detail.pUser, 'username', name)
      },
      setDraftAiChatUser() {
        AiService.setDraftAiChatUser({
          mr_id: this.aiData.mr_id,
          p_user: this.detail.pUser.data
        }).then(() => {
          this.paddingBottom = 300
          this.handleQueryAiByTaskName('p1')
          this.scrollTop = 0
        })
      },
			handleQueryAiByTaskName(taskName) {
				const userInfo = utils.getStorageSync('userInfo')
				this.aiData.clinic_id = userInfo.clinic_id;
        this.aiData.task_name = taskName;
				this.aiData.uid = userInfo.mer_uid;
				this.aiData.device_id = uni.getSystemInfoSync().deviceId;
				this.aiData.token = utils.getStorageSync('accessToken');
        this.curNode = taskName;

        if (taskName === 'p25-p1') {
          this.detail['p1'].answerText = '';
          this.detail['p1'].answerRequestErr = false;
          this.detail['p1'].answerDone = false;
          return
        }
        if (taskName === 'p5') {
          this.detail['p8'].answerText = '';
          this.detail['p8'].answerRequestErr = false;
          this.detail['p8'].answerDone = false;
          return
        }
        if (taskName === 'p25-p29') {
          this.detail['p29'].answerText = '';
          this.detail['p29'].answerRequestErr = false;
          this.detail['p29'].answerDone = false;
          return
        }
        if (taskName === 'p11') {
          this.detail['p7_p8'].answerText = '';
          this.detail['p7_p8'].answerRequestErr = false;
          this.detail['p7_p8'].answerDone = false;
          this.$refs.p7_p8 && this.$nextTick(() => {
            this.$refs.p7_p8.disabledClickDesc = true;
          })
          return
        }
        if (taskName === 'p1') {
          this.detail.p0.done = true;
          this.detail.p0.end = true;
          // this.detail.pUser.data = this.getAiUserInfo?.answer;
          // this.detail.pUser.img = this.getAiUserInfo?.img;
          this.detail.pUser.done = true;
          this.detail.pUser.end = true;
        }
        this.detail[taskName].requestErr = false;
        this.detail[taskName].done = false;
        this.detail[taskName].end = false;
			},
      handleChildScroll() {
        // 子组件触发滚动时，确保滚动到正确位置
        this.$nextTick(() => {
          setTimeout(() => {
            this.scrollToLatest(200, true)
          }, 300)
        })
      }
		}
	}
</script>
<script module="renderscript" lang="renderjs">
import {sseFetch} from '../../../../utils/sseRequest';
export default {
  data() {
	  return {
        userInfo: {}
	  }
  },
  methods: {
	  getProps(newValue) {
		  this.userInfo = newValue
		  if (!!this.userInfo?.task_name) {
		  	this.getSseByTaskName(this.userInfo?.task_name);
		  }
	  },
	      getTaskSseByTaskName(res, taskName) {
			this.$ownerInstance.callMethod('getAiInfo', {
				text: res,
				task_name: taskName
			})
	      },
	      sseEnd(res, taskName) {
          this.$ownerInstance.callMethod('getAiInfoEnd', {
          res,
          taskName
        })
	      },
		  getSseByTaskName(taskName = 'p1') {
			  sseFetch(
			  'his/v4/medical.assistant.tasksse',
			  {
				  ...this.userInfo,
				  task_name: taskName
			  },
			  res => this.getTaskSseByTaskName(res, taskName),
			  res => this.sseEnd(res, taskName)
			  )
		  }
  },
  mounted() {

  },
  beforeDestroy() {
  }
};
</script>
<style lang="scss" scoped>
  .ai-container {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    background-color: #FFFFFF;
    padding-bottom: 50rpx;
    position: relative;
    .ai-inner-container {
      padding: 32rpx 32rpx 196rpx 32rpx;
    }
  }
  .back-top {
    width: 108rpx;
    height: 108rpx;
    position: fixed;
    right: 14rpx;
    bottom: 14rpx;
    @include flex(row, center, center);
    > image {
      width: 100%;
      height: 100%;
    }
  }
</style>
