<script>
export default {
  name: 'CustomLoading',
  props: {
    type: {
      type: String,
      default: '1'
    }
  }
}
</script>

<template>
  <div class="loading-container">
    <div class="preloader-wrapper" v-if="type === '1'"></div>
    <div class="wave-loading" v-if="false">
      <span style="--time:1">.</span>
      <span style="--time:2">.</span>
      <span style="--time:3">.</span>
    </div>
    <div class="pulse-wrapper" v-if="type === '2'">
      <div class="pulse-item one"></div>
      <div class="pulse-item two"></div>
      <div class="pulse-item three"></div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.loading-container {
  position: relative;
  display: inline-block;
}
.preloader-wrapper {
  position: relative;
  display: inline-block;
  width: 10px;
  height: 10px;
  margin-left: 20px;
}
.preloader-wrapper:before {
  width: 10px;
  height: 10px;
  border-radius: 10px;
  content: "";
  position: absolute;
  background: #72E1E2;
  animation: preloader_before 2s infinite ease-in-out;
}

.preloader-wrapper:after {
  width: 10px;
  height: 10px;
  border-radius: 10px;
  content: "";
  position: absolute;
  background: #4488FD;
  left: 22px;
  animation: preloader_after 1.5s infinite ease-in-out;
}

@keyframes preloader_before {
  0% {
    transform: translateX(0px) rotate(0deg);
  }
  50% {
    transform: translateX(40px) scale(1.2) rotate(260deg);
    background: #4488FD;
    border-radius: 0px;
  }
  100% {
    transform: translateX(0px) rotate(0deg);
  }
}
@keyframes preloader_after {
  0% {
    transform: translateX(0px);
  }
  50% {
    transform: translateX(-40px) scale(1.2) rotate(-260deg);
    background: #9b59b6;
    border-radius: 0px;
  }
  100% {
    transform: translateX(0px);
  }
}


.wave-loading {
  height: 20px;
  position: absolute;
  display: flex;
  left: 14px;
  top: -4px;
  transform: translate(-50%, -50%);
}

.wave-loading span {
  display: inline-block;
  font-size: 20px;
  font-weight: bold;
  color: #1e64ee;
  font-family: 'Courier New', Courier, monospace;
  animation: wave-loading 1s ease-in-out infinite;
  animation-delay: calc(0.1s * var(--time));
}

@keyframes wave-loading {
  0% {
    transform: translateY(0px);
  }

  25% {
    transform: translateY(-10px);
  }

  50%,100% {
    transform: translateY(0px);
  }
}



.pulse-wrapper {
  position: relative;
}
.pulse-item {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #1e64ee;
  animation: pulse-loader 0.4s ease 0s infinite alternate;
  position: relative;
  display: inline-block;
}
.two {
  margin: 0 2px;
  animation: pulse-loader 0.4s ease 0.2s infinite alternate;
}
.three {
  animation: pulse-loader 0.4s ease 0.4s infinite alternate;
}

@keyframes pulse-loader {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0.5;
    transform: scale(0.75);
  }
}
</style>