<script>
export default {
  name: 'error-comp',
  props: {
    detail: {
      type: Object,
      detail: () => ({}),
    },
    mt: {
      type: Number,
      detail: 0,
    },
  },
  data() {
    return {
      disabled: true,
      count: 5,
      timer: null
    }
  },
  methods: {
    handleRepeatRequestSse() {
      if (this.disabled) return
      this.$emit('handleRepeatRequestSse');
    },
  },
  mounted() {
    clearInterval(this.timer);
    this.timer = setInterval(() => {
      this.count -= 1;
      if (this.count <= 0) {
        this.disabled = false
        this.timer = null;
      }
    }, 1000)
  },
  beforeDestroy() {
    clearInterval(this.timer);
  }
};
</script>

<template>
  <div class="error-default" :style="`margin-top: ${mt}px`">
    <image
      :style="`width: ${mt ? 260 : 130}px;height: ${mt ? 200 : 100}px`"
      src="/static/image/ai/error-1.png"
      alt=""
    />
    <div class="error-text">{{ detail.requestErrMsg || '网络请求异常' }}</div>
    <a
        :class="disabled ? 'disabled': ''"
    >

      <span v-if="count > 0">
        {{ count }}秒后可重新获取
      </span>
      <span @click="handleRepeatRequestSse" v-else>点击重新获取</span>
    </a>
  </div>
</template>

<style scoped lang="scss">
.error-default {
  width: 100%;
  padding: 26px 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .error-text {
    width: 100%;
    font-size: 14px;
    color: #aaaaaa;
    line-height: 20px;
    text-align: center;
    font-style: normal;
    margin-bottom: 4px;
  }
  > a {
    font-weight: 400;
    font-size: 12px;
    color: #155bd4;
    line-height: 18px;
    text-align: center;
  }
}
.disabled {
  color: #aaaaaa !important;
}
</style>
