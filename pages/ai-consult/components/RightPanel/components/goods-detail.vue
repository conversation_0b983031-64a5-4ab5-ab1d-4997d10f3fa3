<template>
  <uni-drawer ref="showRight" mode="right" :mask-click="true" :width="440">
      <view class="goods-detail">
        <view class="goods-detail-title">
          <view class="title">膏方详情</view>
          <view class="close" @click="closeDrawer">
            <image src="/static/image/logout/close.png" class="close-icon"></image>
          </view>
        </view>
        <scroll-view class="content" scroll-y="true">
          <view class="goods-content">
            <view class="goods-info">
              <view class="goods-info-title"> {{ detail.name }}</view>
              <view class="goods-info-desc">
                <image class="goods-img" :src="main_img" mode="aspectFill"></image>
                <view class="goods-desc">
                  <view class="goods-desc-item" v-for="(spec, index) in info.specs" :key="index">
                    <view class="label">
                      <view>{{ spec.title }}</view>
                      <text>:</text>
                    </view>
                    <view class="text">{{ spec.content }}</view>
                  </view>
                </view>
              </view>
            </view>
            <image class="goods-detail-img" v-for="(img, i) in info.detail_images" :key="i" :src="img" mode="widthFix"></image>
            <view class="goods-introduce" v-for="(intr, i) in desc">
              <view class="line"></view>
              <view class="goods-introduce-title">
               <view class="title-box">
                 <view class="title">{{intr.title}}</view>
                 <view class="expand" @click="expand(i)">
                   <view class="expand-text">{{ intr.show ? '收起' : '展开' }}</view>
                   <image class="expand-img" :src="intr.show? upImg : rightImg"></image>
                 </view>
               </view>
                <view class="content" v-if="intr.show">
                  {{ intr.content }}
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
  </uni-drawer>
</template>
<script>
export default {
  name: "goods-detail",
  props: {
    detail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      defaultGoodsImg: 'https://img-sn-i01s-cdn.rsjxx.com/image/2025/0117/134810_56784.png-B.200',
      upImg: 'https://static.rsjxx.com/image/2025/0207/161519_73904.png-B.200',
      rightImg: 'https://static.rsjxx.com/image/2025/0207/161519_76570.png-B.200',
      desc: []
    }
  },
  computed: {
    info() {
      return {
        detail_images: this.detail?.info?.detail_images || [this.defaultGoodsImg],
        specs: this.detail?.info?.specs || [],
        tags: this.detail?.info?.tags || [],
      }
    },
    main_img() {
      return this.detail?.goods?.main_img || this.defaultGoodsImg
    }
  },
  watch: {
    detail(val) {
      this.desc = this.detail?.info?.desc?.map(desc => ({ ...desc, show: true })) || []
    }
  },
  methods: {
    showDrawer() {
      this.$refs.showRight && this.$refs.showRight.open();
    },
    closeDrawer() {
      this.$refs.showRight.close();
    },
    expand(index) {
      this.$set(this.desc[index], 'show', !this.desc[index].show);
    }
  }
}
</script>
<style scoped lang="scss">
.goods-detail {
  width: 100%;
  height: 100%;
  .goods-detail-title {
    width: 880rpx;
    height: 120rpx;
    padding-left: 40rpx;
    background: #FFFFFF;
    box-shadow: 0 6rpx 12rpx 0 rgba(20,21,22,0.04);
    @include flex(row, space-between, center);
    .title {
      font-weight: 600;
      font-size: 32rpx;
      color: #333333;
    }
    .close {
      width: 60rpx;
      height: 100%;
      padding-right: 30rpx;
      @include flex(row, center, center);
      > image {
        width: 24rpx;
        height: 24rpx;
      }
    }
  }
  .content {
    width: 100%;
    height: calc(100% - 160rpx);
    .goods-content {
      width: 100%;
      height: 100%;
      padding: 0 40rpx;
      .goods-info {
        margin: 40rpx 0;
        width: 100%;
        background: #FFFFFF;
        box-shadow: 2rpx 2rpx 12rpx 0 rgba(0,0,0,0.12);
        border-radius: 8rpx;
        padding: 24rpx;
        .goods-info-title {
          font-weight: 600;
          font-size: 32rpx;
          color: #333333;
          line-height: 48rpx;
        }
        .goods-info-desc {
          width: 100%;
          margin-top: 24rpx;
          @include flex(row, flex-start, center);
          .goods-img {
            width: 164rpx;
            height: 164rpx;
            margin-right: 24rpx;
          }
          .goods-desc {
            flex: 1;
            @include flex(column, space-between, flex-start);
            .goods-desc-item {
              @include flex(row, flex-start, flex-start);
              .label {
                width: 140rpx;
                margin-right: 10rpx;
                @include flex(row, flex-start, center);
                > view {
                  width: 120rpx;
                  font-size: 24rpx;
                  color: #999999;
                  margin-right: 4rpx;
                  text-align: justify;
                  text-align-last: justify;
                }
                > text {
                  font-size: 24rpx;
                  color: #999999;
                }
              }
              .text {
                flex: 1;
                font-size: 24rpx;
                color: #333333;
              }
            }
          }
        }
      }
      .goods-detail-img {
        width: 100%;
        margin-bottom: 20rpx;
      }
      .goods-detail-img:last-child {
        margin-bottom: 0 !important;
      }
      .goods-introduce {
        width: 100%;
        .line {
          width: 100%;
          height: 2rpx;
          border: 2rpx solid #EBEDF0;
        }
        .goods-introduce-title {
          width: 100%;
          .title-box {
            padding: 40rpx 0;
            @include flex(row, space-between, center);
            .title {
              font-weight: 500;
              font-size: 32rpx;
              color: #333333;
              line-height: 44rpx;
            }
            .expand {
              @include flex(row, center, center);
              .expand-text {
                font-size: 24rpx;
                color: #999999;
                line-height: 32rpx;
                margin-right: 4rpx;
              }
              .expand-img {
                width: 20rpx;
                height: 20rpx;
              }
            }
          }
          .content {
            width: 100%;
            padding: 24rpx;
            background: #FAFAFB;
            border-radius: 8rpx;
            font-weight: 300;
            font-size: 28rpx;
            color: #333333;
            line-height: 44rpx;
            white-space: pre-wrap;
            margin-bottom: 40rpx;
          }
        }
      }
    }
  }
}
</style>
