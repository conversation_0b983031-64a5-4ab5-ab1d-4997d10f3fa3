<template>
  <view class="ai-box">
    <view class="ai-head">
      <image class="ai-img" :src="aiImg"></image>
      <view class="ai-name">素灵AI</view>
    </view>
    <view class="ai-text">
      <ua-markdown :source="detail.data" />
      <error-comp v-if="detail.requestErr" :detail="detail" @handleRepeatRequestSse="handleRepeatQueryAi('p1')" />
      <custom-loading type="1" v-if="!detail.data && !detail.requestErr && node !== 'p0' && !detail.start" />
      <custom-loading
          v-if="!detail.requestErr && detail.data && node !== 'p0' && !detail.done"
          ref="customLoading"
          type="2"
      />
    </view>
    <a
        v-if="node !== 'p0' && node !== 'p3'"
        :class="`expand-more ${disabledClickDesc ? 'disabled-expand-more' : '' }`"
        @click="handleRepeatQueryAi('p25-p1')"
    >
      举个通俗易懂的例子
      <image v-if="!disabledClickDesc" src="@/static/image/ai/left.svg"/>
      <image v-else src="@/static/image/ai/disabledRight.png"/>
    </a>
    <view v-if="node !== 'p0' && node !== 'p3' && isShowAnswerTip" class="more-text">
      <ua-markdown :source="detail.answerText" />
      <error-comp
          v-if="detail.answerRequestErr"
          :detail="detail"
          @handleRepeatRequestSse="handleRepeatQueryAi('p25-p1')"
      />
      <custom-loading type="1" v-if="!detail.answerText && !detail.answerRequestErr" />
      <custom-loading
          ref="customLoading"
          type="2"
          v-if="detail.answerText && !detail.answerDone && !detail.answerRequestErr"
      />
    </view>
  </view>
</template>
<script>
import uaMarkdown from "./ua2-markdown/ua-markdown.vue";
import customLoading from "./customLoading/index.vue";
import errorComp from "./error-comp/index.vue";
export default {
  name: "msg-ai-box",
  components: {
    uaMarkdown,
    customLoading,
    errorComp
  },
  props: {
    detail: {
      type: Object,
      default: () => ({
        data: '',
        is_finish: false,
        showAnswer: false,
      }),
    },
    node: {
      type: String,
      default: 'p0',
    },
    isStop: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      aiImg: 'https://static.rsjxx.com/image/2025/0117/101026_68163.png',
      isShowAnswerTip: false,
      disabledClickDesc: false,
    }
  },
  methods: {
    handleRepeatQueryAi(taskName) {
      if (taskName === 'p25-p1') {
        if (this.disabledClickDesc) return;
        this.isShowAnswerTip = true;
        this.disabledClickDesc = true;
      }
      this.$emit('handleQueryAiByTaskName', taskName)
    },
    setData() {
      this.isShowAnswerTip = true;
      this.disabledClickDesc = true;
    }
  }
}
</script>
<style scoped lang="scss">
.ai-box {
  width: 100%;
  margin-bottom: 32rpx;
  .ai-head {
    width: 100%;
    @include flex(row, flex-start, center);
    > image {
      width: 64rpx;
      height: 64rpx;
    }
    .ai-name {
      font-weight: 500;
      font-size: 26rpx;
      color: #AAAAAA;
      margin-left: 20rpx;
    }
  }
  .ai-text {
    margin-top: 16rpx;
    width: 100%;
    padding: 24rpx 32rpx;
    background: #F9F9FA;
    border-radius: 0 20rpx 20rpx 20rpx;
    font-size: 28rpx;
    color: #444444;
  }
  .expand-more {
    color: #155bd4;
    font-size: 24rpx;
    line-height: 36rpx;
    margin-top: 20rpx;
    padding-left: 32rpx;
    display: flex;
    align-items: center;
    transition: color 0.3s;
    > image {
      width: 20rpx;
      height: 20rpx;
      margin-left: 4rpx;
    }
  }
  .disabled-expand-more {
    color: #999;
  }
  .more-text {
    margin-top: 16rpx;
    width: 100%;
    padding: 24rpx 32rpx;
    background: #F9F9FA;
    border-radius: 20rpx;
    font-size: 28rpx;
    color: #444444;
  }
}
</style>
