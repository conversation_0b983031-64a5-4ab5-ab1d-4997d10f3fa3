<template>
  <view class="ai-box">
    <view class="ai-head">
      <image class="ai-img" :src="aiImg"></image>
      <view class="ai-name">素灵AI</view>
    </view>
    <view class="ai-chart">
      <view class="ai-chart-title">病机分析：</view>
      <error-comp v-if="detail.requestErr" :detail="detail" @handleRepeatRequestSse="handleQueryInfoByTaskName" />
      <custom-loading type="1" v-if="!isShowChart && !detail.requestErr" />
      <view
          v-if="isShowChart"
          ref="aiPieChart"
          id="aiPieChart"
          class="ai-chart-box"
          :style="{ width: '100%', height: chartHeight + 'px'}"
      ></view>
      <view :chatData="chartData" :change:chatData="myPieEcharts.init"></view>
    </view>
    <a
        v-if="node !== 'p0'"
        :class="`expand-more ${disabledClickDesc ? 'disabled-expand-more' : '' }`"
        @click="handleRepeatQueryAi('p11')"
    >
      病机原理解读
      <image v-if="!disabledClickDesc" src="@/static/image/ai/left.svg"/>
      <image v-else src="@/static/image/ai/disabledRight.png"/>
    </a>
    <view v-if="node !== 'p0' && isShowAnswerTip" class="more-text">
      <ua-markdown :source="detail.answerText" />
      <error-comp
          v-if="detail.answerRequestErr"
          :detail="detail"
          @handleRepeatRequestSse="handleRepeatQueryAi('p11')"
      />
      <custom-loading type="1" v-if="!detail.answerText && !detail.answerRequestErr" />
      <custom-loading
          ref="customLoading"
          type="2"
          v-if="detail.answerText && !detail.answerDone && !detail.answerRequestErr"
      />
    </view>
  </view>
</template>
<script>
import customLoading from "./customLoading/index.vue";
import uaMarkdown from "./ua2-markdown/ua-markdown.vue";
import errorComp from "./error-comp/index.vue";

export default {
  name: "msg-pie-box",
  components: {
    errorComp, uaMarkdown, customLoading
  },
  props: {
    node: {
      type: String,
      default: 'p8',
    },
    detail: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    isShowChart() {
      const flag = this.detail?.p7?.end && this.detail?.p8?.end;
      return !!flag;
    },
    chartData() {
      const p7_data = this.detail.p7?.data || []
      const p8_data = this.detail.p8?.data || []
      return {
        p7_data,
        p8_data
      }
    }
  },
  data() {
    return {
      chartHeight: 260,
      aiImg: 'https://static.rsjxx.com/image/2025/0117/101026_68163.png',
      isShowAnswerTip: false,
      disabledClickDesc: false,
    }
  },
  mounted() {},
  methods: {
    getEchartsHeight(height) {
      this.chartHeight = height;
    },
    handleQueryInfoByTaskName() {
      this.$emit('handleQueryInfoByTaskName', 'p7')
    },
    handleRepeatQueryAi(taskName) {
      if (taskName === 'p11') {
        if (this.disabledClickDesc) return;
        this.isShowAnswerTip = true;
        this.disabledClickDesc = true;
      }
      this.$emit('handleQueryAiByTaskName', taskName)
    },
    setData() {
      this.isShowAnswerTip = true;
      this.disabledClickDesc = true;
    }
  }
}
</script>

<script module="myPieEcharts" lang="renderjs">
import * as echarts from 'echarts';
export default {
  data() {
    return {
      myChart: null,
      id: 'aiPieChart',
      options: {
        legend: [],
        innerData: [],
        outerData: [],
      },
    };
  },
  watch: {
    options: {
      deep: true,
      handler(val) {
        if (val.innerData?.length && val.outerData?.length) {
          this.drawLine(val);
        }
      },
      immediate: true,
    },
  },
  created() {},
  mounted() {
    // this.$nextTick(() => {
    //   this.drawLine();
    // })
  },
  beforeDestroy() {
    if (this.myChart != null && this.myChart !== "" && this.myChart !== undefined) {
      this.myChart?.destroy && this.myChart?.destroy();
    }
    window.removeEventListener('resize', () => {
      this.myChart && this.myChart.resize();
    });
  },
  methods: {
    init(val) {
      const p7_data = val?.p7_data || [];
      const innerData = p7_data?.map(item => ({ value: item.score, name: item.title }));
      this.$set(this.options, 'innerData', innerData);

      const p8_data = val?.p8_data || [];
      const legend = p8_data?.map(item => item.title);
      this.$set(this.options, 'legend', legend);
      const outerData = p8_data?.map(item => ({ value: item.score, name: item.title }));
      this.$set(this.options, 'outerData', outerData);
    },
    drawLine(data) {
      if (this.myChart != null && this.myChart !== "" && this.myChart !== undefined) {
        this.myChart?.destroy && this.myChart?.destroy();
      }
      window.removeEventListener('resize', () => {
        this.myChart && this.myChart.resize();
      });

      const colorList = ['#707CC2', '#EE7A5F', '#EDC760', '#6BCE99', '#7DD4E8', '#BE7BD5', '#CBD6A2', '#C87181'];
      this.$nextTick(() => {
        this.myChart = echarts.getInstanceByDom(document.getElementById(this.id));
        if (!this.myChart) {
          this.myChart = echarts.init(document.getElementById(this.id));
        }
        if (this.myChart) {
          this.myChart.clear();
        }
        window.addEventListener('resize', () => {
          this.myChart && this.myChart.resize();
        });
        let option = {
          grid: {
            right: 0,
            left: 0,
            top: 0,
            bottom: '30%',
          },
          tooltip: {
            trigger: 'item',
            formatter: a => {
              return `${a.name}:${a.percent}%`;
            },
          },
          legend: {
            data: data.legend || [],
          },
          series: [
            {
              name: 'pie',
              type: 'pie',
              // selectedMode: 'single', // 点击内环脱离效果
              radius: [0, '29%'], // 更改内环的大小
              center: ['50%', '50%'],
              itemStyle: {
                borderRadius: 6,
                borderColor: '#fff',
                borderWidth: 2,
              },
              label: {
                position: 'inner',
                fontSize: 12,
              },
              labelLine: {
                show: false,
              },
              color: colorList,
              data: data.innerData,
            },
            {
              name: 'pie',
              type: 'pie',
              radius: ['42%', '55%'], // 更改外环的大小
              center: ['50%', '50%'],
              // labelLine: {
              //   length: 30,
              // },
              itemStyle: {
                borderRadius: 6,
                borderColor: '#fff',
                borderWidth: 2,
              },
              label: {
                borderWidth: 1,
                borderRadius: 4,
                formatter: a => {
                  return `{a|${a.percent}%}\n\n{b|}\n\n{c|${a.name}}`;
                },
                rich: {
                  a: {
                    padding: [-10, -40, -20, -30],
                  },
                  b: {
                    height: 10,
                    width: 10,
                    lineHeight: 10,
                    marginBottom: 10,
                    padding: [0, -20, 0, 20],
                    borderRadius: 10,
                    backgroundColor: {
                      image: 'https://static.rsjxx.com/image/2025/0119/174131_69020.png',
                    },
                  },
                  c: {
                    padding: [-10, -50, 0, -30],
                  },
                },
              },
              labelLine: {
                // 引导线设置
                length: 22,
                length2: 28,
                cap: 'round',
                align: 'right',
                lineStyle: {
                  type: 'dashed',
                },
              },
              color: colorList,
              data: data.outerData,
            },
          ],
        };
        this.myChart.setOption(option);
      });
    },
  },
};
</script>
<style scoped lang="scss">
.ai-box {
  width: 100%;
  margin-bottom: 32rpx;

  .ai-head {
    width: 100%;
    @include flex(row, flex-start, center);

    > image {
      width: 64rpx;
      height: 64rpx;
    }

    .ai-name {
      font-weight: 500;
      font-size: 26rpx;
      color: #AAAAAA;
      margin-left: 20rpx;
    }
  }

  .ai-chart {
    padding: 24rpx 0;
    width: 100%;
    height: 100%;

    .ai-chart-title {
      height: 40rpx;
      font-size: 28rpx;
      color: #333333;
      line-height: 40rpx;
    }

    .ai-chart-box {
      margin-top: 16rpx;
      width: 100%;
      height: 100%;
      min-height: 100rpx;
    }
  }

  .expand-more {
    color: #155bd4;
    font-size: 24rpx;
    line-height: 36rpx;
    margin-top: 20rpx;
    padding-left: 32rpx;
    display: flex;
    align-items: center;
    transition: color 0.3s;

    > image {
      width: 20rpx;
      height: 20rpx;
      margin-left: 4rpx;
    }
  }

  .disabled-expand-more {
    color: #999;
  }
  .more-text {
    margin-top: 16rpx;
    width: 100%;
    padding: 24rpx 32rpx;
    background: #F9F9FA;
    border-radius: 20rpx;
    font-size: 28rpx;
    color: #444444;
  }
}
</style>
