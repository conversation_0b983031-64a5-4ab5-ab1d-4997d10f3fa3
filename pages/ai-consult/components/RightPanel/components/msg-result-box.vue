<template>
  <view class="ai-box">
    <view class="ai-head">
      <image class="ai-img" :src="aiImg"></image>
      <view class="ai-name">素灵AI</view>
    </view>
    <view class="ai-content">
      <view class="ai-content-title">综上所述，订制方案的建议如下：</view>
      <view class="ai-container">
        <view class="head" v-if="!p17.done && !p17.data.syndrome">
          <custom-loading type="1"/>
        </view>
        <view v-else class="head">
          <view class="head-title">
            <view class="head-label">辨证：</view>
            <view class="head-text">{{ p17.data.syndrome }}</view>
          </view>
          <view class="head-title">
            <view class="head-label">治法：</view>
            <view class="head-text">{{ p17.data.treatment }}</view>
          </view>
        </view>
        <view class="line" v-if="p17.done && p17.data.syndrome"></view>
        <view class="result" v-if="p17.done && p17.data.syndrome">
          <view class="result-head" v-if="goodsName">
            <view class="result-head-title">{{ goodsName }}</view>
            <view v-if="!isReadOnly" :class="`result-head-text ${exportDisabled?'result-head-text--disabled':''}`"
                  @click="copy">
              <uni-icons :color="''" type="plusempty"></uni-icons>
              导入
            </view>
          </view>
          <view class="goods">
            <view class="goods-item" v-for="(good,index) in goodsList" :key="index">
              <image
                :src="main_img(good.plans[0])"
                mode="aspectFill"
                @click="openGoodsDetail(good.plans[0])"
              ></image>
              <view class="goods-info">
                <view class="goods-title">
                  <view
                    :class="`title ${!good.plans[0].goods.id || good.plans[0].goods.stock <= 0 ? 'empty-title' : ''}`"
                    @click="openGoodsDetail(good.plans[0])"
                  >
                    {{ good.plans.length ? good.plans[0].name : '' }}
                  </view>
                  <view class="empty"
                        v-if="good.plans[0].goods&&(!good.plans[0].goods.id || good.plans[0].goods.stock<=0)">缺货
                  </view>
                </view>
                <view class="goods-labels" v-if="good.plans && good.plans.length && good.plans[0].info">
                  <view class="goods-label" v-for="(tag, i) in good.plans[0].info.tags" :key="i">{{ tag }}</view>
                </view>
              </view>
            </view>
          </view>

          <error-comp v-if="p29.requestErr" :detail="p29" @handleRepeatRequestSse="handleQueryInfoByTaskName('p29')"/>
          <custom-loading type="1" v-if="p17.done && !p29.requestErr && goodsList.length === 0"/>
          <view class="sLine" v-if="p29.end"></view>

          <view class="ai-text" v-if="p29.end">
            <ua-markdown :source="p29.answerText"/>
            <error-comp v-if="p29.answerRequestErr" :detail="p29" @handleRepeatRequestSse="handleRepeatQueryAi('p25-p29')"/>
            <custom-loading type="1" v-if="!p29.answerText && !p29.answerRequestErr"/>
            <custom-loading
              v-if="!p29.answerRequestErr && p29.answerText && !p29.answerDone"
              ref="customLoading"
              type="2"
            />
          </view>
        </view>
      </view>
    </view>
    <goods-detail ref="goods-detail" :detail="checkGoodsDetail"/>
  </view>
</template>

<script>
import customLoading from "./customLoading/index.vue";
import uaMarkdown from "./ua2-markdown/ua-markdown.vue";
import errorComp from "./error-comp/index.vue";
import GoodsDetail from "./goods-detail.vue";

export default {
  name: "msg-result-box",
  components: {GoodsDetail, errorComp, uaMarkdown, customLoading},
  props: {
    p17: {
      type: Object,
      default: () => ({
        data: {}
      })
    },
    p29: {
      type: Object,
      default: () => ({
        data: {}
      })
    },
    isReadOnly: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    goodsName() {
      const str = this.p29?.data?.groups?.[0]?.map(key => this.nameMap?.[key])?.join('+')
      return str ? str + '保健方案' : ''
    },
    goodsList() {
      return this.p29?.data?.plans?.filter(good => good?.plans?.[0]) || []
    },
    exportDisabled() {
      return this.p29?.data?.plans?.every(good => !good?.plans?.[0]?.goods?.id || good?.plans?.[0]?.goods?.stock <= 0)
    },
    main_img() {
      return plan => {
        if (!plan?.goods) return this.defaultGoodsImg
        if (!plan?.goods?.main_img) return this.defaultGoodsImg
        return plan?.goods?.main_img
      }
    }
  },
  data() {
    return {
      defaultGoodsImg: 'https://img-sn-i01s-cdn.rsjxx.com/image/2025/0117/134810_56784.png-B.200',
      aiImg: 'https://static.rsjxx.com/image/2025/0117/101026_68163.png',
      nameMap: {
        pj: '淋漓祛湿',
        bjg: '保健膏',
        gf: '膏方',
        ys: '药膳',
        aj: '艾灸',
        rjg: '膏药热敷',
        jl: '经筋骨',
        wst: '微生态'
      },
      checkGoodsDetail: {}
    }
  },
  methods: {
    copy() {
      const disabeld = this.exportDisabled
      if (disabeld) return
      const goods = this.p29?.data?.plans?.filter(good => good?.plans?.[0]?.goods?.id && good?.plans?.[0]?.goods.stock > 0)?.map(good => good?.plans?.[0]?.goods)
      const obj = {};
      obj.goods_info = goods
      uni.$emit('importAiRecordEvent', obj)
    },
    openGoodsDetail(row) {
      this.checkGoodsDetail = {}
      this.checkGoodsDetail = row
      this.$refs["goods-detail"].showDrawer()
    },
    handleQueryInfoByTaskName(task_name) {
      this.$emit('handleQueryInfoByTaskName', task_name)
    },
    handleRepeatQueryAi(taskName) {
      if (taskName === 'p5') {
        if (this.disabledClickDesc) return;
        this.isShowAnswerTip = true;
        this.disabledClickDesc = true;
      }
      this.$emit('handleQueryAiByTaskName', taskName)
    }
  }
}
</script>

<style scoped lang="scss">
.ai-box {
  width: 100%;
  margin-bottom: 32rpx;

  .ai-head {
    width: 100%;
    @include flex(row, flex-start, center);

    > image {
      width: 64rpx;
      height: 64rpx;
    }

    .ai-name {
      font-weight: 500;
      font-size: 26rpx;
      color: #AAAAAA;
      margin-left: 20rpx;
    }
  }

  .ai-content {
    width: 100%;

    .ai-content-title {
      width: 100%;
      height: 40rpx;
      font-size: 28rpx;
      color: #333333;
      line-height: 40rpx;
      margin: 22rpx 0;
    }

    .ai-container {
      background: #FFFFFF;
      border-radius: 8rpx;
      border: 1px solid #EBEDF0;
      padding: 32rpx;

      .head {
        width: 100%;
        margin-bottom: 32rpx;

        .head-title {
          @include flex(row, flex-start, center);

          .head-label {
            font-size: 28rpx;
            color: #999999;
            line-height: 40rpx;
          }

          .head-text {
            font-weight: 500;
            font-size: 28rpx;
            color: #333333;
            line-height: 40rpx;
          }
        }

        .head-title:first-child {
          margin-bottom: 20rpx;
        }
      }

      .line {
        width: 100%;
        height: 2rpx;
        background-color: #EBEDF0;
        margin-bottom: 32rpx;
      }

      .result {
        width: 100%;

        .result-head {
          @include flex(row, space-between, center);
          margin-bottom: 32rpx;

          .result-head-title {
            flex: 1;
            font-weight: 500;
            font-size: 30rpx;
            color: #333333;
            line-height: 40rpx;
          }

          .result-head-text {
            width: 108rpx;
            height: 56rpx;
            background: #FFFFFF;
            border-radius: 8rpx;
            border: 2rpx solid #155BD4;
            font-size: 26rpx;
            color: #155BD4;
            line-height: 36rpx;
            @include flex(row, center, center);
            transition: color 0.3s, background 0.3s;

            > image {
              width: 24rpx;
              height: 24rpx;
              margin-right: 8rpx;
            }

            &--disabled {
              color: $uni-text-color-disable;
              border-color: $uni-text-color-disable;
            }
          }

          .result-head-text:not(.result-head-text--disabled):active {
            color: $uni-text-color-disable;
            background: $uni-primary;
          }

          .exportDisabled {
            background: $uni-text-color-disable;
          }
        }

        .goods {
          width: 100%;

          .goods-item {
            width: 100%;
            height: 100%;
            margin-bottom: 16rpx;
            @include flex(row, flex-start, center);

            > image {
              width: 120rpx;
              height: 120rpx;
              flex-shrink: 0;
              margin-right: 24rpx;
              border-radius: 4rpx;
            }

            .goods-info {
              flex: 1;
              min-height: 120rpx;
              @include flex(column, space-around, flex-start);

              .goods-title {
                height: 40rpx;
                @include flex(row, flex-start, center);

                .title {
                  flex: 1;
                  overflow: hidden; /* 隐藏超出的内容 */
                  text-overflow: ellipsis; /* 显示省略号 */
                  white-space: nowrap; /* 不换行 */
                  font-size: 28rpx;
                  color: #155BD4;
                }

                .empty-title {
                  color: #CCCCCC;
                }

                .empty {
                  font-weight: 500;
                  font-size: 10px;
                  color: #E63E3E;
                  padding: 4rpx;
                  border-radius: 2px;
                  border: 1px solid #E63E3E;
                  margin-left: 10rpx;
                }
              }

              .goods-labels {
                width: 100%;
                overflow: hidden;
                @include flex(row, flex-start, center);

                .goods-label {
                  padding: 4rpx 16rpx;
                  font-size: 24rpx;
                  color: #999999;
                  line-height: 32rpx;
                  background: #F9F9FA;
                  border-radius: 20rpx;
                  margin-right: 32rpx;
                }
              }
            }
          }

          .goods-item:last-child {
            margin-bottom: 0;
          }
        }

        .sLine {
          width: 100%;
          height: 0;
          border: 1px dashed #EBEDF0;
          margin: 32rpx 0;
        }

        .ai-text {
          width: 100%;
          background: #fff;
          font-size: 26rpx;
          line-height: 40rpx;
          color: #333333;
        }
      }
    }
  }
}
</style>
