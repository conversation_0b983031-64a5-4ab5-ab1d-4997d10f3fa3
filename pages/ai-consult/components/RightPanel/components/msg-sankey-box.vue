<template>
  <view class="ai-box">
    <view class="ai-head">
      <image class="ai-img" :src="aiImg"></image>
      <view class="ai-name">素灵AI</view>
    </view>
    <view class="ai-chart">
      <view class="ai-chart-title">症群分析：</view>
      <error-comp v-if="detail.requestErr" :detail="detail" @handleRepeatRequestSse="handleQueryInfoByTaskName('p8')" />
      <custom-loading type="1" v-if="!isShowChart" />
      <view
          v-if="isShowChart"
          ref="aiSankeyChart"
          id="aiSankeyChart"
          class="ai-chart-box"
          :style="{ width: '100%', height: chartHeight + 'px'}"
      ></view>
      <view :chatData="chartData" :change:chatData="mySankeyEcharts.init"></view>
    </view>
    <a
        v-if="node !== 'p0'"
        :class="`expand-more ${disabledClickDesc ? 'disabled-expand-more' : '' }`"
        @click="handleRepeatQueryAi('p5')"
    >
      更多症群解读内容
      <image v-if="!disabledClickDesc" src="@/static/image/ai/left.svg"/>
      <image v-else src="@/static/image/ai/disabledRight.png"/>
    </a>
    <view v-if="node !== 'p0' && isShowAnswerTip" class="more-text">
      <ua-markdown :source="detail.answerText" />
      <error-comp
          v-if="detail.answerRequestErr"
          :detail="detail"
          @handleRepeatRequestSse="handleRepeatQueryAi('p5')"
      />
      <custom-loading type="1" v-if="!detail.answerText && !detail.answerRequestErr" />
      <custom-loading
          ref="customLoading"
          type="2"
          v-if="detail.answerText && !detail.answerDone && !detail.answerRequestErr"
      />
    </view>
  </view>
</template>
<script>
import errorComp from "./error-comp/index.vue";
import customLoading from "./customLoading/index.vue";
import uaMarkdown from "./ua2-markdown/ua-markdown.vue";
export default {
  name: "msg-sankey-box",
  components: {
    uaMarkdown,
    errorComp,
    customLoading,
  },
  props: {
    node: {
      type: String,
      default: 'p8',
    },
    detail: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    isShowChart() {
      return this.detail?.data?.length > 0
    },
    chartData() {
      return this.detail?.data || []
    }
  },
  data() {
    return {
      chartHeight: 0,
      aiImg: 'https://static.rsjxx.com/image/2025/0117/101026_68163.png',
      isShowAnswerTip: false,
      disabledClickDesc: false,
    }
  },
  mounted() {
  },
  methods: {
    getEchartsHeight(height) {
      this.chartHeight = height;
    },
    handleQueryInfoByTaskName(task_name) {
      this.$emit('handleQueryInfoByTaskName', task_name)
    },
    handleRepeatQueryAi(taskName) {
      if (taskName === 'p5') {
        if (this.disabledClickDesc) return;
        this.isShowAnswerTip = true;
        this.disabledClickDesc = true;
      }
      this.$emit('handleQueryAiByTaskName', taskName)
    },
    setData() {
      this.isShowAnswerTip = true;
      this.disabledClickDesc = true;
    }
  }
}
</script>

<script module="mySankeyEcharts" lang="renderjs">
import * as echarts from 'echarts';
export default {
  data() {
    return {
      myChart: null,
      id: 'aiSankeyChart',
      options: {
        data: [],
        links: [],
      },
    };
  },
  watch: {
    options: {
      deep: true,
      handler(val) {
        if (val.data?.length) {
          this.drawLine(val);
        }
      },
      immediate: true,
    },
  },
  created() {},
  mounted() {
    // this.$nextTick(() => {
    //   this.drawLine();
    // })
  },
  beforeDestroy() {
    if (this.myChart != null && this.myChart !== "" && this.myChart !== undefined) {
      this.myChart?.destroy && this.myChart?.destroy();
    }
    window.removeEventListener('resize', () => {
      this.myChart && this.myChart.resize();
    });
  },
  methods: {
    init(val) {
      if (this.myChart != null && this.myChart !== "" && this.myChart !== undefined) {
        this.myChart?.destroy && this.myChart?.destroy();
      }
      window.removeEventListener('resize', () => {
        this.myChart && this.myChart.resize();
      });
      const list = val?.map(item => item.symptom?.map(itm => ({ value: itm, ...item }))).flat();
      let data = list?.map(item => item.value);
      val?.forEach(item => {
        data.push(item.title);
      });
      data = [...new Set(data)]?.map(name => ({ name }));
      this.$set(this.options, 'data', data);
      const link = list?.map(item => {
        return {
          source: item.value,
          target: item?.title,
          value: 2,
        };
      });
      const echartsHeight = link.length > 10  ? link.length * 22 : link.length * 30
      this.$set(this.options, 'links', link);
      this.$ownerInstance.callMethod('getEchartsHeight', echartsHeight)
      setTimeout(() => {
        this.myChart && this.myChart.resize()
      }, 100)
    },
    drawLine(data) {
      this.$nextTick(() => {
        this.myChart = echarts.getInstanceByDom(document.getElementById(this.id));
        if (!this.myChart) {
          this.myChart = echarts.init(document.getElementById(this.id));
        }
        if (this.myChart) {
          this.myChart.clear();
        }
        window.addEventListener('resize', () => {
          this.myChart && this.myChart.resize();
        });
        let option = {
          color: ['#707CC2', '#EE7A5F', '#EDC760', '#6BCE99', '#7DD4E8', '#BE7BD5', '#CBD6A2', '#C87181'],
          grid: {
            right: 0,
            left: 0,
            top: 0,
            bottom: 0,
          },
          series: {
            type: 'sankey',
            layout: 'none',
            draggable: false,
            emphasis: {
              focus: 'adjacency',
            },
            label: {
              fontSize: 11,
            },
            data: data.data || [],
            links: data.links || [],
          },
        };
        this.myChart.setOption(option);
      });
    },
  },
};
</script>
<style scoped lang="scss">
.ai-box {
  width: 100%;
  margin-bottom: 32rpx;
  .ai-head {
    width: 100%;
    @include flex(row, flex-start, center);
    > image {
      width: 64rpx;
      height: 64rpx;
    }
    .ai-name {
      font-weight: 500;
      font-size: 26rpx;
      color: #AAAAAA;
      margin-left: 20rpx;
    }
  }
  .ai-chart {
    padding: 24rpx 0;
    width: 100%;
    height: 100%;
    .ai-chart-title {
      height: 40rpx;
      font-size: 28rpx;
      color: #333333;
      line-height: 40rpx;
    }
    .ai-chart-box {
      margin-top: 16rpx;
      width: 100%;
      height: 100%;
      min-height: 100rpx;
    }
  }
  .expand-more {
    color: #155bd4;
    font-size: 24rpx;
    line-height: 36rpx;
    margin-top: 20rpx;
    padding-left: 32rpx;
    display: flex;
    align-items: center;
    transition: color 0.3s;
    > image {
      width: 20rpx;
      height: 20rpx;
      margin-left: 4rpx;
    }
  }
  .disabled-expand-more {
    color: #999;
  }
  .more-text {
    margin-top: 16rpx;
    width: 100%;
    padding: 24rpx 32rpx;
    background: #F9F9FA;
    border-radius: 20rpx;
    font-size: 28rpx;
    color: #444444;
  }
}
</style>
