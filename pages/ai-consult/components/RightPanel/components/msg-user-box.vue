<template>
  <view class="ai-box ai-box-reserve">
    <view class="ai-head">
      <image v-if="detail.img" class="ai-img" :src="detail.img"></image>
      <view v-else class="defaultImg">
        <uni-icons :size="16" type="person" color="#CCCCCC"></uni-icons>
      </view>
      <view class="ai-name">{{ detail.username || '提问者'}}</view>
    </view>
    <view class="ai-text">
      {{ detail.data }}
    </view>
  </view>
</template>

<script>
export default {
  name: "msg-user-box",
  props: {
    detail: {
      type: Object,
      default: () => ({})
    },
    aiUserInfo: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
      aiImg: 'https://static.rsjxx.com/image/2025/0117/101026_68163.png',
    }
  }
}
</script>

<style scoped lang="scss">
.ai-box {
  width: 100%;
  margin-bottom: 32rpx;
  .ai-head {
    width: 100%;
    @include flex(row, flex-start, center);
    > image {
      width: 64rpx;
      height: 64rpx;
      border-radius: 50%;
      object-fit: cover;
    }
    .ai-name {
      font-weight: 500;
      font-size: 26rpx;
      color: #AAAAAA;
      margin-right: 20rpx;
    }
    .defaultImg {
      width: 52rpx;
      height: 52rpx;
      border: 1rpx solid #eeeeee;
      border-radius: 50%;
      @include flex(row, center, center);
    }
  }
  .ai-text {
    margin-top: 16rpx;
    width: 100%;
    padding: 24rpx 32rpx;
    background: #F9F9FA;
    border-radius: 0 20rpx 20rpx 20rpx;
    font-size: 28rpx;
    color: #444444;
  }
}
.ai-box-reserve {
  .ai-head {
    flex-direction: row-reverse;
  }
  .ai-text {
    border-radius: 20rpx 0 20rpx 20rpx;
    background: #E4EEFE;
  }
}
</style>
