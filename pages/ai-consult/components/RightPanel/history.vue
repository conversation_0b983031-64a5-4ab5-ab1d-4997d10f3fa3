<template>
  <view class="history-box">
    <scroll-view scroll-with-animation scroll-y id="ai-container" class="ai-container">
      <view v-for="item in historyList" :key="item.id" class="history-item">
        <view class="history-item-content">
          <view class="header">
            <view class="content-header" @click="changeItem(item.id)">
              <view class="content-header-time">
                <view>{{ dayjs(item.create_time * 1000).format('YYYY/MM/DD') }}</view>
                <image
                  v-if="item.contain_herbs === '1'"
                  src="@/static/image/ai/v4-pres.svg"
                  style="margin-left: 6px; width: 16px; height: 16px; vertical-align: -3px"
                />
                <image
                  v-if="item.record_source === '3'"
                  src="https://static.rsjxx.com/image/2025/0306/165257_80238.png-B.w300"
                  style="margin-left: 6px; width: 16px; height: 16px; vertical-align: -3px"
                />
              </view>
              <view class="content-header-name">
                <view class="name">{{item.patient_name}}</view>
                <image v-if="item.id === activeId" class="img" :src="bottomArrow" mode="aspectFill"></image>
                <image v-else class="img" :src="leftArrow" mode="aspectFill"></image>
              </view>
            </view>
            <view v-if="item.id === activeId" class="content-other">
              <view class="action">
                <view class="time">{{ time_format }}｜已生成订单</view>
                <view class="btn">
  <!--                <a class="link">-->
  <!--                  <image class="btn-image" src="https://static.rsjxx.com/image/2025/0115/174018_73666.png" mode="aspectFill"></image>-->
  <!--                  诊断报告-->
  <!--                </a>-->
                  <a v-if="!isReadOnly" class="link" @click="copy('all')">
                    <image class="btn-image" src="https://static.rsjxx.com/image/2025/0115/174018_24939.png" mode="aspectFill"></image>
                    复制全部
                  </a>
                </view>
              </view>
              <view class="info">
                <view v-if="loading" class="mask-loading">
                  <u-loading-icon size="32" color="#115bd4"></u-loading-icon>
                </view>
                <view class="title">
                  <view class="left-title">档案</view>
                  <a v-if="!isReadOnly" class="right-title" @click="copy('medical_case')">复制</a>
                </view>
                <view class="title-content">
                  <view class="label-box">
                    <view class="label">主诉:</view>
                    <view class="text">{{ detail.medical_case ? detail.medical_case.engrave_text : ''}}</view>
                  </view>
                  <view class="label-box">
                    <view class="label">其他:</view>
                    <view class="text">{{ detail.medical_case ? detail.medical_case.diagnostic_methods : ''}}</view>
                  </view>
                  <view class="label-box">
                    <view class="label">现病:</view>
                    <view class="text">{{ detail.medical_case ? detail.medical_case.present_medical_history : ''}}</view>
                  </view>
                  <view class="label-box">
                    <view class="label">既往:</view>
                    <view class="text">{{ detail.medical_case ? detail.medical_case.past : '' }}</view>
                  </view>
                  <view class="label-box">
                    <view class="label">体查:</view>
                    <view class="text">{{ detail.medical_case ? detail.medical_case.physique : '' }}</view>
                  </view>
                  <view class="label-box">
                    <view class="label">检查单:</view>
                    <view class="checklist" v-if="detail.medical_case.qa_title" @click="showCheckListDetail(detail.id)">
                      <image src="https://static.rsjxx.com/image/2025/0115/174018_24939.png" mode="aspectFill"></image>
                      {{ detail.medical_case ? detail.medical_case.qa_title : '' }}
                    </view>
                  </view>
                </view>
                <!--   舌像资料    -->
                <view v-if="!isHideTongue">
                  <view class="line"></view>
                  <view class="title">
                    <view class="left-title">影像资料</view>
                    <a v-if="!isReadOnly" class="right-title" @click="copy('tongue')">复制</a>
                  </view>
                  <view class="tongue">
                    <view class="tongue-content">
                      <!-- 患者图片 -->
                      <patientPhoto :tongue_image="tongue_image"></patientPhoto>
                    </view>
                    <!--                <view class="more">-->
                    <!--                  <view>查看更多</view>-->
                    <!--                  <view>共15张</view>-->
                    <!--                </view>-->
                  </view>
                </view>
  <!--              <view class="line"></view>-->
  <!--              <view class="title">-->
  <!--                <view class="left-title">诊疗项目</view>-->
  <!--                <a class="right-title">复制</a>-->
  <!--              </view>-->
  <!--              <view class="item-content">-->
  <!--                <view>淋漓祛湿艾灸套餐</view>-->
  <!--                <view>1天1次，共3次</view>-->
  <!--              </view>-->
                <view v-if="goodsText">
                  <view class="line"></view>
                  <view class="title">
                    <view class="left-title">商品</view>
                    <a v-if="!isReadOnly" class="right-title" @click="copy('goods')">复制</a>
                  </view>
                  <view class="item-content">
                    <view>{{goodsText}}</view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view v-if="historyList.length === 0" class="empty">
        <image src="https://static.rsjxx.com/image/2025/0121/174852_72863.png" />
        <view class="text">暂无数据</view>
      </view>
      <check-list-drawer v-model="checkVisible" :mr_id="qa_mr_id" :is-detail="true" :is-disabled="true"></check-list-drawer>
    </scroll-view>
  </view>
</template>

<script>
import patientPhoto from '@/components/patient-photo/index.vue'
import checkListDrawer from "@/pages/ai-consult/components/user-profile/components/checkListDrawer.vue";
import AiService from "../../../../api/ai";
import 'dayjs/locale/zh-cn'
import dayjs from "dayjs";
dayjs.locale('zh-cn')

export default {
  components: {
    patientPhoto,
    checkListDrawer
  },
  props: {
    isReadOnly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dayjs,
      leftArrow: 'https://static.rsjxx.com/image/2025/0115/165019_59796.png',
      bottomArrow: 'https://static.rsjxx.com/image/2025/0115/165019_51887.png',
      historyList: [],
      detail: {
        medical_case:{}
      },
      activeId: '',
      // mock 影像资料数据
      tongue_image: {
        tongue_on_images: [],
        tongue_lower_images: [],
        position_images: [],
        additional_images: [],
        tongue_diag_images: [],
        tongue_ai: [],
      },
      mr_id: '',
      pt_id: '',
      loading: false,
      copyDetail: {},
      checkVisible: false,
      qa_mr_id: '',
    }
  },
  mounted() {
    // 获取页面参数
    const params = this.$store.getters['global/getQuery'] || {}
    this.mr_id = params.mr_id;
    this.pt_id = params.pt_id;
    this.getRecordList()
  },
  computed: {
    goodsText() {
      return this.detail?.goods_info?.attrs?.map(good => good.name + '*' + good.amount).join(', ');
    },
    time_format() {
      const time = dayjs(this.detail.create_time * 1000).locale('zh-cn')
      return time.format('ddd HH:mm');
    },
    isHideTongue(){
      return Object.values(this.tongue_image).every(array => array.length === 0);
    },
  },
  methods: {
    getRecordList() {
      AiService.getRecordList({
        pt_id: this.pt_id,
        source: 'ipad_history',
        status: 1,
        pageSize: 1000,
      }).then(res => {
        this.historyList = res?.list || []
        if (res?.list?.length > 0) {
          this.activeId = res.list?.[0]?.id
          this.getRecordInfo(this.activeId)
          this.getReplication(this.activeId)
        }
      })
    },
    getRecordInfo(id) {
      if (!id) return
      this.loading = true
      AiService.getRecordInfo({
        mr_id: id,
      }).then(res => {
        this.detail = res || {};
        this.tongue_image = res?.tongue_image || {}
      }).finally(() => {
        this.loading = false
      })
    },
    copy(type) {
      const obj = this.buildCopyObject(type);
      uni.$emit('importAiRecordEvent', obj);
      uni.showToast({
        title: '导入成功',
        icon: 'none',
      });
    },
    buildCopyObject(type) {
      const obj = {};
      if (type === 'all' || type === 'medical_case') {
        obj.medical_case = this.detail.medical_case || {};
        obj.isCopyAll = true;
      }
      if (type === 'all' || type === 'tongue') {
        obj.tongue_image = this.detail.tongue_image || {};
      }
      if (type === 'all' || type === 'goods') {
        obj.goods_info = this.copyDetail.goods_info || {};
      }
      return obj;
    },
    getReplication(id) {
      if (!id) return
      AiService.getReplication({
        mr_id: id,
      }).then(res => {
        this.copyDetail = res || {};
      })
    },
    changeItem(id) {
      if (this.activeId === id) {
        this.activeId = ''
        return
      }
      this.activeId = id;
      this.getRecordInfo(this.activeId)
      this.getReplication(this.activeId)
    },
    showCheckListDetail(id){
      this.qa_mr_id = id
      this.checkVisible = true
    },
  }
}
</script>

<style lang="scss" scoped>
.history-box {
  width: 100%;
  height: 100%;
  background-color: #FFFFFF;
  padding-bottom: 40rpx;
  .ai-container {
    width: 100%;
    height: 100%;
  }
}
.history-item {
  width: 100%;
  padding: 0 12rpx;
  background: #FFFFFF;
  border-radius: 6rpx;
}
.history-item-content {
  width: 100%;
  @include flex(column, start, start);
  border-bottom: 2rpx solid #EBEDF0;
  .header {
    width: 100%;
    min-height: 90rpx;
    @include flex(column, center, start);
    .content-header {
      width: 100%;
      height: 100%;
      padding: 0 12rpx;
      min-height: 90rpx;
      @include flex(row, space-between, center);
      .content-header-time {
        display: flex;
        align-items: center;
        font-weight: 600;
        font-size: 30rpx;
        color: #333333;
      }
      .content-header-name {
        @include flex(row, center, center);
        .name {
          font-size: 24rpx;
          color: #999999;
          margin-left: 9rpx;
        }
        .img {
          width: 15rpx;
          height: 15rpx;
        }
      }
    }
    .content-other {
      width: 100%;
      padding-bottom: 24rpx;
      @include flex(column, start, start);
      .action {
        width: 100%;
        padding: 0 12rpx;
        margin-bottom: 6rpx;
        @include flex(row, space-between, center);
        .time {
          font-size: 24rpx;
          color: #999999;
        }
        .btn {
          @include flex(row, center, center);
          .btn-image {
            width: 21rpx;
            height: 21rpx;
            margin-right: 8rpx;
          }
          .bottom {
            font-size: 26rpx;
            color: #666666;
            background: #FFFFFF;
            @include flex(row, center, center);
          }
          .link {
            font-size: 24rpx;
            color: #155BD5;
            transition: color 0.3s;
            margin-left: 24rpx;
            @include flex(row, center, center);
            .btn-image {
              width: 18rpx;
              height: 18rpx;
              margin-right: 5rpx;
            }
          }
          .link:active {
            color: $uni-text-color-disable;
          }
        }
      }
      .info {
        width: 100%;
        background: #FAFAFB;
        border-radius: 6rpx;
        padding: 18rpx 24rpx;
        margin-top: 12rpx;
        position: relative;
        .title {
          margin-bottom: 12rpx;
          @include flex(row, space-between, center);
          .left-title {
            font-weight: 600;
            font-size: 28rpx;
            color: #333333;
          }
          .right-title {
            font-size: 24rpx;
            color: #155BD4;
            transition: color 0.3s;
          }
          .right-title:active {
            color: $uni-text-color-disable;
          }
        }
        .title-content {
          .label-box {
            @include flex(row, start, start);
            margin-bottom: 14rpx;
            .label {
              text-align: right;
              flex-shrink: 0;
              width: 112rpx;
              font-size: 28rpx;
              color: #999999;
              padding-right: 20rpx;
            }
            .text {
              flex: 1;
              flex-wrap: wrap;
              font-size: 28rpx;
              color: #555555;
            }
            .checklist {
              @include flex(row, center, center);
              padding: 9rpx 14rpx;
              background: #F5F6F8;
              border-radius: 3rpx;
              border: 1rpx solid #DCDFE6;
              font-size: 28rpx;
              >image {
                width: 32rpx;
                height: 32rpx;
                margin-right: 14rpx;
              }
            }
          }
        }
        .line {
          width: 100%;
          height: 0;
          margin: 16rpx 0;
          border-bottom: 1px dashed #EBEEF5;
        }
        .tongue {
          position: relative;
          margin-top: 8rpx;
          .tongue-content {
            @include flex(row, start, center);
            flex-wrap: nowrap;
            overflow: hidden;
            .tongue-img {
              width: 54rpx;
              height: 54rpx;
            }
          }
          .more {
            @include flex(column, flex-end, center);
            height: 54rpx;
            padding-bottom: 4rpx;
            position: absolute;
            right: 0;
            top: 0;
            background-color: #FAFAFB;
            box-shadow: -5px 0 10px 10px #FAFAFB;
            > view:first-child {
              @include flex(row, center, center);
              font-size: 22rpx;
              color: #333333;
              line-height: 40rpx;
            }
            >view:nth-child(2) {
              @include flex(row, center, center);
              font-weight: 300;
              font-size: 20rpx;
              color: #999999;
              line-height: 24rpx;
            }
          }
        }
        .item-content {
          @include flex(row, space-between, center);
          font-size: 28rpx;
          line-height: 36rpx;
          color: #555555;
          > view:first-child {
            flex: 1;
          }
          >view:nth-child(2) {
            flex-shrink: 0;
          }
        }
      }
    }
  }
}
.mask-loading {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background: hsla(0, 0%, 100%, .9);
  z-index: 1;
  @include flex(row, center, center);
}
.empty {
  width: 100%;
  height: 800rpx;
  @include flex(column, center, center);
  font-size: 28rpx;
  color: #AAAAAA;
  line-height: 40rpx;
  position: relative;
  > image {
    width: 442rpx;
    height: 340rpx;
  }
  .text {
    width: 100%;
    position: absolute;
    bottom: 260rpx;
    text-align: center;
  }
}
</style>
