<template>
  <view>
    <uni-popup ref="inputDialog" type="dialog" class="custom-pop">
      <uni-popup-dialog ref="inputClose" mode="line" title="用户信息确认" value="对话框预置提示内容!" placeholder="请输入内容"
        style="width: 1110rpx" @close="closeDialog" @confirm="submitDialog" confirm-text="确认创建" :before-close="true"
        class="custom-dialog" :class="{'topDialog': keyboardExist}">

        <uni-forms ref="uniForm" :modelValue="formData" :rules="formRules" label-align="right">
          <uni-forms-item label="手机号:" required name="mobile">
            <taogewan-combox-remote emptyTips="当前用户暂无信息,请使用后台进行创建" v-model="formData.mobile" placeholder="请输入手机号"
              :candidates="patientList" @input="uniComboxInput" @select="uniComboxSelect" @clear="uniComboxClear"
              @keyBoardChange="uniKeyBoardChange" @emptyCreate="emptyCreate" />
          </uni-forms-item>
          <uni-forms-item label="姓名:" name="name">
            <uni-easyinput type="text" v-model="formData.name" placeholder="请输入姓名" disabled />
          </uni-forms-item>
          <uni-forms-item label="性别:" name="name">
            <uni-easyinput type="text" v-model="formData.sex" placeholder="请输入性别" disabled />
          </uni-forms-item>
          <uni-forms-item label="年龄:" name="name">
            <uni-easyinput type="text" v-model="formData.age" placeholder="请输入年龄" disabled />
          </uni-forms-item>
          <uni-forms-item label="建单人:" required name="created_mid">
            <uni-data-select :localdata="clinicStaffList" :clear="false" placeholder="请选择建单人"
              v-model="formData.created_mid" :arrayConfig="configParams" />
          </uni-forms-item>
        </uni-forms>
        <div class="dialog-footer">
          <button class="mr10" @click="closeDialog">取消</button>
          <button type="primary" @click="submitDialog" :loading="submitLoading" :disabled="submitLoading">确认创建</button>
        </div>
      </uni-popup-dialog>
    </uni-popup>
    <uni-popup ref="underwayDialog" type="dialog">
      <uni-popup-dialog ref="inputClose" mode="input" confirm-text="继续咨询" cancel-text="选择其他用户"
        @confirm="continueConsult" class="underwayDialog" @close="clearUserInfo">
        <view style="font-size: 28rpx;text-align: center;margin: 20rpx 0">当前用户正在咨询中，是否继续</view>
      </uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script>
  import S from 'utils/utils'
  import {
    getUid
  } from 'utils/runtime'
  import {
    debounce
  } from 'lodash'
  import patientService from "@/api/patient";
  const initFormData = {
    uid: '',
    pt_id: '',
    mobile: '',
    name: '',
    age: '',
    sex: '',
    created_mid: '',
  };

  export default {
    name: 'editActivityModal',
    mixins: [],
    components: {},
    props: {
      visible: {
        type: Boolean,
        default: false,
      },
      id: {
        type: String,
        default: '',
      },
    },

    data() {
      return {
        formData: {
          ...initFormData
        },
        submitLoading: false,
        clinicStaffList: [],
        formRules: { // 校验规则
          mobile: {
            rules: [{
              required: true,
              errorMessage: '请输入手机号'
            }]
          },
          age: {
            rules: [{
              required: true,
              errorMessage: '年龄不能为空'
            }, {
              format: 'number',
              errorMessage: '年龄只能输入数字'
            }]
          },
          created_mid: {
            rules: [{
              required: true,
              errorMessage: '请选择建单人'
            }]
          }
        },
        patientList: [],
        configParams: {
          labelKey: "name",
          valueKey: "id",
        },
        underway_mr: {},
        keyboardExist: false
      };
    },

    computed: {},

    watch: {},

    created() {
      this.getPatientList('')
      this.getClinicMemberList()
    },

    mounted() {},

    methods: {
      cancel() {
        this.$emit('update:visible', false);
      },
      submitForm() {},
      openDialog() {
        this.$refs.inputDialog.open()
        this.getPatientList('')
      },
      closeDialog() {
        this.formData = {...initFormData,created_mid: getUid()}
        this.$refs.inputDialog.close()
      },

      getPatientList: debounce(function(keyword) {
        let params = {
          keyword,
          underway_mr: 1
        }
        patientService.getPatientList(params).then(res => {
          res.list.forEach(item => {
            item.sexText = item.sex === '1' ? '男' : '女';
            item.ageText = S.formatDuration(item.birthday);
          })

          this.patientList = res.list

        })
      }, 300),
      uniComboxInput(val) {
        this.getPatientList(val);

        // console.log("=>(createConsultDialog.vue:145) val", val);
        // this.debounce(this.debounce(() => {
        //   this.getPatientList();
        // }, 300))
        // this.debounce(() => {
        //   console.log("=>(createConsultDialog.vue:145) val", val);
        //     this.getPatientList(val);
        // }, 300);
        // this.getPatientList(val)
      },
      uniComboxSelect(key, item) {
        this.formData.mobile = item.mobile
        this.formData.name = item.name
        this.formData.sex = item.sexText
        this.formData.age = item.ageText
        this.formData.uid = item.uid
        this.formData.pt_id = item.id
        this.underway_mr = item.underway_mr
        if (item.underway_mr?.id) {
          this.$refs.underwayDialog.open()
        }
      },
      uniComboxClear() {
        this.formData.mobile = ''
      },
      uniKeyBoardChange(val) {
        this.keyboardExist = val.height > 0;
      },
      emptyCreate(val){
        this.closeDialog()
        this.$emit('showCreateDrawer', val)
      },
      submitDialog() {
        this.$refs.uniForm.validate().then(res => {
          this.createInquiryRecord()
        })
      },
      getClinicMemberList() {
        patientService.getClinicMemberList().then(res => {
          this.clinicStaffList = res.list
          this.formData.created_mid = getUid()
        })
      },
      createInquiryRecord() {
        this.submitLoading = true
        let params = {
          uid: this.formData.uid,
          pt_id: this.formData.pt_id,
          created_mid: this.formData.created_mid
        }
        patientService.createInquiryRecord(params).then(res => {
          uni.showToast({
            title: '建档成功'
          })
          this.$emit('success', res.mr_id, this.formData.pt_id)
          this.closeDialog()
          this.submitLoading = false
        }).catch((err) => {
          this.submitLoading = false
        }).finally(() => {
          this.submitLoading = false
        })
      },
      continueConsult() {
        this.$emit('success', this.underway_mr?.id, this.formData.pt_id)
        this.closeDialog()
      },
      clearUserInfo() {
        this.formData.mobile = ''
        this.formData.name = ''
        this.formData.sex = ''
        this.formData.age = ''
        this.formData.uid = ''
        this.formData.pt_id = ''
      },
    },

    destroyed() {},
  };
</script>

<style scoped lang="scss">
  ::v-deep .uni-dialog-title {
    padding: 24rpx 30rpx;
    border-bottom: 1px solid #EBEDF0;
    justify-content: flex-start;

    .uni-dialog-title-text {
      color: #333333;
      font-weight: 600;
      // font-size: 24rpx;
    }
  }

  ::v-deep .uni-dialog-content {
    display: block;
  }

  ::v-deep .uni-forms-item__content {
    width: 360rpx;
  }

  .custom-dialog {
    ::v-deep .uni-dialog-button-group {
      display: none;
    }
  }

  .underwayDialog {
    ::v-deep .uni-dialog-title {
      display: none;
    }

    ::v-deep .uni-dialog-button-text {
      font-size: 28rpx;
    }
  }


  .dialog-footer {
    display: flex;
    justify-content: flex-end;

    uni-button {
      margin: 0;
    }
  }

  .mr10 {
    margin-right: 10px !important;
  }
  .topDialog{
    position: fixed;
    top:0 ;
    left: 50%;
    transform: translateX(-50%);
  }

  ::v-deep .uni-select__selector-scroll {
    max-height: 175px;
  }
</style>
