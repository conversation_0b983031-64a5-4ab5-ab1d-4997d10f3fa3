<template>
<view>
  <cus-drawer v-model="drawerVisible" title="创建用户" @on-open="onOpen" @on-close="onClose" :loading="confirmLoading">
    <view class="form-content" slot="content">
      <uni-forms ref="uniForm" :model="formData" :rules="rules" :label-width="90" label-align="right">
        <uni-forms-item label="用户手机" required name="mobile">
          <uni-easyinput v-model="formData.mobile" placeholder="请输入手机号" maxlength="11" type="number" :disabled="isEchoUser" />
        </uni-forms-item>

          <uni-forms-item label="验证码" required v-if="!isEchoUser" name="auth_code" >
            <view class="verify-code-wrapper">
              <uni-easyinput v-model="formData.auth_code" placeholder="请输入验证码" maxlength="6" />
              <view class="counting-btn" @click="getVerifyCode" :class="{'disabled-counting-btn': counting}">{{ countText }}</view>
            </view>
            <!--          <view class="flex flex-end">-->
            <!--            <view style="color: #155BD4">没办法提供验证码？</view>-->
            <!--          </view>-->
          </uni-forms-item>


        <uni-forms-item label="用户姓名" required name="name">
          <uni-easyinput v-model="formData.name" placeholder="请输入用户姓名" maxlength="16" :disabled="isEchoUser" />
          <view class="show-word-limit">{{formData.name.length || 0}}/16</view>
        </uni-forms-item>

        <uni-forms-item label="用户性别" required name="sex">
          <uni-data-select
            v-model="formData.sex"
            :localdata="genderOptions"
            placeholder="请选择性别"
            :disabled="isEchoUser"
          />
        </uni-forms-item>

        <uni-forms-item label="出生日期" required name="birthday">
          <uni-datetime-picker
            v-model="formData.birthday"
            type="date"
            placeholder="请选择出生日期"
            @change="selectBirthday"
            :default-value="'1970-01-01'"
            :end="todayDate"
            :disabled="isEchoUser && isExistBirthday"
            :class="['common-input', !formData.birthday ? 'date-picker--placeholder' : '']"
          />
        </uni-forms-item>

        <uni-forms-item label="用户年龄">
          <uni-easyinput v-model="formData.age" placeholder="根据出生日期自动生成" class="common-input" disabled />
        </uni-forms-item>

        <uni-forms-item label="用户等级" key="566">
          <uni-data-select
            v-model="formData.offline_level"
            :localdata="levelOptions"
            placeholder="请选择用户等级"
          />
        </uni-forms-item>
        <uni-forms-item  v-if="isEchoUser" label="验证码" required name="auth_code">
          <view class="verify-code-wrapper">
            <uni-easyinput v-model="formData.auth_code" placeholder="请输入验证码" maxlength="6" />
            <view class="counting-btn" @click="getAuthCode" :class="{'disabled-counting-btn': counting}">{{ countText }}</view>
          </view>
        </uni-forms-item>

      </uni-forms>
    </view>
    <view slot="footer" class="footer" >
      <view class="flex">
        <button class="ui-btn-drawer-base ui-btn-drawer-default" type="default"  @click="onClose">取消</button>
        <button class="ui-btn-drawer-base ui-btn-drawer-primary ui-btn-distance-l" type="primary" :loading="confirmLoading" @click="submitForm">创建新用户</button>
      </view>
    </view>

  </cus-drawer>
  <uni-popup ref="existDialog" type="dialog">
    <uni-popup-dialog ref="inputClose" mode="input" title="当前手机号在用户中心已存在" confirm-text="账号同步到本店" cancel-text="我再想想"
                      @confirm="echoUserInfo" class="existDialog">
      <view style="font-size: 28rpx;text-align: center;margin: 20rpx 0;color: #999">如果一个手机号在用户中心已存在，则无法直接用该手机号创建新用户，但你可以通过同步账号的方式，在本店创建新账号。</view>
    </uni-popup-dialog>
  </uni-popup>
</view>
</template>

<script>
import cusDrawer from "@/components/cus-drawer/index.vue";
import patientService from "@/api/patient";
import S from "@/utils/utils";
import {debounce} from 'lodash'
import LoginService from "@/api/login";
import {getUid} from 'utils/runtime'
const initFormData = {
  mobile: '',
  auth_code: '',
  name: '',
  sex: '',
  birthday: '',
  age: '',
  offline_level: ''
};


export default {
  name: "createConsultDrawer",

  components: {cusDrawer},
  props: {
    value: {
      type: Boolean,
      default: () => false
    },
    echoValue: {
      type: String,
      default: '',
    }
  },

  data() {
    return {
      drawerVisible:false,
      confirmLoading:false,
      counting: false,
      countdown: 60,
      countText: '获取验证码',
      formData: {...initFormData},
      genderOptions: [
        { value: '1', text: '男' },
        { value: '2', text: '女' }
      ],
      levelOptions: [
        { value: 'A', text: 'A' },
        { value: 'B', text: 'B' },
        { value: 'C', text: 'C' },
        { value: 'D', text: 'D' },
        { value: 'E', text: 'E' },
        { value: 'F', text: 'F' }
      ],
      rules: {
        mobile: {
          rules: [
            { required: true, errorMessage: '请输入手机号' },
          ]
        },
        auth_code: {
          rules: [
            { required: true, errorMessage: '请输入验证码' },
          ]
        },
        name: {
          rules: [
            { required: true, errorMessage: '请输入用户姓名' }
          ]
        },
        sex: {
          rules: [
            { required: true, errorMessage: '请选择性别' }
          ]
        },
        birthday: {
          rules: [
            { required: true, errorMessage: '请选择出生日期' }
          ]
        }
      },
      patientList: [],
      todayDate: '',
      isUserExist: false, // 客户是否已存在
      syn_user_info: {},
      isEchoUser: false,
      isExistBirthday: false,
    };
  },
  computed: {},
  watch: {
    value(val) {
      this.drawerVisible = !!val;
    },
  },
  created() {

  },
  mounted() {
  },
  methods: {
    onOpen(){
      this.todayDate = S.dayjs().format('YYYY-MM-DD')
      if(this.echoValue){
        // 创建用户弹窗中没有填充输入的内容（新建时没有达到11位时需自动填充到姓名中，输入11位时自动填充至手机号输入框）
        const phoneRegex = /^1[3-9]\d{9}$/;
        if(phoneRegex.test(this.echoValue)){
          this.formData.mobile = this.echoValue
        }else{
          this.formData.name = this.echoValue
        }
      }
    },
    onClose(){
      this.formData = {...initFormData}
      this.isEchoUser = false
      this.$emit('input', false)
    },
    getVerifyCode() {
      if (!this.formData.mobile) {
        uni.showToast({
          title: '请先输入手机号',
          icon: 'none'
        });
        return;
      }

      // 手机号格式校验
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(this.formData.mobile)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        });
        return;
      }
      let params = {mobile: this.formData.mobile}

      patientService.getUserExistMobile(params).then(res => {
        if (res.is_exist === '1') {
          this.syn_user_info = res.info;
          this.$refs.existDialog.open()
        }else{
          this.getAuthCode()
        }
      })
    },
    submitForm(){
      this.$refs.uniForm.validate().then(res => {
        this.editPatientInfo()
      }).catch(err => {
      })
    },
    editPatientInfo(){
      this.confirmLoading = true
      let params = {...this.formData}
      patientService.editPatientInfo(params).then(res => {
        uni.showToast({
          icon: "none",
          title: '创建成功'
        })
        this.createInquiryRecord(res)
      }).catch(() => {
        this.confirmLoading = false
      })
    },
    createInquiryRecord(res) {
      let params = {
        uid: res.uid,
        pt_id: res.pt_id,
        created_mid: getUid()
      }
      patientService.createInquiryRecord(params).then(res => {
        this.onClose()
        this.$emit('successCreateRecord', res.mr_id, this.formData.pt_id)
        this.confirmLoading = false
      }).catch(() => {
        this.confirmLoading = false
      })
    },
    selectBirthday(val){
      this.formData.age = S.formatDuration(val);
    },
    echoUserInfo(){
      this.isEchoUser = true
      this.formData.auth_code = ''
      this.formData.name = this.syn_user_info.profile?.real_name
      this.formData.sex = this.syn_user_info.profile?.gender
      this.formData.birthday = this.syn_user_info.profile?.birthday
      this.isExistBirthday = this.syn_user_info.profile?.birthday !== ''
      this.formData.age = S.formatDuration(this.syn_user_info.profile?.birthday)
    },
    getAuthCode(){
      if(this.counting){
        return;
      }
      LoginService.getAuthCode({ mobile: this.formData.mobile }).then(res => {
        uni.showToast({
          title: '验证码已发送',
          icon: 'none',
        });
        this.counting = true;
        this.countdown = 60;
        this.countText = `${this.countdown}s后重新获取`;

        const timer = setInterval(() => {
          if (this.countdown > 0) {
            this.countdown--;
            this.countText = `${this.countdown}s后重新获取`;
          } else {
            clearInterval(timer);
            this.counting = false;
            this.countText = '获取验证码';
          }
        }, 1000);
      });
    },

  },


};
</script>
<style scoped lang="scss">
.verify-code-wrapper {
  position:relative;
  display: flex;
  align-items: center;
  margin-bottom: 5px;
  .counting-btn{
    position: absolute;
    top: 50%;
    right: 40px;
    transform: translateY(-50%);
    color: #155BD4;
    cursor: pointer;
  }
  .disabled-counting-btn{
    color: #B8CEF2;
  }
}
.mr10{
  margin-right: 10px;
}
.existDialog{
  width: 380px;
  ::v-deep .uni-popup__info{
    font-weight: 600;
    color: #000000;
  }
  ::v-deep .uni-dialog-button-text {
    font-size: 28rpx;
  }

}
::v-deep .common-input {
  .uni-date__x-input, .uni-input-placeholder, input {
    font-size: 26rpx;
  }
}
::v-deep .date-picker--placeholder {
  .uni-date__x-input {
    color: #bbb;

  }
}
</style>
