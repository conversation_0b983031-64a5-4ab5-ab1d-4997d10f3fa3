<template>
  <view>
    <uni-drawer ref="showRight" mode="left" :mask-click="false" class="left-drawer" :width="330" :maskClick="true">
      <view class="top-box">
        <view class="search-box">
          <uni-easyinput prefixIcon="search" v-model="historyConsult" placeholder="搜索历史咨询" class="searchInput"
            @input="changeKeyword">
          </uni-easyinput>
          <button type="primary" icon="el-icon-plus" size="small" @click="createConsult">建单</button>
        </view>
        <view class="search-tip" v-show="historyConsult">共<text style="color: #155BD4">{{consultList.length}}</text>
          条搜索结果</view>
      </view>


      <view>
        <view class="consult-box">
          <view class="consult-header" v-show="!historyConsult">
            <view class="header-title">咨询</view>
            <view class="consult-select">
              <uni-datetime-picker type="date" size="small" v-model="consultDate" :end="todayDate" @change="changeDate">
                <view class="consult-select">
                  <view style="margin-right: 6px">{{dateText}}</view>
                  <image src="https://static.rsjxx.com/image/2025/0113/110719_5199.png" class="down-arrow" />
                </view>
              </uni-datetime-picker>
            </view>
          </view>
          <view class="consult-content" v-if="consultList.length> 0">
            <scroll-view style="height: 1071rpx" scroll-y="true">
              <view
                class="consult-item"
                v-for="(item, index) in consultList"
                :class="{'selected': item.id === mr_id}"
                :key="index"
                @click="selectConsult(item)"
              >
                <view class="consult-left">
                  <image v-if="item.avatar" :src="item.avatar" class="patient-avatar" />
                  <view v-else class="default-patient-avatar">{{item.patient_name[0]}}</view>
                  <view class="patient-name">{{item.patient_name}}</view>
                </view>
                <view class="consult-right">
                  <view v-if="item.is_draft === '1'" style="color: #E63E3E">草稿</view>
                  <view v-else>{{item.create_time_text}}</view>
                </view>
              </view>
            </scroll-view>
          </view>
          <view class="empty-box" v-show="consultList.length === 0">
            <image src="https://static.rsjxx.com/image/2025/0121/104146_42458.png" class="empty-img" />
            <view>暂无咨询单</view>
          </view>
        </view>


      </view>
    </uni-drawer>
    <create-consult-dialog ref="createDialog" @success="createSuccess" @showCreateDrawer="showCreateDrawer"></create-consult-dialog>
    <create-user-drawer v-model="createVisible" :echoValue="echoValue" @successCreateRecord="createSuccess"></create-user-drawer>
  </view>
</template>

<script>
  import createConsultDialog from "./components/createConsultDialog.vue"
  import createUserDrawer from "./components/createUserDrawer.vue";
  import S from 'utils/utils'
  import patientService from "@/api/patient";

  export default {
    name: "index",

    components: {
      createConsultDialog,
      createUserDrawer
    },
    props: {},
    data() {

      return {
        historyConsult: '',
        consultDate: '',
        createVisible: false,
        todayDate: '',
        consultList: [],
        mr_id: '',
        echoValue: '',
      };
    },
    computed: {
      dateText() {
        return this.consultDate === this.todayDate ? '今日' : this.consultDate
      },
    },
    created() {
      this.todayDate = S.dayjs().format('YYYY-MM-DD')
      this.consultDate = S.dayjs().format('YYYY-MM-DD')

      // 获取页面参数
      const params = this.$store.getters['global/getQuery'] || {}
      this.mr_id = params.mr_id
    },
    mounted() {},
    methods: {
      showDrawer(openDialog = false) {
        this.$refs.showRight.open();
        this.getInquiryRecordList()
        if (openDialog) {
          this.$refs.createDialog.openDialog()
        }
      },
      closeDrawer() {
        this.$refs.showRight.close();
        this.historyConsult = ''
        // this.consultDate = this.todayDate
      },
      createConsult() {
        this.$refs.createDialog.openDialog()
        // this.$refs.showRight.close();
        // this.createVisible = true
      },
      createSuccess(mr_id, pt_id) {
        this.closeDrawer()
        this.$store.commit('global/SET_QUERY', {
          mr_id,
          pt_id,
          menuId: '0',
        })
      },
      changeKeyword(val) {
        this.historyConsult = val
        this.getInquiryRecordList()
      },
      changeDate() {
        this.getInquiryRecordList()
      },
      getInquiryRecordList() {
        let params = {
          keyword: this.historyConsult,
          create_time: this.historyConsult ? '' : this.consultDate
        }
        patientService.getInquiryRecordList(params).then(res => {
          res.list.forEach(item => {
            if (this.consultDate === this.todayDate) {
              item.create_time_text = `今天 ${S.dayjs(item.create_time*1000).format('HH:mm')}`
            }
            item.create_time_text = S.dayjs(item.create_time * 1000).format('YYYY-MM-DD HH:mm')
          })
          this.consultList = res.list
        })
      },
      selectConsult(item) {
        this.closeDrawer()
        setTimeout(() => {
          this.$store.commit('global/SET_QUERY', {
            mr_id: item.id,
            pt_id: item.pt_id,
            menuId: '0',
          })
        }, 50)
      },

      showCreateDrawer(val){
        this.$refs.showRight.close();
        this.createVisible = true
        this.echoValue = val
      },
    },
  };
</script>
<style scoped lang="scss">
  .left-drawer {
    left: 60px;
  }

  .top-box {
    border-bottom: 1px solid #ebedf0;
    padding: 12px 16px;

    .search-box {
      display: flex;
      align-items: center;
    }

    .search-tip {
      margin-top: 18rpx;
      font-weight: 400;
      font-size: 18rpx;
      color: #999999;
    }
  }


  .searchInput {
    margin-right: 8px;
  }

  .consult-box {
    .consult-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 18px 16px;
      box-shadow: 0px 3px 6px 0px rgba(20, 21, 22, 0.03);
      margin-bottom: 16rpx;

      .header-title {
        font-weight: 600;
        font-size: 32rpx;
        color: #262626;
      }

      .consult-select {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        cursor: pointer;
        min-width: 50px;
        font-weight: 400;
        font-size: 24rpx;
        color: #666666;

        .down-arrow {
          width: 10px;
          height: 10px;
        }

        .customDatePicker {
          width: 100%;
          position: absolute;
          top: 0;
          right: 0;

          ::v-deep .uni-date-editor {
            opacity: 0;
          }
        }
      }
    }

    .consult-content {
      height: 100vh;

      .consult-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        // margin-bottom: 16px;
        padding: 16rpx 32rpx;

        .consult-left {
          display: flex;
          align-items: center;

          .patient-avatar {
            width: 80rpx;
            min-width: 80rpx;
            height: 80rpx;
            border-radius: 50%;
            margin-right: 12px;
          }

          .default-patient-avatar {
            width: 80rpx;
            min-width: 80rpx;
            height: 80rpx;
            border-radius: 50%;
            background-color: #155BD4;
            margin-right: 12px;
            text-align: center;
            line-height: 78rpx;
            color: #ffffff;
          }

          .patient-name {
            font-weight: 400;
            font-size: 14px;
            color: #333333;
          }
        }

        .consult-right {
          font-weight: 400;
          font-size: 12px;
          color: #999999;
        }

        &:hover {
          background: rgba(21, 91, 212, 0.04);
        }
      }

      .selected {
        background: rgba(21, 91, 212, 0.04);
      }
    }
  }

  .empty-box {
    display: flex;
    height: 1017rpx;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-weight: 400;
    font-size: 21rpx;
    color: #AAAAAA;

    .empty-img {
      width: 332rpx;
      height: 255rpx;
    }
  }
</style>
