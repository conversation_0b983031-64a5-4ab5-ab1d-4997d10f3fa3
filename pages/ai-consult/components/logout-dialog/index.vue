<template>
  <div style="position: relative; z-index: 10000">
    <uni-popup ref="popup" background-color="#fff" @change="change" :mask-click="false">
      <view class="content-box">
        <image src="/static/image/logout/close.png" class="close-icon" @click="close"></image>
        <view class="main">
          <view class="user-avatar" :style="{backgroundColor: getColorForName}">{{ userInfo.user_name.slice(-2) }}</view>
          <view class="user-name">{{ userInfo.user_name }}</view>
          <view class="user-item">
            <view class="user-label">门店</view>
            <view class="user-value">{{ userInfo.clinic_name }}</view>
          </view>
          <view class="user-item">
            <view class="user-label">手机</view>
            <view class="user-value">{{ userInfo.mobile }}</view>
          </view>
          <view class="user-item">
            <view class="user-label">身份</view>
            <view class="user-value">{{ userInfo.role_name }}</view>
          </view>
          <view class="logout-btn" @click="doubleCheck">退出</view>
        </view>
      </view>
    </uni-popup>

    <uni-popup ref="doubleCheckDialog" type="dialog">
      <uni-popup-dialog ref="inputClose" mode="input" confirm-text="确认" cancel-text="取消"
                        @confirm="logout" class="doubleCheckDialog">
        <view style="font-size: 28rpx;text-align: center;margin: 20rpx 0;color: #333">是否确认退出？</view>
      </uni-popup-dialog>
    </uni-popup>
  </div>
</template>

<script>
import { getUserInfo, removeLoginInfo } from '@/utils/runtime'
import LoginService from '@/api/login';
export default {
  name: "index",

  components: {},
  props: {},
  data() {
    return {
      userInfo: getUserInfo()
    };
  },
  computed: {
    getColorForName() {
      const name = this.userInfo.user_name.trim();
      if (!name) return 'hsl(0, 0%, 50%)';

      // 简单哈希算法
      let hash = 0;
      for (let i = 0; i < name.length; i++) {
        hash = name.charCodeAt(i) + ((hash << 5) - hash);
      }

      // 使用哈希值生成HSL颜色
      const hue = Math.abs(hash) % 360;
      return `hsl(${hue}, 80%, 60%)`;
    },
  },
  watch: {},
  created() {
  },
  mounted() {
  },
  methods: {
    open() {
      this.$refs.popup.open('center')
    },
    close() {
      this.$refs.popup.close()
    },
    change(){},
    doubleCheck(){
      this.$refs.doubleCheckDialog.open()
    },
    logout() {
      LoginService.logout().then((result) => {
      }).finally(r => {
        removeLoginInfo()
      });
    },
  },


};
</script>
<style scoped lang="scss">
.content-box {
  position: relative;
  width: 640rpx;
  height: 866rpx;
  background: #FFFFFF;
  border-radius: 8rpx;
  padding: 96rpx 80rpx 0;
  text-align: center;
  color: #000000;

  .close-icon {
    position: absolute;
    top: 32rpx;
    right: 32rpx;
    width: 40rpx;
    height: 40rpx;
  }

  .main {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .user-avatar {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 144rpx;
    height: 144rpx;
    background-color: #155BD4;
    text-align: center;
    color: #ffffff;
    margin-bottom: 16rpx;
    border-radius: 50%;
    font-size: 36rpx;
    //line-height: 140rpx;
  }

  .user-name {
    font-weight: 500;
    font-size: 32rpx;
    color: #262626;
    margin-bottom: 64rpx;
  }

  .user-item {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2rpx solid #EBEDF0;
    padding-bottom: 20rpx;
    margin-bottom: 40rpx;

    .user-label {
      min-width: fit-content;
      font-weight: 400;
      font-size: 28rpx;
      color: #999999;
      margin-right: 20px;
    }

    .user-value {
      font-weight: 400;
      font-size: 28rpx;
      color: #333333;
      text-align: right;
    }
  }

  .logout-btn {
    width: 320rpx;
    height: 72rpx;
    background: #F6F6F6;
    border-radius: 8rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #155BD4;
    line-height: 72rpx;
    margin-top: 44rpx;
  }
}

::v-deep .uni-popup__wrapper {
  border-radius: 8px;
}
.doubleCheckDialog {
  ::v-deep .uni-dialog-title {
    display: none;
  }

  ::v-deep .uni-dialog-button-text {
    font-size: 28rpx;
  }
}
</style>
