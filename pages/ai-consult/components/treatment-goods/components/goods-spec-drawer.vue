<template>
  <uni-popup ref="popup" type="right" :mask-click="true" @change="onVisibleChange">
    <view class="spec-wrapper" v-if="goodsInfo">
      <view class="spec-header">
        <view class="product-info">
          <image class="product-img" v-if="goodsImage" :src="goodsImage" mode="aspectFill"></image>
          <view class="product-detail">
            <view class="product-name">{{ goodsName }}</view>
            <view class="product-price" v-if="selectedSpec">¥{{ selectedSpec.price }}</view>
          </view>
        </view>
        <uni-icons type="closeempty" size="16" color="#AAAAAA" @click="close"></uni-icons>
      </view>

      <view class="spec-content">
        <view class="spec-title">规格</view>
        <view class="spec-list">
          <view
            v-for="(item, index) in specList"
            :key="index"
            :class="['spec-item', { active: selectedSpecId === item.id }]"
            @click="handleSpecClick(item)"
          >
            {{ item.spec }}
          </view>
        </view>
      </view>

      <view class="spec-footer">
        <view class="quantity-box">
          <text class="quantity-label">数量</text>
          <u-number-box
            v-model="quantity"
            :min="1"
            :step="1"
            integer
            inputWidth="100"
            buttonSize="60"
          ></u-number-box>
        </view>
        <button class="ui-btn-drawer-base ui-btn-drawer-primary" type="primary" @click="confirmSelect">确定</button>
      </view>
    </view>
  </uni-popup>
</template>

<script>
export default {
  name: "GoodsSpecDrawer",
  props: {
    value: {
      type: Boolean,
      default: false
    },
    goodsInfo: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      selectedSpecId: '',
      quantity: 1,
    }
  },
  computed: {
    goodsImage() {
      return this.goodsInfo && this.goodsInfo.main_img ? this.goodsInfo.main_img + '-B.200' : '';
    },
    goodsName() {
      return this.goodsInfo ? this.goodsInfo.name || '' : '';
    },
    specList() {
      return this.goodsInfo?.attrs ? Object.values(this.goodsInfo.attrs) : [];
    },
    selectedSpec() {
      return this.goodsInfo?.attrs?.[this.selectedSpecId] || null;
    }
  },
  watch: {
    value: {
      handler(val) {
        if (val && this.goodsInfo) {
          // 如果已经选择过规格，显示已选规格
          if (this.goodsInfo.attr_id) {
            this.selectedSpecId = this.goodsInfo.attr_id;
            this.quantity = this.goodsInfo.quantity || 1;
          }
          this.$nextTick(() => {
            this.open();
          });
        } else {
          this.$nextTick(() => {
            this.close();
          });
        }
      },
      immediate: true
    },
    'goodsInfo.attrs': {
      handler(val) {
        if (val && !this.selectedSpecId) {
          // 只有在没有选择规格时才默认选中第一个
          const firstSpec = Object.values(val)[0];
          if (firstSpec) {
            this.selectedSpecId = firstSpec.id;
          }
        }
      },
      immediate: true
    }
  },
  methods: {
    open() {
      if (this.$refs.popup && this.goodsInfo) {
        this.$refs.popup.open();
      }
    },
    close() {
      if (this.$refs.popup) {
        this.$refs.popup.close();
      }
    },
    onVisibleChange(e) {
      this.$emit('input', e.show);
      if (!e.show) {
        this.quantity = 1;
        this.selectedSpecId = '';
      }
    },
    handleSpecClick(spec) {
      this.selectedSpecId = spec.id;
    },
    confirmSelect() {
      if (!this.selectedSpecId) {
        uni.showToast({
          title: '请选择规格',
          icon: 'none'
        });
        return;
      }
      this.$emit('confirm', {
        specId: this.selectedSpecId,
        quantity: this.quantity
      });
      this.close();
    }
  }
}
</script>

<style lang="scss" scoped>
.spec-wrapper {
  width: 740rpx;
  height: 100%;
  background: #FFFFFF;
  display: flex;
  flex-direction: column;

  .spec-header {
    padding: 40rpx;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    border-bottom: 2rpx solid #EBEDF0;

    .product-info {
      display: flex;
      align-items: flex-start;

      .product-img {
        width: 160rpx;
        height: 160rpx;
        border-radius: 8rpx;
        margin-right: 24rpx;
      }

      .product-detail {
        .product-name {
          font-size: 32rpx;
          color: #333333;
          line-height: 48rpx;
          font-weight: 500;
          margin-bottom: 16rpx;
        }

        .product-price {
          font-size: 36rpx;
          color: #E63E3E;
          font-weight: 500;
        }
      }
    }
  }

  .spec-content {
    flex: 1;
    padding: 40rpx;

    .spec-title {
      font-size: 28rpx;
      color: #999999;
      line-height: 40rpx;
      margin-bottom: 24rpx;
    }

    .spec-list {
      display: flex;
      flex-wrap: wrap;
      gap: 24rpx;

      .spec-item {
        padding: 12rpx 32rpx;
        background: #F5F5F5;
        border-radius: 8rpx;
        font-size: 28rpx;
        color: #333333;
        line-height: 40rpx;
        cursor: pointer;

        &.active {
          background: $color-primary;
          color: #FFFFFF;
        }
      }
    }
  }

  .spec-footer {
    padding: 40rpx;
    border-top: 2rpx solid #EBEDF0;

    .quantity-box {
      display: flex;
      align-items: center;
      margin-bottom: 40rpx;

      .quantity-label {
        font-size: 28rpx;
        color: #999999;
        margin-right: 24rpx;
      }
    }
  }
}
</style>
