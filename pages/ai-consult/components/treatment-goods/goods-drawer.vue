<template>
  <uni-drawer ref="inputDrawer" :maskClick="true" mode="right" :duration="300" :width="880" @change="onVisibleChange">
    <view class="popup-wrapper" @click="handlePageClick">
      <view class="pop-header">
        <view class="pop-title">添加商品</view>
        <uni-icons type="closeempty" size="16" color="#AAAAAA" @click="close"></uni-icons>
      </view>
      <view class="pop-content">
        <view class="goods-box">
          <view class="goods-menus">
            <view :class="['goods-menu', item.id === queryFormData.goods_type ? 'active' : '']"
              v-for="(item, index) in goodsMenus" :key="item.id" @click="handleMenuClick(item)">
              <text class="goods-menu-title">{{ item.name }}</text>
            </view>
          </view>
          <view class="goods-table">
            <view class="goods-search">
              <view class="goods-search-l">
                <view class="goods-title">商品列表</view>
                <view class="refresh-btn link-primary" @click="getGoodsList">
                  <uni-icons type="refreshempty" size="12" color="#115bd4"></uni-icons>
                  刷新
                </view>
              </view>
              <view class="goods-search-r">
                <uni-data-select size="small" v-model="queryFormData.source" placeholder="来源" @change="getGoodsList"
                  style="width: 100px;" :localdata="sourceList" class="customSelect"></uni-data-select>

                <uni-easyinput v-model="queryFormData.name" placeholder="商品名称" maxlength="20" suffixIcon="search"
                  clearable primaryColor="#115bd4" class="uni-search" confirmType="search" trim
                  @confirm="getGoodsList"></uni-easyinput>
                <button @click="getGoodsList" type="primary">筛选</button>
                <button @click="resetSearch" type="default">重置</button>
              </view>
            </view>
            <view class="goods-table-body">
              <scroll-view scroll-y="true" style="height: 100%">
                <uni-table ref="table" style="width: 100%;min-width: 100%;" :loading="tableLoading" emptyText="暂无更多数据">
                  <uni-tr class="static-table-header">
                    <uni-th align="left" width="200">产品</uni-th>
                    <uni-th align="center">类型</uni-th>
                    <uni-th align="center">来源</uni-th>
                    <uni-th align="center">库存</uni-th>
                    <uni-th align="center">单价</uni-th>
                    <uni-th align="center" width="100">选择</uni-th>
                  </uni-tr>
                  <uni-tr v-for="(row, index) in goodsList" :key="index" @click.native="handleSelect(row, index)">
                    <uni-td align="left" width="200">
                      <div class="product-box">
                        <image class="main-img" @click="previewImage(row.main_img)" mode="aspectFill"
                          v-if="row.main_img" :src="row.main_img + '-B.200'"></image>
                        <view class="product-name">{{ row.name || '-' }}</view>
                      </div>
                    </uni-td>
                    <uni-td align="center">
                      <view class="name"> {{ row.goods_type_text || '-' }}</view>
                    </uni-td>
                    <uni-td align="center">{{ row.source_platform_text || '-' }}</uni-td>
                    <uni-td align="center">
                      {{ row.stock || '-' }}
                    </uni-td>
                    <uni-td align="center">
                      <view>{{ formatPrice(row.price) }}</view>
                    </uni-td>
                    <uni-td align="center" width="100">
                      <view>
                        <view class="uni-checkbox uni-checkbox-checked" v-if="isGoodsChecked(row.id)">
                          <uni-icons type="checkmarkempty" color="#115bd4" size="12"></uni-icons>
                          已添加
                        </view>
                        <view class="uni-checkbox" v-else>
                          <uni-icons type="plusempty" color="#999999" size="12"></uni-icons>
                          添加
                        </view>
                      </view>

                    </uni-td>
                  </uni-tr>

                </uni-table>
              </scroll-view>

              <view class="pagination-box">
                <view class="select-info">
                  已添加{{ getCheckedGoods.length }}个产品
                </view>
                <uni-pagination class="flex-1" :current="queryFormData.page" :total="total" title="标题文字"
                  :show-icon="true" @change="onPageChange" />
              </view>

            </view>

          </view>
        </view>

      </view>
      <view class="pop-footer">
        <view class="cart-btn" @click.stop="toggleCartPopup">
          <uni-badge size="small" :text="getCheckedGoods.length" absolute="rightTop" type="error">
            <image class="cart-icon" src="@/static/image/main/cartoon.png" mode="aspectFill"></image>
          </uni-badge>
        </view>
        <uni-transition :modeClass="['fade']" :show="showCartPopup">
          <view class="cart-popup" @click.stop>
            <view class="cart-header">
              <view class="cart-title">已选商品 ({{ getCheckedGoods.length }})</view>
              <view class="clear-btn" @click="closeCartPopup">
                收起
              </view>
            </view>
            <scroll-view scroll-y="true" class="cart-list" style="height: 680rpx;">
              <view class="cart-list-content" v-if="getCheckedGoods.length">
                <view class="cart-item" v-for="(item,idx) in getCheckedGoods" :key="item.id">
                  <image class="item-img" v-if="item.main_img" :src="item.main_img + '-B.200'" mode="aspectFill">
                  </image>
                  <view class="item-info">
                    <view class="flex-between item-name-box">
                      <view class="item-name">{{ item.name }}</view>
                      <view class="remove-btn" @click.stop="handleSelect(item,idx)">
                        <uni-icons type="closeempty" size="12" color="currentColor"></uni-icons>
                      </view>
                    </view>
                    <view class="flex-between">
                      <view class="item-price">{{ formatPrice(item.price) }}</view>
                      <view class="item-stock">库存：{{ item.stock }}</view>
                    </view>
                  </view>
                </view>
              </view>
              <view class="cart-empty" v-else>
                暂无已选商品
              </view>
            </scroll-view>

          </view>
        </uni-transition>
        <view class="pop-footer-btn">
          <button class="ui-btn-drawer-base ui-btn-drawer-default" @click="close">取消</button>
          <button class="ui-btn-drawer-base ui-btn-drawer-primary ui-btn-distance-l" type="primary" @click="handleConfirm">确定</button>
        </view>
      </view>
    </view>
  </uni-drawer>
</template>
<script>
import ConsultationService from "@/api/consultation";
import formatMixin from '@/mixins/format'
import cloneDeep from 'lodash/cloneDeep'
const init_queryFormData = {
  page: 1,
  pageSize: 10,
  goods_type: '10',
  source: '',
  name: '',
  status: 200,
  check_stock: 1,
  shelf_scope: '1,9',
  filter_pms_self_goods: '1',
  xn_scope: '', //实物商品上架范围s
}
export default {
  name: "GoodsDrawer",
  components: {},
  mixins: [formatMixin],
  model: {
    prop: "visible",
    event: "changeVisible"
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectedGoods: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    getCheckedGoods() {
      return this.selectGoods
    },
    isGoodsChecked(id){
      return id => {
        const existIndex = this.selectGoods.findIndex(item=>item.id === id)
        return existIndex !== -1
      }
    }
  },
  data() {
    return {
      activeIndex: 0,
      goodsList: [],
      showCartPopup: false,
      goodsMenus: [
        { id: '10', name: '实物' },
        { id: '15', name: '虚拟' },
        { id: '20', name: '套餐' },
        { id: '25', name: '通兑券' },
      ],
      queryFormData: {
        ...init_queryFormData
      },
      total: 0,
      tableLoading: false,
      sourceList: [],
      selectGoods: [],
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.open()
      } else {
        this.close()
      }
    },
  },
  mounted() {
    this.getGoodsOptions()
  },
  beforeDestroy() {
  },
  methods: {
    resetSearch() {
      this.resetFormData()
      this.getGoodsList()
    },
    handleSelect(row, index) {
      const existIndex = this.selectGoods.findIndex(item=>item.id === row.id)
      if(existIndex !== -1){
        this.selectGoods.splice(existIndex,1)
      }else {
        this.selectGoods.push(row)
      }

      // if (this.selectGoods[row.id]) {
      //   this.$delete(this.selectGoods, row.id)
      //   this.goodsList[index].checked = false
      // } else {
      //   this.$set(this.selectGoods, row.id, row);
      //   this.goodsList[index].checked = true
      // }
    },
    previewImage(imgUrl) {
      uni.previewImage({
        urls: [imgUrl]
      })
    },
    handleMenuClick(item) {
      this.queryFormData.goods_type = item.id
      this.getGoodsList()
    },
    close() {
      this.resetFormData()
      this.selectGoods = []
      this.showCartPopup = false
      this.$refs.inputDrawer.close()
    },
    open() {
      this.$refs.inputDrawer.open()
      this.getGoodsList()
    },
    resetFormData() {
      this.queryFormData = {
        ...init_queryFormData
      }
    },
    onVisibleChange(v) {
      if (!v) {
        this.inputValue = '';
        this.showCartPopup = false;
        this.$emit("changeVisible", false);
      }else {
        if(this.selectedGoods.length){
          this.selectGoods = cloneDeep(this.selectedGoods)
        }
      }
    },
    getGoodsList() {
      this.tableLoading = true;
      ConsultationService.getGoodsList(this.queryFormData).then(res => {
        this.goodsList = this.handleGoodsList(res.goods_items);
        this.total = res.total;
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    handleGoodsList(list) {
      return list
    },
    onPageChange({ current }) {
      this.queryFormData.page = current;
      this.getGoodsList()
    },
    getGoodsOptions() {
      ConsultationService.getGoodsOptions().then(res => {
        const sourcePlatformDesc = res.sourcePlatformDesc
        let sourceList = []
        for (const resKey in sourcePlatformDesc) {
          sourceList.push({
            value: resKey,
            text: sourcePlatformDesc[resKey].desc
          })
        }
        this.sourceList = sourceList
      })
    },
    handleConfirm() {
      if (this.getCheckedGoods.length) {
        this.$emit('selectGoodsHandler', this.getCheckedGoods,2333);
        this.close();
      } else {
        uni.showToast({
          title: '请选择商品',
          icon: 'none'
        })
      }
    },
    toggleCartPopup() {
      this.showCartPopup = !this.showCartPopup
    },
    closeCartPopup() {
      this.showCartPopup = false
    },
    handlePageClick() {
      this.closeCartPopup()
    }
  },
}
</script>
<style scoped lang="scss">
.popup-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .pop-header {
    @include flex($justify: space-between);
    padding: 36rpx 40rpx;
    box-shadow: $ui-draw-shadow-base;
    height: 120rpx;
    flex: none;

    .pop-title {
      font-weight: 600;
      font-size: 32rpx;
      color: #333333;
      line-height: 48rpx;
    }
  }

  .pop-content {
    flex: 1;
    min-height: 0;
    overflow: hidden;

    .goods-menus {
      background: #FAFAFB;
      width: 192rpx;
      flex: none;
      overflow-y: auto;

      .goods-menu {
        height: 88rpx;
        text-align: center;
        @include flex($align: center, $justify: center);

        .goods-menu-title {
          font-weight: 500;
          font-size: 28rpx;
        }

        &.active {
          background: #FFFFFF;
          color: $color-primary;
          position: relative;

          &::before {
            content: "";
            position: absolute;
            top: 24rpx;
            left: 0;
            width: 6rpx;
            height: 40rpx;
            background: $color-primary;
          }
        }
      }
    }

    .goods-box {
      display: flex;
      height: 100%;
      margin-top: 2rpx;

      .goods-table {
        flex: 1;
        padding: 40rpx;

        .goods-search {
          display: flex;
          align-items: center;
          margin-bottom: 24rpx;
          justify-content: space-between;
          .goods-search-l {
            display: flex;
            align-items: center;
            .goods-title {
              font-weight: 500;
              font-size: 32rpx;
              color: #333333;
              line-height: 44rpx;
              margin-right: 24rpx;
            }

            .refresh-btn {
              display: flex;
              align-items: center;
              font-size: 24rpx;
              line-height: 32rpx;
            }
          }

          .goods-search-r {
            display: flex;
            align-items: center;
            gap: 24rpx;
            min-width: 50%;
            padding-right: 240rpx;
            .uni-search {
              width: 340rpx!important;
              background: #FFFFFF;
              border-radius: 18rpx;
              display: flex;
              align-items: center;
            }
          }
        }
      }
    }
  }

  .pop-footer {
    flex: none;
    padding: 40rpx;
    border-top: 1px solid rgba(235, 237, 240, 0.8);
    display: flex;
    padding-bottom: 40rpx + constant(safe-area-inset-bottom);
    padding-bottom: 40rpx +env(safe-area-inset-bottom);
    justify-content: space-between;
    align-items: center;
    position: relative;

    .cart-btn {
      position: relative;
      padding: 16rpx;
      cursor: pointer;

      .cart-icon {
        width: 72rpx;
        height: 72rpx;
      }
    }

    .cart-popup {
      position: absolute;
      bottom: 100%;
      left: 40rpx;
      width: 720rpx;
      background: #FFFFFF;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
      border-radius: 8rpx;
      z-index: 100;
      margin-bottom: 8rpx;
      display: flex;
      flex-direction: column;

      .cart-header {
        padding: 24rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex: none;

        .cart-title {
          font-weight: 500;
          font-size: 28rpx;
          color: #333333;
          line-height: 33rpx;
        }

        .clear-btn {
          font-size: 24rpx;
          color: #999;
          cursor: pointer;

          &:hover {
            color: $color-primary;
          }
        }
      }

      .cart-list {
        height: 680rpx;
        min-height: 680rpx;
      }

      .cart-list-content {
        padding: 0 24rpx 24rpx;

        .cart-item {
          padding: 12rpx;
          display: flex;
          align-items: center;
          background: #F9F9FA;
          border-radius: 6rpx;
          margin-bottom: 12rpx;

          &:last-child {
            margin-bottom: 0;
          }

          .item-img {
            width: 84rpx;
            height: 84rpx;
            border-radius: 8rpx;
            margin-right: 12rpx;
          }

          .item-info {
            flex: 1;
            min-width: 0;

            .flex-between {
              @include flex($justify: space-between);
            }

            .item-name-box {
              margin-bottom: 18rpx;
            }

            .item-name {
              font-size: 28rpx;
              line-height: 30rpx;
              color: #333;
              @include text-ellipsis();
            }

            .item-price {
              color: #FF4D4F;
              font-size: 24rpx;
            }

            .item-stock {
              font-size: 24rpx;
              color: #999;
            }
          }

          .remove-btn {
            cursor: pointer;
            color: #999;

            &:hover {
              color: $color-primary;
            }
          }
        }
      }

      .cart-empty {
        padding: 48rpx 24rpx;
        text-align: center;
        color: #999;
        font-size: 26rpx;
      }
    }

    .pop-footer-btn {
      height: 36px;
      line-height: 36px;
      display: flex;
      align-items: center;
    }
  }
}

.goods-table-body {
  height: calc(100% - 200rpx);

  .static-table-header {
    position: sticky;
    top: 0rpx;
    background: #fff;
    z-index: 10;
  }

  ::v-deep.uni-table-scroll {
    overflow-x: unset;
  }

  ::v-deep .uni-table {
    min-width: unset !important;
  }

  .pagination-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 40rpx;
    padding-bottom: 40rpx;

    .select-info {
      font-size: 24rpx;
      color: #999999;
    }

    ::v-deep .uni-pagination__num-tag {
      width: 60rpx;
    }
  }

  ::v-deep .uni-pagination {
    justify-content: flex-end;
    width: 100%;
    padding-right: 30rpx;
  }

  .product-box {
    @include flex();

    .main-img {
      display: block;
      width: 80rpx;
      height: 80rpx;
      min-width: 80rpx;
      border-radius: 8rpx;
      border: 1rpx solid #ebedf0;
    }

    .product-name {
      font-size: 28rpx;
      color: #333333;
      line-height: 44rpx;
      margin-left: 16rpx;
    }
  }

  .uni-checkbox {
    padding: 8rpx 20rpx;
    border-radius: 28rpx;
    border: 2rpx solid #DCDFE6;
    font-size: 24rpx;
    color: #999999;
    line-height: 32rpx;
    display: inline-flex;
    align-items: center;
  }

  .uni-checkbox-checked {
    border-color: $color-primary;
    color: $color-primary;
  }
}
.customSelect{
  ::v-deep .uni-select__selector{
    z-index: 11;
  }
}
</style>
