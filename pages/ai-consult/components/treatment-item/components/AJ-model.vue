<!--
 * @description 艾灸穴位选择抽屉组件
 * <AUTHOR>
 * @createDate 2025-02-11
 *
 * 功能说明：
 * 1. 展示艾灸治疗的详细信息和穴位选择界面
 * 2. 支持经络和穴位的选择与管理
 * 3. 提供穴位单/双侧切换功能
 * 4. 实时展示已选穴位列表
 *
 * 组件结构：
 * - 抽屉布局（右侧弹出）
 *   - 顶部标题栏
 *   - 主体内容区
 *     - 左侧经络列表（可滚动）
 *     - 右侧穴位选择区（网格布局）
 *   - 底部已选穴位展示
 *   - 底部操作按钮（取消/确定）
 *
 * 主要功能：
 * 1. 经络选择：点击左侧经络加载对应穴位列表
 * 2. 穴位选择：点击穴位进行选择/取消选择
 * 3. 单双侧切换：点击穴位右侧区域切换单/双侧
 * 4. 实时预览：底部展示已选穴位列表
 * 5. 数据同步：确认时将选择结果同步给父组件
 *
 * Props:
 * - data: Object - 艾灸治疗数据对象
 *
 * Events:
 * - confirm: 确认选择时触发，返回更新后的数据对象
 *
 * 使用示例：
 * <AJ-model
 *   ref="AJModel"
 *   :data="itemData"
 *   @confirm="handleConfirm"
 * />
-->

<template>
  <uni-popup ref="popup" type="right" :mask-click="true">
    <view class="aj-drawer">
      <view class="drawer-header">
        <text class="title">经络</text>
        <uni-icons @click="handleClose" type="closeempty" size="18" color="#999999"></uni-icons>
      </view>

      <view class="drawer-content">
        <!-- 添加经络选择区域 -->
        <view class="meridian-container">
          <!-- 左侧经络列表 -->
          <scroll-view class="meridian-list" scroll-y>
            <view
              v-for="item in meridianList"
              :key="item.name"
              :class="['meridian-item', { active: currentMeridian === item.name }]"
              @click="handleSelectMeridian(item)"
            >
              {{ item.name }}
              <text class="count" v-if="item.count">{{ item.count }}</text>
            </view>
          </scroll-view>

          <!-- 右侧穴位选择区域 -->
          <view class="acupoint-container">
            <view class="acupoint-list">
              <view
                v-for="point in acupointList"
                :key="point.name"
                :class="['acupoint-item', { active: isPointSelected(point) }]"
                @click="handleSelectAcupoint(point)"
              >
                <text class="item-name">{{ point.name }}</text>
                <text
                  v-if="point.number === '2'"
                  class="side"
                  @click.stop="toggleSide(point)"
                >{{ point.typeName }}</text>
                <text
                  v-else
                  @click.stop
                  class="side side-disabled"
                >{{ point.typeName }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 已选穴位展示区域 -->
        <view class="selected-points">
          <text class="label">已选穴位：</text>
          <view class="selected-tags">
            <view v-for="(point, index) in selectedPoints" :key="index" class="tag">
              {{ point.name }}·{{ point.typeName }}
              <text class="remove" @click="removePoint(index)">×</text>
            </view>
          </view>
        </view>
      </view>

      <view class="drawer-footer">
        <button class="btn cancel" @click="handleClose">取消</button>
        <button class="btn confirm" @click="handleConfirm">确定</button>
      </view>
    </view>
  </uni-popup>
</template>

<script>
import {$operator} from "@/utils/operation";
import {cloneDeep} from "lodash";
export default {
  name: 'AJModel',

  data() {
    return {
      currentMeridian: '',
      meridianList: [
        { name: '手太阳小肠经', count: 2 },
        { name: '足太阳膀胱经' },
        { name: '手阳明大肠经' },
        { name: '足阳明胃经' },
        // ... 其他经络
      ],
      acupointList: [],
      selectedPoints: [],
      modelData: {
        acupoint_list: []
      }
    }
  },

  methods: {
    /**
     * 打开抽屉
     * @param {Object} data - 艾灸数据对象
     */
    open(data) {
      this.modelData = cloneDeep(data)
      // 同步已选中的穴位数据
      if (data.acupoint_list && data.acupoint_list.length > 0) {
        this.selectedPoints = cloneDeep(data.acupoint_list)
      } else {
        this.selectedPoints = []
      }
      // 默认选中第一个经络
      if (this.meridianList.length > 0) {
        this.handleSelectMeridian(this.meridianList[0])
      }
      this.$refs.popup.open()
    },

    /**
     * 关闭抽屉
     */
    handleClose() {
      this.$refs.popup.close()
    },

    /**
     * 确认操作
     * 触发confirm事件，将修改后的数据传递给父组件
     */
    handleConfirm() {
      // 计算选中穴位的总数量和总金额
      const totalNum = this.selectedPoints.reduce((sum, point) => {
        return sum + (point.typeCode === 2 ? 2 : 1);
      }, 0);

      const totalPrice = this.selectedPoints.reduce((sum, point) => {
        const pointPrice = $operator.multiply(point.price, point.typeCode === 2 ? 2 : 1);
        return $operator.add(sum, pointPrice);
      }, 0);

      // 格式化穴位名称和类型文本
      const servTypeText = this.selectedPoints
        .map(point => `${point.name}(${point.typeName}侧)`)
        .join(',');

      // 构建返回数据结构
      const result = {
        type: 'AJ',
        serv_type: 'AJ',
        serv_type_text: servTypeText,
        num: totalNum,
        price: totalPrice.toFixed(2),
        acupoint_list: this.selectedPoints,
        quantity: 1
      };
      result.id = this.modelData.id
      this.$emit('confirm', result);
      this.handleClose();
    },

    handleSelectMeridian(meridian) {
      this.currentMeridian = meridian.name
      // 使用新的穴位数据结构
      this.acupointList = [
        {
          "name": "少泽",
          "code": "7be8cab3093a47e5828e2d67c0e95e05",
          "image": "https://pub-static.rsjia.com/knowledge_base/1580520175863.SI1.png",
          "number": "2",
          "price": "26",
          "typeCode": 1,
          "typeName": "单"
        },
        {
          "name": "支正",
          "code": "dd381c38b9574386a576f8148ff7a233",
          "image": "https://pub-static.rsjia.com/knowledge_base/1580540043333.SI7.png",
          "number": "2",
          "price": "26",
          "typeCode": 2,
          "typeName": "双",
          "popVisible": false
        },
        {
          "name": "曲垣",
          "code": "150ea735e46e46cda35118d411b27d57",
          "image": "https://pub-static.rsjia.com/knowledge_base/1580474062641.SI13.png",
          "number": "2",
          "price": "26",
          "typeCode": 1,
          "typeName": "单"
        }
      ]
    },

    handleSelectAcupoint(point) {
      // 检查是否已存在相同的穴位
      const existingIndex = this.selectedPoints.findIndex(
        p => p.code === point.code && p.meridian === this.currentMeridian
      )

      if (existingIndex !== -1) {
        // 如果已存在，则移除该穴位
        this.selectedPoints.splice(existingIndex, 1)
      } else {
        // 如果不存在，则添加新穴位
        const fullPoint = {
          ...point,
          meridian: this.currentMeridian
        }
        this.selectedPoints.push(fullPoint)
      }

      this.updateModelData()
    },

    removePoint(index) {
      this.selectedPoints.splice(index, 1)
      this.updateModelData()
    },

    updateModelData() {
      this.modelData.acupoint_list = this.selectedPoints
    },

    // 添加判断穴位是否被选中的方法
    isPointSelected(point) {
      return this.selectedPoints.some(p => p.code === point.code)
    },

    /**
     * 切换穴位的单双属性
     * @param {Object} point - 穴位对象
     */
    toggleSide(point) {
      // 只有当number为2时才允许切换
      if (point.number !== '2') return

      point.typeName = point.typeName === '单' ? '双' : '单'
      point.typeCode = point.typeCode === 1 ? 2 : 1

      // 更新已选中的穴位
      const selectedIndex = this.selectedPoints.findIndex(
        p => p.code === point.code && p.meridian === this.currentMeridian
      )
      if (selectedIndex !== -1) {
        this.selectedPoints[selectedIndex].typeName = point.typeName
        this.selectedPoints[selectedIndex].typeCode = point.typeCode
        this.updateModelData()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.aj-drawer {
  width: 1480rpx;
  height: 100vh;
  background: #FFFFFF;
  display: flex;
  flex-direction: column;

  .drawer-header {
    padding: 32rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2rpx solid #EBEDF0;

    .title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
    }
  }

  .drawer-content {
    flex: 1;
    overflow-y: auto;

    .meridian-container {
      display: flex;
      height: calc(100vh - 300rpx);

      .meridian-list {
        width: 300rpx;
        border-right: 2rpx solid #EBEDF0;
        background: #FAFAFB;

        .meridian-item {
          padding: 24rpx 24rpx 24rpx 34rpx;
          font-size: 28rpx;
          color: #333;
          display: flex;
          justify-content: space-between;
          border-left: 6rpx solid #FAFAFB;

          &.active {
            background: #F5F7FA;
            color: #2B6DE3;
            border-color: #2B6DE3;
          }

          .count {
            color: #999;
          }
        }
      }

      .acupoint-container {
        flex: 1;
        padding: 24rpx;

        .acupoint-list {
          @include flex(row, flex-start, flex-start);
          flex-wrap: wrap;
          .acupoint-item {
            width: 188rpx;
            height: 60rpx;
            background: #FFFFFF;
            border-radius: 8rpx;
            border: 2rpx solid #DCDFE6;
            opacity: 0.8;
            margin: 12rpx;
            @include flex(row, center, center);
            .item-name {
              flex: 1;
              font-size: 26rpx;
              @include flex(row, center, center);
            }
            .side {
              width: 60rpx;
              height: 100%;
              border-left: 2rpx solid #DCDFE6;
              color: #999;
              font-size: 22rpx;
              @include flex(row, center, center);
            }

            &.active {
              border-color: #155BD4;
              color: #155BD4;

              .side {
                color: #999;
              }
            }
          }
        }
      }
    }

    .selected-points {
      position: sticky;
      bottom: 0;
      background: #FFFFFF;
      padding: 24rpx;
      border-top: 2rpx solid #EBEDF0;
      display: flex;
      align-items: center;
      flex-wrap: wrap;

      .label {
        width: 140rpx;
        font-size: 28rpx;
        color: #666666;
        display: block;
      }

      .selected-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 12rpx;

        .tag {
          background: #F5F7FA;
          padding: 8rpx 16rpx;
          border-radius: 4rpx;
          font-size: 24rpx;

          .remove {
            margin-left: 8rpx;
            color: #999;
          }
        }
      }
    }
  }

  .drawer-footer {
    padding: 24rpx 32rpx;
    display: flex;
    justify-content: flex-end;
    border-top: 2rpx solid #EBEDF0;

    .btn {
      width: 160rpx;
      height: 72rpx;
      border-radius: 8rpx;
      font-size: 28rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0;

      &.cancel {
        background: #F5F5F5;
        color: #666666;
      }

      &.confirm {
        background: #2B6DE3;
        color: #FFFFFF;
      }
    }
    .btn:last-child {
      margin-left: 24rpx;
    }
  }
}

.acupoint-item {
  .side-disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}
</style>
