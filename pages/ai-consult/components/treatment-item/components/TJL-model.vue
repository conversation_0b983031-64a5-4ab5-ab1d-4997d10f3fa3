<!--
 * @description 通经络抽屉组件
 * <AUTHOR> Name
 * @createDate 2024-xx-xx
 *
 * 功能说明：
 * 1. 展示通经络治疗的详细信息
 * 2. 显示选择的经络列表
 * 3. 支持查看和编辑通经络相关配置
 *
 * 使用说明：
 * <TJL-model ref="TJLModel" :data="itemData" @confirm="handleConfirm" />
-->

<template>
  <uni-popup ref="popup" type="right" :mask-click="true">
    <view class="tjl-drawer">
      <view class="drawer-header">
        <text class="title">经络设定</text>
        <uni-icons @click="handleClose" type="closeempty" size="18" color="#999999"></uni-icons>
      </view>

      <view class="drawer-content">
        <!-- 主体两列布局 -->
        <view class="main-layout">
          <!-- 左侧经络列表 -->
          <scroll-view class="meridian-list" scroll-y>
            <view
              v-for="item in meridianOptions"
              :key="item.value"
              class="meridian-item"
              :class="{
                active: currentMeridianId === item.value,
                checkedActive: selectedMeridians.includes(item.value) && currentMeridianId !== item.value
              }"
              @click="checkedCurMeridian(item)"
            >
              <image
                class="checkbox-icon"
                @click.stop="toggleMeridian(item)"
                :src="selectedMeridians.includes(item.value) ? checkedIcon : uncheckedIcon"
              ></image>
              <text class="item-text">{{ item.label }}</text>
            </view>
          </scroll-view>

          <!-- 右侧内容区 - 嵌套两列布局 -->
          <view class="content-area">
            <!-- 经络详情区 -->
            <view class="meridian-info">
              <view class="meridian-info-title">经络详情</view>
              <text class="meridian-name">{{ currentMeridian.label }}</text>
              <image
                class="meridian-image"
                :src="currentMeridian.image"
                mode="widthFix"
              ></image>
            </view>

            <!-- 详情说明区 -->
            <scroll-view class="detail-info" scroll-y>
              <!-- 功能说明 -->
              <view class="detail-section">
                <view class="section-title">功能：</view>
                <view class="section-content">{{ currentMeridian.functions }}</view>
              </view>

              <!-- 循行说明 -->
              <view class="detail-section">
                <view class="section-title">循行：</view>
                <view class="section-content">{{ currentMeridian.path }}</view>
              </view>

              <!-- 病候说明 -->
              <view class="detail-section">
                <view class="section-title">病候：</view>
                <view class="section-content">{{ currentMeridian.symptoms }}</view>
              </view>

              <!-- 综述说明 -->
              <view class="detail-section">
                <view class="section-title">综论：</view>
                <view class="section-content">{{ currentMeridian.summary }}</view>
              </view>
            </scroll-view>
          </view>
        </view>

        <!-- 已选经络展示 -->
        <view class="selected-meridians" v-if="selectedMeridians.length">
          <text class="label">已选穴位：</text>
          <view class="tags">
            <view
              v-for="(meridian, index) in selectedMeridianLabels"
              :key="index"
              class="tag"
            >
              {{ meridian }}
              <uni-icons
                type="clear"
                size="12"
                color="#999999"
                @click="removeMeridian(meridian)"
              ></uni-icons>
            </view>
          </view>
        </view>
      </view>

      <view class="drawer-footer">
        <button class="btn cancel" @click="handleClose">取消</button>
        <button class="btn confirm" @click="handleConfirm">确定</button>
      </view>
    </view>
  </uni-popup>
</template>

<script>
export default {
  name: 'TJLModel',

  data() {
    return {
      checkedIcon: 'https://static.rsjxx.com/image/2025/0211/164318_60406.png-B.200',
      uncheckedIcon: 'https://static.rsjxx.com/image/2025/0211/164318_57299.png-B.200',
      // 经络选项列表
      meridianOptions: [
        {
          label: '手太阴肺经',
          value: 'STYFE',
          image: 'https://pub-static.rsjia.com/knowledge_base/jingluoxunxing/zutaiyin_pijing.gif',
          functions: '主治外感、头痛、现项、咳嗽咽等证。',
          path: '起于中焦，向下联络大肠回络胃口过膈属于肺脏，从肺系（肺与咽喉相联系的部位）横行出来，沿上臂内侧下行，行于手少阴经和手厥阴经的前面，经射穴入寸口。沿鱼际线，出拇指内侧端（少商），手腕后方支脉，从列缺处外分出，走向喉内侧侧端，与手阳明大肠经相接。',
          symptoms: '咳嗽气喘气短、咳血咽痛、外感伤风、循环部位痛痹或活动受限等。主咳嗽、心烦、掌中热。',
          summary: '丁火之脏，脉在左寸，实则肺热而咽喘，静'
        },
        { label: '手阳明大肠经', value: 'SYMDCE' },
        { label: '足阳明胃经', value: 'ZYMWE' },
        { label: '足太阴脾经', value: 'ZTYPJ' },
        { label: '手少阴心经', value: 'SSYXJ' },
        { label: '手太阳小肠经', value: 'STYXCJ' },
        { label: '足太阳膀胱经', value: 'ZTYPGJ' },
        { label: '足少阴肾经', value: 'ZSYSJ' },
        { label: '手厥阴心包经', value: 'SJYXBJ' },
        { label: '手少阳三焦经', value: 'SSYSJJ' },
        { label: '足少阳胆经', value: 'ZSYDJ' },
        { label: '足厥阴肝经', value: 'ZJYGJ' }
      ],
      selectedMeridians: [], // 已选经络列表
      currentMeridianId: '',
      currentMeridian: {}, // 当前查看的经络详情
      detail: ''
    }
  },

  computed: {
    // 已选经络的标签文本
    selectedMeridianLabels() {
      return this.selectedMeridians.map(value =>
        this.meridianOptions.find(option => option.value === value)?.label || ''
      )
    }
  },

  methods: {
    /**
     * 打开抽屉
     */
    open(data) {
      this.detail = data
      this.selectedMeridians = Object.keys(data?.meridian_map || {})
      this.currentMeridian = this.meridianOptions[0]
      this.currentMeridianId = this.meridianOptions?.[0]?.value
      this.$refs.popup.open()
    },

    /**
     * 关闭抽屉
     */
    handleClose() {
      this.$refs.popup.close()
    },

    /**
     * 切换经络选择状态
     */
    toggleMeridian(item) {
      const index = this.selectedMeridians.indexOf(item.value)
      if (index > -1) {
        this.selectedMeridians.splice(index, 1)
      } else {
        this.selectedMeridians.push(item.value)
      }
    },
    /**
     * 切换选择当前经络
     */
    checkedCurMeridian(item) {
      this.currentMeridianId = item.value
      this.currentMeridian = item
    },

    /**
     * 移除已选经络
     */
    removeMeridian(label) {
      const item = this.meridianOptions.find(option => option.label === label)
      if (item) {
        const index = this.selectedMeridians.indexOf(item.value)
        if (index > -1) {
          this.selectedMeridians.splice(index, 1)
        }
      }
    },

    /**
     * 确认选择
     */
    handleConfirm() {
      const selectedList = this.meridianOptions.filter(item => this.selectedMeridians.indexOf(item.value) !== -1)
      const meridian_map = Object.fromEntries(selectedList.map(item => [item.value, item.label]))
      const selectedData =  {
        serv_type: 'TJL',
        serv_type_text: selectedList.map(item => item.label).join(','),
        num: selectedList.length || 0,
        quantity: 1,
        price: '2.11',
        meridian_map
      }
      if (this.detail.id) {
        selectedData.id = this.detail.id
      }
      this.$emit('confirm', selectedData)
      this.handleClose()
    }
  }
}
</script>

<style lang="scss" scoped>
.tjl-drawer {
  width: 1480rpx;
  height: 100vh;
  background: #FFFFFF;
  display: flex;
  flex-direction: column;

  .drawer-header {
    padding: 32rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2rpx solid #EBEDF0;

    .title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
    }
  }

  .drawer-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .main-layout {
      flex: 1;
      display: flex;
      overflow: hidden;
    }

    .meridian-list {
      width: 340rpx;
      border-right: 2rpx solid #EBEDF0;
      padding: 24rpx 40rpx;
      height: 100%;
      box-sizing: border-box;

      .meridian-item {
        padding: 16rpx 24rpx;
        display: flex;
        align-items: center;
        cursor: pointer;
        border: 2rpx solid #DCDFE6;
        border-radius: 8rpx;
        background: #FFFFFF;
        margin-bottom: 24rpx;

        .checkbox-icon {
          width: 32rpx;
          height: 32rpx;
          margin-right: 16rpx;
        }

        .item-text {
          font-size: 26rpx;
          color: #333333;
        }

        &.active {
          background: #EDF3FC;
          border-color: #155BD4;
          color: #2B6DE3;
          .item-text {
            color: #2B6DE3;
          }
        }

        &.checkedActive {
          .item-text {
            color: #2B6DE3;
          }
        }
      }
    }

    .content-area {
      flex: 1;
      display: flex;
      overflow: hidden;

      .meridian-info {
        width: 560rpx;
        padding: 32rpx;
        border-right: 2rpx solid #EBEDF0;
        box-sizing: border-box;

        .meridian-info-title {
          font-weight: 500;
          font-size: 30rpx;
          color: #333333;
          line-height: 44rpx;
        }
        .meridian-name {
          font-size: 28rpx;
          line-height: 44rpx;
          color: #999999;
          margin-top: 16rpx;
        }

        .meridian-image {
          width: 100%;
          margin-top: 40rpx;
        }
      }

      .detail-info {
        flex: 1;
        height: 100%;
        box-sizing: border-box;
        padding: 32rpx;

        .detail-section {
          margin-bottom: 24rpx;

          .section-title {
            font-size: 28rpx;
            font-weight: 500;
            color: #333333;
            margin-bottom: 8rpx;
          }

          .section-content {
            font-size: 26rpx;
            color: #666666;
            line-height: 1.6;
          }
        }
      }
    }

    .selected-meridians {
      @include flex();
      padding: 24rpx 32rpx;
      border-top: 2rpx solid #EBEDF0;

      .label {
        width: 140rpx;
        font-size: 28rpx;
        color: #666666;
        display: block;
      }

      .tags {
        flex: 1;
        display: flex;
        flex-wrap: wrap;
        gap: 16rpx;

        .tag {
          padding: 8rpx 16rpx;
          background: #F5F7FA;
          border-radius: 4rpx;
          font-size: 24rpx;
          color: #333333;
          display: flex;
          align-items: center;
          gap: 8rpx;
        }
      }
    }
  }

  .drawer-footer {
    padding: 24rpx 32rpx;
    display: flex;
    justify-content: flex-end;
    border-top: 2rpx solid #EBEDF0;

    .btn {
      width: 160rpx;
      height: 72rpx;
      border-radius: 8rpx;
      font-size: 28rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0;

      &.cancel {
        background: #F5F5F5;
        color: #666666;
      }

      &.confirm {
        background: #2B6DE3;
        color: #FFFFFF;
      }
    }
    .btn:last-child {
      margin-left: 24rpx;
    }
  }
}
</style>
