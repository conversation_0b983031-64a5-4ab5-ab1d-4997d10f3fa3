<!--
 * @description 自定义底部弹出选择器组件
 * <AUTHOR> Name
 * @createDate 2024-xx-xx
 *
 * 功能说明：
 * 1. 底部弹出的选择器
 * 2. 支持点击遮罩层关闭
 * 3. 支持右上角关闭按钮
 *
 * 使用说明：
 * <custom-picker v-model="visible" :options="options" @select="handleSelect" />
-->

<template>
  <uni-popup ref="popup" type="bottom" :mask-click="true" @change="onVisibleChange">
    <view class="picker-wrapper">
      <!-- 头部区域 -->
      <view class="picker-header">
        <text class="title">选择项目类型</text>
        <uni-icons
          class="picker-close"
          type="closeempty"
          size="18"
          color="#999999"
          @click="handleClose"
        ></uni-icons>
      </view>

      <!-- 选项列表区域 -->
      <view class="picker-content">
        <view
          v-for="(item, index) in options"
          :key="index"
          class="picker-item"
          :class="{ active: modelValue === item.value }"
          @click="handleSelect(item)"
        >
          <text class="item-text">{{ item.text }}</text>
          <uni-icons
            v-if="modelValue === item.value"
            type="checkmarkempty"
            size="16"
            color="#2B6DE3"
          ></uni-icons>
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<script>
export default {
  name: 'CustomPicker',

  props: {
    /**
     * 选项列表
     * @type {Array<{text: string, value: string}>}
     */
    options: {
      type: Array,
      default: () => []
    },

    /**
     * 当前选中的值
     */
    modelValue: {
      type: [String, Number],
      default: ''
    }
  },

  emits: ['update:modelValue', 'select', 'close'],

  methods: {
    /**
     * 打开选择器
     */
    open() {
      this.$refs.popup.open()
    },

    /**
     * 关闭选择器
     */
    close() {
      this.$refs.popup.close()
    },

    /**
     * 处理关闭事件
     */
    handleClose() {
      this.close()
      this.$emit('close')
    },

    /**
     * 处理选项选择
     * @param {Object} item - 选中的选项
     */
    handleSelect(item) {
      this.$emit('update:modelValue', item.value)
      this.$emit('select', item)
      this.close()
    },

    /**
     * 处理弹窗状态变化
     * @param {Object} e - 状态变化事件对象
     */
    onVisibleChange(e) {
      if (!e.show) {
        this.$emit('close')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.picker-wrapper {
  background-color: #FFFFFF;
  padding-bottom: 48rpx;
  border-radius: 24rpx 24rpx 0 0;

  .picker-header {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 32rpx;
    border-bottom: 2rpx solid #EBEDF0;
    position: relative;

    .title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
    }
    .picker-close {
      position: absolute;
      width: 60rpx;
      height: 60rpx;
      @include flex(row, center, center);
      right: 20rpx;
      top: calc((100% - 60rpx) / 2);
    }
  }

  .picker-content {
    max-height: 60vh;
    overflow-y: auto;

    .picker-item {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 32rpx;

      &.active {
        background-color: #F5F7FA;

        .item-text {
          color: #2B6DE3;
        }
      }

      .item-text {
        font-size: 28rpx;
        color: #333333;
      }
    }
  }
}
</style>
