<!--
 * @description 治疗项目列表组件
 * <AUTHOR> Name
 * @createDate 2024-xx-xx
 *
 * 功能说明：
 * 1. 展示治疗项目列表，包括项目名称、规格、数量和金额
 * 2. 支持项目数量调整（非只读模式）
 * 3. 支持左滑删除单个项目
 * 4. 支持清空所有项目
 * 5. 自动计算单项金额和总金额
 *
 * 使用说明：
 * <treatment-item :isReadOnly="false" />
-->

<template>
  <!-- 治疗项目列表组件 - 用于展示和管理治疗项目清单 -->
  <view class="items-wrapper">
    <view class="items-header base-box-shadow">
      <view class="goods-title base-title">诊疗项目</view>
      <template v-if="!isReadOnly">
        <button @click="addItems" class="mr-12">
          <uni-icons type="plusempty" size="14"></uni-icons>
          添加项目
        </button>
        <button @click="clearItems" :disabled="itemsList.length === 0" >
          清空项目
        </button>
      </template>
    </view>
    <view class="items-table">
      <uni-swipe-action ref="swipeAction">
        <uni-swipe-action-item
          v-for="(row, index) in itemsList"
          :key="index"
          :disabled="isReadOnly"
          :right-options="options"
          @click="onClick(index)"
          @change="swipeChange($event, 0)"
        >
          <uni-row class="items-table-row">
            <uni-col :span="1" class="items-table-col">
              {{ index + 1 }}
            </uni-col>
            <uni-col :span="10" class="items-table-col">
              <view class="item-type">
                <view class="flex-1" v-if="row.serv_type === 'QS'">
                  {{ row.serv_type_text }}
                </view>
                <view class="items-type-text flex-1" v-else @click="openDetail(row)">
                  {{ row.serv_type_text }}
                </view>
                <view class="product-stock" v-if="row.serv_type !== 'QS' && !AJCodes.includes(row.code)">
                  {{row.num || '-'}}
                  {{ spec_type_maps[row.serv_type] }}
                </view>
              </view>
            </uni-col>
            <uni-col :span="4" class="items-table-col">
              <view class="items-type">{{ serv_type_maps[row.serv_type] }}</view>
            </uni-col>
            <uni-col :span="5" class="items-table-col">
              <view class="items-count">
                <u-number-box
                  v-model="row.quantity"
                  :min="1"
                  :step="1"
                  integer
                  inputWidth="80"
                  buttonSize="60"
                  :disabled="isReadOnly"
                  @change="v=>changeQuantity(v,index)"
                >
                </u-number-box>
<!--                <view class="text">次</view>-->
              </view>
            </uni-col>
            <uni-col :span="4" class="items-table-col">
              <view class="price-info">
                <text class="price-symbol">¥</text>
                <text class="price-value">{{ getRowTotal(row) }}</text>
              </view>
            </uni-col>
          </uni-row>
        </uni-swipe-action-item>
      </uni-swipe-action>
      <view class="goods-footer">
        <view class="total-title">合计：</view>
        <view class="total-price">¥{{ totalAmount }}</view>
      </view>
    </view>

    <confirm-dialog
      ref="clearConfirm"
      title="清空项目"
      message="确定要清空所有诊疗项目吗？"
      @confirm="handleClearConfirm"
    ></confirm-dialog>

    <!-- 自定义选择器 -->
    <custom-picker
      v-model="selectedServType"
      :options="servTypeOptions"
      @select="handleServTypeSelect"
      ref="customPicker"
    ></custom-picker>

    <!-- 通经络抽屉 -->
    <TJLModel
      ref="TJLModel"
      @confirm="handleItemsConfirm"
    ></TJLModel>

    <!-- 艾灸抽屉 -->
    <AJModel
      ref="AJModel"
      @confirm="handleItemsConfirm"
    ></AJModel>
  </view>
</template>

<script>
/**
 * 治疗项目列表组件
 * @module TreatmentItem
 * @description 用于展示和管理治疗项目，支持添加、删除、修改数量等操作
 * @requires components/confirm-dialog
 * @requires utils/operation
 */
import ConfirmDialog from '@/components/confirm-dialog/index.vue';
import CustomPicker from './components/custom-picker.vue'
import TJLModel from './components/TJL-model.vue'
import AJModel from './components/AJ-model.vue'
import {$operator} from "@/utils/operation";

export default {
  name: "treatmentItem",
  components: {
    ConfirmDialog,
    CustomPicker,
    TJLModel,
    AJModel
  },
  props: {
    isReadOnly: Boolean
  },
  data() {
    return {
      /**
       * 项目列表数据
       * @type {Array<Object>}
       * @property {string} serv_type - 服务类型代码（QS/TJL/AJ）
       * @property {string} serv_type_text - 服务描述文本
       * @property {number} num - 默认数量
       * @property {number} quantity - 当前数量
       * @property {string} price - 单价
       * @property {Array} [meridian_map] - 经络图谱（仅TJL类型）
       * @property {Array} [acupoint_list] - 穴位列表（仅AJ类型）
       */
      itemsList: [],
      /**
       * 服务类型中文映射表
       * @type {Object.<string, string>}
       * @description 将服务类型代码映射为中文显示文本
       */
      serv_type_maps: {
        'QS': '淋漓祛湿',
        'TJL': '通经络',
        'AJ': '艾灸调理',
      },
      /**
       * 规格类型映射表
       * @type {Object.<string, string>}
       * @description 不同服务类型对应的规格显示文本
       */
      spec_type_maps: {
        'QS': '-',
        'TJL': '经络',
        'AJ': '穴位',
      },
      /**
       * 滑动操作按钮配置
       * @type {Array<Object>}
       * @description 配置左滑显示的删除按钮样式和文本
       */
      options: [{
        text: "删除",
        style: {
          backgroundColor: '#E63E3E'
        }
      }],
      /**
       * 艾灸服务特定编码列表
       * @type {Array<string>}
       * @description 存储艾灸服务相关的唯一标识符
       */
      AJCodes: ['ec27b4c1c43ae073ca585385f73b1357', '0d718695720ac9cc1735b8f2ffb3f67d'],
      /**
       * 自定义选择器相关数据
       */
      selectedServType: '',
      servTypeOptions: [
        {text: '淋漓祛湿', value: 'QS'},
        {text: '通经络', value: 'TJL'},
        {text: '艾灸调理', value: 'AJ'},
      ],
    }
  },
  methods: {
    /**
     * 打开添加项目选择器
     */
    addItems() {
      this.$refs.customPicker.open()
    },

    /**
     * 处理服务类型选择
     * @param {Object} item - 选中的选项
     */
    handleServTypeSelect(item) {
      switch(item.value) {
        case 'QS':
          this.addQSItem()
          break
        case 'TJL':
          this.$refs.TJLModel.open({
            meridian_map: {}
          })
          break
        case 'AJ':
          this.$refs.AJModel.open({
            acupoint_list: []
          })
          break
      }
    },

    /**
     * 清空项目列表
     * @description 调用确认对话框组件进行二次确认
     */
    clearItems() {
      this.$refs.clearConfirm.open()
    },

    /**
     * 确认清空操作的回调
     * @description 当用户在确认对话框中点击确认时执行，清空整个项目列表
     */
    handleClearConfirm() {
      this.itemsList = []
    },

    /**
     * 删除单个项目
     * @param {number} index - 要删除的项目索引
     * @description 显示确认弹窗，确认后从列表中移除该项目
     */
    onClick(index) {
      this.itemsList.splice(index, 1)
      uni.showToast({
        title: '删除成功',
        icon: 'none'
      });
      this.resetSwipeAction()
    },

    resetSwipeAction() {
      this.$nextTick(() => {
        this.$refs.swipeAction.closeAll();
        this.$refs.swipeAction.resize();
      })

    },
    /**
     * 处理诊疗项目选择确认
     * @param {Object} data - 诊疗项目选择数据
     * @description 将选中的诊疗项目添加到列表或更新现有项目
     */
    handleItemsConfirm(data) {
      if (!data || data.num <= 0) return
      if (!data.id) {
        // 新增数据
        data.id = Math.random().toString(36).substring(2) + Date.now().toString(36)
        this.itemsList.push(data)
      } else {
        // 更新现有数据
        const index = this.itemsList.findIndex(item => item.id === data.id)
        if (index !== -1) {
          // 保存原有的quantity值
          const originalQuantity = this.itemsList[index].quantity
          // 更新数据时保持原有的quantity值不变
          data.quantity = originalQuantity
          this.$set(this.itemsList, index, data)
        }
      }
    },

    openDetail(row) {
      if (row.serv_type === 'QS') return
      if (row.serv_type === 'TJL') {
        this.$refs.TJLModel.open({
          id: row.id,
          meridian_map: row.meridian_map
        })
      }
      if (row.serv_type === 'AJ') {
        this.$refs.AJModel.open({
          id: row.id,
          acupoint_list: row.acupoint_list
        })
      }
    },
    /**
     * 处理滑动事件
     * @param {string} e - 滑动方向
     * @param {number} index - 项目索引
     * @description 当向右滑动时关闭所有打开的滑动项
     */
    swipeChange(e, index) {
      if (e === 'right') {
        this.$refs.swipeAction.closeAll()
      }
    },

    /**
     * 更新项目数量
     * @param {number} value - 新的数量值
     * @param {number} index - 项目索引
     * @description 更新指定项目的数量
     */
    changeQuantity(value, index) {
      this.itemsList[index].quantity = value
      // 这里可以添加数量变化后的其他逻辑，比如计算总价等
    },

    /**
     * 计算单行项目的总价
     * @param {Object} row - 项目数据对象
     * @returns {string} 格式化后的价格字符串，保留两位小数
     * @description 使用 $operator.multiply 确保精确计算
     * @throws {Error} 计算过程中可能出现的错误
     */
    getRowTotal(row) {
      if (!row.price || !row.quantity) return '0.00';
      try {
        const total = $operator.multiply(row.price, row.quantity);
        return parseFloat(total).toFixed(2);
      } catch (error) {
        console.error('计算价格出错:', error);
        return '0.00';
      }
    },

    /**
     * 添加PRL服务项目
     * @description 直接添加一条默认格式的PRL服务数据到列表中
     */
    addQSItem() {
      const qsItem = {
        serv_type: 'QS',
        serv_type_text: 'PRL服务',
        num: 1,
        quantity: 1,
        price: '111.11'
      }
      this.itemsList.push(qsItem)
    },
  },
  computed: {
    /**
     * 计算所有项目的总金额
     * @returns {string} 格式化后的总金额，保留两位小数
     * @description 使用 $operator.multiply 和 $operator.add 确保精确计算
     * @throws {Error} 计算过程中可能出现的错误
     */
    totalAmount() {
      try {
        const total = this.itemsList.reduce((sum, item) => {
          const rowTotal = $operator.multiply(item.price || 0, item.quantity || 0);
          return $operator.add(sum, rowTotal);
        }, 0);
        return parseFloat(total).toFixed(2);
      } catch (error) {
        console.error('计算总价出错:', error);
        return '0.00';
      }
    }
  }
}
</script>

<style scoped lang="scss">
.items-wrapper {
  background: #fff;
  margin-top: 24rpx;

  .items-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16rpx 32rpx;

    .items-title {
      .refresh-btn {
        display: flex;
        align-items: center;
      }
    }

    .mr-3 {
      margin-right: 3rpx;
    }
  }
  .items-table {
    font-size: 28rpx;
    line-height: 40rpx;
    overflow: visible;

    .items-table-row {
      padding: 16rpx 32rpx;
      border-bottom: 2rpx solid #EBEDF0;
      overflow: visible;
      @include flex();
      gap: 12rpx;
      overflow: visible;

      .items-table-col {
        height: 100%;
        overflow: visible;

        .items-type {
          width: 100%;
          height: 80rpx;
          min-width: 80rpx;
          border-radius: 8rpx;
          @include flex(row, flex-end, center)
        }

        .item-type {
          width: 100%;
          @include flex(row, space-between, center);
          .flex-1 {
            flex: 1;
          }
          .items-type-text {
            color: #115bd5;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: break-all;
          }
          .product-stock {
            color: #999999;
            padding-left: 12rpx;
          }
        }

        .items-count {
          width: 100%;
          @include flex(row, flex-start, center);
          .text {
            padding-left: 12rpx;
          }
        }

        .price-info {
          display: flex;
          justify-content: flex-end;
          align-items: baseline;
          font-size: 28rpx;
          padding: 8rpx 0;
          border-radius: 8rpx;
          transition: background-color 0.2s;

          .price-symbol {
            margin-right: 4rpx;
          }
        }
      }
    }
  }
}
.mr-12 {
  margin-right: 24rpx;
}
.goods-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 24rpx 44rpx;
  font-size: 28rpx;
  line-height: 40rpx;

  .total-title {
    color: #999999;
    margin-right: 8rpx;
  }

  .total-price {
    font-weight: 600;
    color: #333333;
  }
}
</style>
