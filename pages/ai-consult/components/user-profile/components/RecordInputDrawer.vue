<template>
  <uni-drawer ref="inputDrawer" :maskClick="true" mode="right" :duration="300" :width="740"
              @change="onVisibleChange">
    <view class="popup-wrapper">
      <view class="pop-header">
        <view class="pop-title">{{ recordItem.label }}</view>
        <uni-icons type="closeempty" size="16" color="#AAAAAA" @click="close"></uni-icons>
      </view>
      <view class="pop-content">
        <component :is="getComponentName"
                   ref="recordItem"
                   @handleSelect="handleSelect"
                   :data-source="getDataSource"
                   :form-data="formData"
                   :source="source"
                   @handlerDayClick="handleDayClick"></component>
      </view>
      <view class="pop-footer">
        <view class="footer-title">{{ recordItem.label }}:</view>
        <uni-easyinput class="pop-footer-input" v-model="inputValue" type="textarea" auto-height
                       @input="handleInput"
                       :placeholder="recordItem.placeholder" clearable></uni-easyinput>
        <button class="pop-footer-btn" type="primary" @click="saveHandler">保存</button>
      </view>
    </view>
  </uni-drawer>
</template>
<script>
import ChiefComplaint from "@/pages/ai-consult/components/user-profile/components/chief-complaint/index.vue";
import OtherSymptoms from "@/pages/ai-consult/components/user-profile/components/other-symptoms/index.vue";
import PastHistory from "@/pages/ai-consult/components/user-profile/components/past-history/index.vue";
import PhysicalExamination from "@/pages/ai-consult/components/user-profile/components/physical-examination/index.vue";
import ConsultationService from "@/api/consultation";

export default {
  name: "RecordInputDrawer",
  components: {
    ChiefComplaint,
    OtherSymptoms,
    PastHistory,
    PhysicalExamination,
  },
  model: {
    prop: "visible",
    event: "changeVisible"
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    recordItem: {
      type: Object,
      default: () => ({
        propKey: '',
        label: '',
        placeholder: '',
        required: false,
        showDayList: false,
        value: '',
        componentName: ''
      })
    },
    formData: {
      type: Object,
      default: () => ({})
    },
    source: {
      type: String,
      default: ''
    },
  },
  computed: {
    getComponentName() {
      return this.recordItem.componentName || ''
    },
    getDataSource() {
      switch (this.recordItem.propKey) {
        case 'engrave_text':
          return this.complaints;
        case 'diagnostic_methods':
          return this.present_desc;
        case 'physique':
          return this.healthMap;
        case 'past':
          return this.pastHistory;
        default:
          return [];
      }
    }
  },
  data() {
    return {
      inputValue: '',
      complaints: [],
      present_desc: [],
      healthMap: [],
      pastHistory: []
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.open()
      } else {
        this.close()
      }
    },
    inputValue(val) {
      this.emitRecordItem();
    }
  },
  mounted() {
    this.getRecordOptions();
    this.getAssistOptions();
  },
  methods: {
    getRecordOptions() {
      ConsultationService.getSymptomTreeV4().then(res => {
        this.complaints = res.chief_complaint;
        this.present_desc = res.present_desc;
      }).finally(() => {
      });
    },
    getAssistOptions() {
      ConsultationService.getAssistantOptions().then(res => {
        this.healthMap = res.health_map;
        this.pastHistory = res.past_history;
      });
    },
    close() {
      this.$refs.inputDrawer.close()
    },
    open() {
      this.$refs.inputDrawer.open()
      this.inputValue = this.recordItem.value || '';
    },
    onVisibleChange(v) {
      if (!v) {
        this.$emit("changeVisible", false);
      }
    },
    saveHandler() {
      // this.$emit("saveRecordItem", this.inputValue, this.recordItem.propKey);
      this.close();
    },
    emitRecordItem() {
      this.$emit("saveRecordItem", this.inputValue, this.recordItem.propKey);
    },
    handleSelect(value) {
      const splitKeywords = this.inputValue.split('，');
      if (!splitKeywords.includes(value)) {
        const addVal = this.inputValue ? `，${value}` : value;
        this.inputValue += addVal;
      }
      // if (this.inputValue) {
      //   this.inputValue += `，${value}`
      // } else {
      //   this.inputValue = value
      // }
    },
    handleInput(value) {
      this.$refs.recordItem?.hideDayList();
    },
    handleDayClick(day) {
      this.inputValue += `${day}`
    }
  },
}
</script>
<style scoped lang="scss">
.popup-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .pop-header {
    @include flex($justify: space-between);
    padding: 36rpx 40rpx;
    box-shadow: $box-shadow-base;
    height: 120rpx;
    flex: none;

    .pop-title {
      font-weight: 600;
      font-size: 32rpx;
      color: #333333;
      line-height: 48rpx;
    }
  }

  .pop-content {
    flex: 1;
    min-height: 0;
    overflow: hidden;

    .chief-complaint {
      height: 100%;
    }
  }

  .pop-footer {
    flex: none;
    padding: 40rpx;
    border-top: 1px solid rgba(235, 237, 240, 0.8);
    display: flex;
    padding-bottom: 40rpx + constant(safe-area-inset-bottom);
    padding-bottom: 40rpx +env(safe-area-inset-bottom);
    flex-wrap: wrap;

    .footer-title {
      width: 100%;
      font-size: 24rpx;
      color: #999999;
      line-height: 32rpx;
      margin-bottom: 16rpx;
    }

    .pop-footer-btn {
      height: 36px;
      line-height: 36px;
      align-self: flex-end;
    }

    .pop-footer-input {
      margin-right: 24rpx;
      flex: 1;

      ::v-deep .uni-easyinput__content-textarea {
        min-height: 36px;
        max-height: 240rpx;

        .uni-textarea-textarea {
          line-height: 40rpx;
        }
      }
    }
  }
}
</style>
