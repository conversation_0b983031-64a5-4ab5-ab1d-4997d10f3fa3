<template>
<uni-popup ref="inputDialog" type="dialog">
  <uni-popup-dialog ref="inputClose" mode="line" title="上传检测单" value="对话框预置提示内容!" placeholder="请输入内容"
                    style="width: 1110rpx" @close="closeDialog" @confirm="submitDialog" confirm-text="确认创建" :before-close="true">

    <uni-forms ref="uniForm" :modelValue="formData" :rules="formRules" label-align="right" label-width="100">
      <uni-forms-item label="表单类型:">
        <view class="radio-box">
          <view class="radio-item" :class="{active: formData.type === item.id}" v-for="(item,index) in formTypeList" :key="index" @click="selectFormType(item)">{{item.desc}}</view>
        </view>
      </uni-forms-item>
      <uni-forms-item label="表单列表:">
        <view class="radio-box">
          <view class="radio-item" :class="{active: formData.key === item.id}" v-for="(item,index) in formList" :key="index" @click="selectFormKey(item)">{{item.desc}}</view>
        </view>
      </uni-forms-item>
      <uni-forms-item label="填写方式:">
        <view class="radio-box">
          <view class="radio-item" :class="{active: formData.method === item.id}" v-for="(item,index) in formMethodList" :key="index" @click="selectFormMethod(item)">{{item.desc}}</view>
        </view>
      </uni-forms-item>
    </uni-forms>
    <div class="dialog-footer">
      <button class="ui-btn-drawer-base ui-btn-drawer-default" type="default" @click="closeDialog">取消</button>
      <button class="ui-btn-drawer-base ui-btn-drawer-primary ui-btn-distance-l" type="primary" @click="submitDialog" >填写检测单</button>
    </div>
  </uni-popup-dialog>
</uni-popup>
</template>

<script>
import S from 'utils/utils'
import {
  getUid
} from 'utils/runtime'
import {
  debounce
} from 'lodash'
const initFormData = {
  type: '1',
  key: '',
  method: '1',
};
import qaService from "@/api/qa";

export default {
  name: 'editActivityModal',
  mixins: [],
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      default: '',
    },
  },

  data() {
    return {
      formData: {
        ...initFormData
      },
      clinicStaffList: [],
      formRules: { // 校验规则
        mobile: {
          rules: [{
            required: true,
            errorMessage: '姓名不能为空'
          }]
        },
        age: {
          rules: [{
            required: true,
            errorMessage: '年龄不能为空'
          }, {
            format: 'number',
            errorMessage: '年龄只能输入数字'
          }]
        },
        created_mid: {
          rules: [{
            required: true,
            errorMessage: '请选择建单人'
          }]
        }
      },
      patientList: [],
      configParams: {
        labelKey: "name",
        valueKey: "id",
      },
      formTypeList: [],
      formListDesc: [],
      formMethodList: [
        {
          id: '1',
          desc: '本机填写'
        },
        // {
        //   id: '2',
        //   desc: '扫码填写'
        // }
      ],
    };
  },

  computed: {
    formList(){
      return this.formListDesc.filter(item => item.type === this.formData.type)
    },
  },

  watch: {},

  created() {
    this.getQAOption()
  },

  mounted() {},

  methods: {
    cancel() {
      this.$emit('update:visible', false);
    },
    submitForm() {},
    openDialog() {
      this.$refs.inputDialog.open()
      this.formData.key = this.formList[0].id
    },
    closeDialog() {
      this.$refs.inputDialog.close()
      this.formData = {...initFormData}
    },

    submitDialog() {
      if(!this.formData.key){
        uni.showToast({
          icon: 'none',
          title: '请选择表单列表'
        })
        return
      }
      this.$emit('openDrawer',this.formData.key)
      this.closeDialog()
    },
    selectFormType(item){
      this.formData.type = item.id
      this.formData.key = this.formList[0].id
    },
    selectFormKey(item){
      this.formData.key = item.id
    },
    selectFormMethod(item){
      this.formData.method = item.id
    },
    getQAOption(){
      // 获取页面参数
      const currentParams = this.$store.getters['global/getQuery'] || {}
      let params = {
        mr_id:currentParams.mr_id,
      }
      if (!params.mr_id) return
      qaService.getQAOption(params).then(res => {
        this.formTypeList = S.descToArrHandle(res.from_type_desc)
        this.formListDesc = S.descToArrHandle(res.from_list_desc)
      })
    },
  },


  destroyed() {},
};
</script>

<style scoped lang="scss">
::v-deep .uni-dialog-title {
  padding: 24rpx 30rpx;
  border-bottom: 1px solid #EBEDF0;
  justify-content: flex-start;

  .uni-dialog-title-text {
    color: #333333;
    font-weight: 600;
    // font-size: 24rpx;
  }
}

::v-deep .uni-dialog-content {
  display: block;
}

::v-deep .uni-forms-item__content {
  width: 360rpx;
}

::v-deep .uni-dialog-button-group {
  display: none;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.mr10 {
  margin-right: 10px !important;
}

.radio-box{
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  .radio-item{
    padding: 14rpx 40rpx;
    border: 2rpx solid #DCDFE6;
    border-radius: 8rpx;
    color: #444444;
    cursor: pointer;
    margin-right: 24rpx;
    margin-bottom: 24rpx;
  }
  .active{
    border: 2rpx solid #155BD4;
    color: #155BD4;
  }
}
</style>
