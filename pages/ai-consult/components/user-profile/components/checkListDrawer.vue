<template>
<view>
  <cus-drawer v-model="drawerVisible" title="填写检测单" @on-open="onOpen" @on-close="onClose" :loading="confirmLoading">
    <view slot="content" class="content">
      <view class="qa-container" v-if="drawerVisible">
        <view class="title-box">
          <view class="m-title">{{ questionTitle }}</view>
          <view class="f-title">共{{ questionsLength }}题</view>
        </view>
        <view class="topic-box">
          <view v-for="(first_item, first_index) in questions" :key="first_index + 'question'">
            <view class="first-title" v-if="first_item.category">
              <view>{{first_item.category}}</view>
              <view class="line"></view>
            </view>
            <view v-for="(second_item, second_index) in first_item.items" :key="second_index + 'item'">
              <view v-if="second_item.title" class="second-title">{{second_item.title}}</view>
              <view class="topic-item" v-for="(item, idx) in second_item.questions" :key="item.id">
                <view class="topic-title">
                  <text class="required" v-if="item.required === '1'">*</text>
                  <text>{{ idx + 1 }}、</text>
                  <text class="title">{{ item.title }}</text>
                  <text class="topic-type">【{{ qaTypeDesc[item.type] }}】</text>
                </view>
                <view class="qa-tips" v-if="item.tips">{{ item.tips }}</view>
                <!-- 单选题 -->
                <view class="topic-content topic-radio" v-if="item.type === '1'">
                  <view class="qa-radio">
                    <uni-data-checkbox v-model="item.answer" :localdata="item.options" :disabled="isDisabled"></uni-data-checkbox>
                  </view>
                </view>

                <!-- 多选题 -->
                <view class="topic-content topic-checkbox" v-else-if="item.type === '2'">
                  <checkbox-group class="checkGroup">
                    <uni-data-checkbox multiple v-model="item.answer" :localdata="item.options" :disabled="isDisabled"></uni-data-checkbox>
                  </checkbox-group>
                </view>

                <!-- 问答题 -->
                <view class="topic-content topic-answer" v-else-if="item.type === '3'">
                  <uni-easyinput v-model="item.answer" type="textarea" class="answer-textarea" placeholder="请输入内容" :disabled="isDisabled" />
                </view>

                <!-- 图片题 -->
                <view class="topic-content topic-img" v-else-if="item.type === '4'">
                  <upload :maxCount="3" v-model="item.answer" :disabled="isDisabled"></upload>
                </view>
              </view>
            </view>
          </view>




        </view>
      </view>
    </view>

    <view slot="footer" class="footer" >
      <view class="btn-group" v-if="!isDisabled">
        <!--      <button type="default"  @click="closeDrawer">取消</button>-->
        <button class="ml20" type="primary" :loading="confirmLoading" @click="confirm">保存并上传</button>
      </view>
    </view>
  </cus-drawer>
  <uni-popup ref="tipDialog" type="dialog">
    <uni-popup-dialog ref="inputClose" mode="input" confirm-text="不写了，确认退出" cancel-text="我再想想"
                      @confirm="handleConfirm" class="doubleCheckDialog">
      <view style="font-size: 28rpx;text-align: center;margin: 20rpx 0;color: #333">你好像什么都没写，是否退出本次填写</view>
    </uni-popup-dialog>
  </uni-popup>
</view>
</template>

<script>
  import cusDrawer from '@/components/cus-drawer/index.vue'
  import upload from '@/components/upload/index.vue'
  import qaService from "@/api/qa";

  export default {
    name: 'checkListDrawer',
    components: {
      cusDrawer,
      upload
    },
    mixins: [],
    props: {
      value: {
        type: Boolean,
        default: () => false
      },
      checkListKey: {
        type: String,
        default: ''
      },
      mr_id: {
        type: String,
        default: ''
      },
      isDetail: {
        type: Boolean,
        default: false
      },
      isDisabled: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        confirmLoading: false,
        drawerVisible: false,
        questionTitle: '',
        questionsLength: 0,
        questions: [],

        qaTypeDesc: {
          1: '单选题',
          2: '多选题',
          3: '问答题',
          4: '图片题',
        },
      };
    },
    computed: {},
    watch: {
      value(val) {
        this.drawerVisible = !!val;
      },
    },
    created() {},
    mounted() {},
    methods: {
      confirm() {
        // let flag = this.checkRequireQuestion(this.questions)
        // if(!flag){
        //   uni.showToast({
        //     icon: "none",
        //     title:'请填写必填项'
        //   })
        //   return
        // }
        if(this.checkEmpty(this.questions)){
          this.$refs.tipDialog.open()
          return
        }
        this.commitQAContent()
      },
      onOpen() {
        if(this.isDetail){
          this.getQADetail()
        }else{
          this.getQAContent()
        }

      },
      onClose() {
        this.closeDrawer()
      },
      closeDrawer() {
        this.$emit('input', false)
        this.clearData()
      },
      clearData() {},
      // 处理单选题选择逻辑
      handleRadioSelect(item, answer) {
        item.options.forEach((opt) => {
          opt.selected = false;
        });
        answer.selected = true;
      },
      // 处理多选题选择逻辑
      handleCheckboxSelect(item, answer) {
        answer.selected = !answer.selected;
      },
      // 处理图片上传变化
      handleImageChange(item) {
      },
      getQAContent() {
        // 获取页面参数
        const currentParams = this.$store.getters['global/getQuery'] || {}
        let params = {
          mr_id:currentParams.mr_id,
          key: this.checkListKey
        }
        qaService.getQAContent(params).then(res => {
          this.questionTitle = res.title
          this.questionsLength = res.num
          res.content.map(first_item => {
            first_item.items.forEach(second_item => {
              second_item.questions.forEach(third_item => {
                if(third_item.type === '1' || third_item.type === '3'){
                  third_item.answer = ''
                }else{
                  third_item.answer = []
                }

                third_item.options.forEach(option_item => {
                  option_item.text = option_item.value
                })
              })
            })
          })
          this.questions = res.content
        })
      },
      commitQAContent(){
        this.confirmLoading = true
        let params = {
          mr_id: this.mr_id,
          questionnaire_key: this.checkListKey,
          questionnaire_details: this.handleQuestionParams(this.questions)
        }
        qaService.commitQAContent(params).then(res => {
          uni.showToast({
            title: '上传成功'
          })
          this.confirmLoading = false
          this.closeDrawer()
          let patientRecord = {
            check_list_title: this.questionTitle,
            check_list_key: this.checkListKey,
            ...res
          }

          this.$emit('success',patientRecord)
        }).catch(() => {
          this.confirmLoading = false
        })
      },
      handleQuestionParams(questions){
        let commitAnswer = []
        questions.forEach(first_item => {
          first_item.items.forEach(second_item => {
            let qa = second_item.questions.map(question_item => {
              return {
                id: question_item.id,
                options: question_item.type === '1' || question_item.type === '2' ? question_item.answer : '',
                answer: question_item.type === '3' || question_item.type === '4' ? question_item.answer : '',
              }
            })
            let answer = {
              id: second_item.id,
              qa: qa
            }
            commitAnswer.push(answer)
          })
        })
        return commitAnswer
      },
      getQADetail(){
        let params = {
          mr_id: this.mr_id
        }
        qaService.getQADetail(params).then(res => {
          this.questionTitle = res.title
          this.questionsLength = res.num
          res.content.map(first_item => {
            first_item.items.forEach(second_item => {
              second_item.questions.forEach(third_item => {
                // if(third_item.type === '1' || third_item.type === '3'){
                //   third_item.answer = ''
                // }else{
                //   third_item.answer = []
                // }

                third_item.options.forEach(option_item => {
                  option_item.text = option_item.value
                })
              })
            })
          })
          this.questions = res.content
        })
      },
      // 检查必填项是否填写
      checkRequireQuestion(questions) {
        let flag = true;

        questions.forEach(first_item => {
          // 使用every方法检查所有second_item
          first_item.items.forEach(second_item => {
            // 检查每个second_item的questions
            const allQuestionsFilled = second_item.questions.every(third_item => {
              if (third_item.required === '1') {
                if (third_item.type === '1' || third_item.type === '3') {
                  return third_item.answer !== '';
                } else {
                  return third_item.answer.length > 0;
                }
              }
              // 如果不是必填项，返回true
              return true;
            });

            // 如果有一个second_item未填写，flag设为false
            if (!allQuestionsFilled) {
              flag = false;
            }
          });
        });

        return flag;
      },
      // 检查是否全部未填写
      checkEmpty(questions) {
        let flag = true;

        questions.forEach(first_item => {
          // 使用every方法检查所有second_item
          first_item.items.forEach(second_item => {
            // 检查每个second_item的questions
            const allQuestionsFilled = second_item.questions.every(third_item => {
              if (third_item.type === '1' || third_item.type === '3') {
                return third_item.answer === '';
              } else {
                return third_item.answer.length === 0;
              }
            });

            // 如果有一个second_item未填写，flag设为false
            if (!allQuestionsFilled) {
              flag = false;
            }
          });
        });

        return flag;
      },
      handleConfirm(){
        this.closeDrawer()
      }
    },

  };
</script>

<style lang="scss" scoped>
  .content {}

  .footer {
    .btn-group {
      display: flex;
    }
  }

  .ml20 {
    margin-left: 20rpx;
  }

  .qa-container {
    padding: 30rpx 0 180rpx;
    margin-top: -30rpx;
    font-size: 24rpx;
  }

  .title-box {
    padding: 30rpx 45rpx 16rpx;
    background: #ffffff;
    border-radius: 16rpx;
    text-align: center;
    margin-bottom: 20rpx;
  }

  .m-title {
    font-weight: 600;
    font-size: 36rpx;
    color: #262626;
    margin-bottom: 16rpx;
  }

  .f-title {
    font-size: 24rpx;
    line-height: 30rpx;
    font-weight: 400;
    color: #999999;
  }

  .topic-box {
    .topic-item {
      background: #ffffff;
      padding: 40rpx 53rpx;
    }

    .topic-title {
      position: relative;
      line-height: 42rpx;
      font-size: 28rpx;
    }

    .required {
      position: absolute;
      color: #f03232;
      left: -16rpx;
    }

    .topic-type {
      font-weight: 400;
      font-size: 24rpx;
      color: #AAAAAA;
      margin: 0 8rpx;
    }

    .title {
      font-size: 28rpx;
      font-weight: 500;
      color: #333333;
    }

    .qa-tips {
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      margin-top: 10rpx;
      margin-bottom: 32rpx;
    }
  }

  .qa-radio {
    display: flex;
  }

  .radio-item {
    margin-bottom: 20rpx;
    padding: 20rpx;
    border-radius: 8rpx;
    //background: #f7f8fa;
  }

  .radio-icon {
    position: relative;
    width: 32rpx;
    height: 32rpx;
    border-radius: 50%;
    border: 1px solid #999999;
    margin-right: 10rpx;
  }

  .radio-item--active .radio-icon {
    border-color: #155BD4;

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 16rpx;
      height: 16rpx;
      border-radius: 50%;
      background-color: #155BD4;
    }
  }

  .img-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24rpx;
    color: #c6c6c6;
    width: 140rpx;
    height: 140rpx;
    border: 1px dashed #e5e5e5;
    border-radius: 2rpx;
  }

  .answer-textarea {
    //::v-deep .uni-easyinput__content{
    //  background-color: #F9F9FA !important;
    //}
  }
  .uni-list-cell{
    display: flex;
    align-items: center;
    margin-right: 20rpx;
  }
  .checkGroup{
    display: flex;
    flex-wrap: wrap;
  }
  .first-title{
    position: relative;
    background: #F4F8FD;
    padding: 24rpx 40rpx;
    font-weight: 500;
    font-size: 32rpx;
    color: #155BD4;
    margin-bottom: 48rpx;
    .line{
      position: absolute;
      top: 0;
      left: 0;
      width: 4rpx;
      height: 100%;
      background: #155BD4;
    }
  }

  .second-title{
    font-weight: 500;
    font-size: 32rpx;
    color: #262626;
    padding: 0 26px;
  }
  .doubleCheckDialog {
    ::v-deep .uni-dialog-title {
      display: none;
    }

    ::v-deep .uni-dialog-button-text {
      font-size: 28rpx;
    }
  }
</style>
