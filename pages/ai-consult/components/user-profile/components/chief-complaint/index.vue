<template>
  <view class="content-wrapper">
    <view class="chief-wrapper">
      <view class="chief-menus">
        <scroll-view scroll-y="true" style="height: 100%;">

          <view :class="['chief-menu', activeIndex === index? 'active' : '' ]"
                v-for="(item,index) in dataSource" :key="item.id"
                @click="handleMenuClick(index)">
            <text class="chief-menu-title">{{ item.name }}</text>
          </view>
        </scroll-view>
      </view>
      <view class="chief-content">
        <scroll-view ref="rightScroll" :scrollTop="scrollTop" @scroll="scroll" scroll-y="true" style="height: 100%;">
          <view id="top-to-view"></view>
          <view class="chief-item" v-for="(item,index) in getChildrenList" :key="'item'+index">
            <view class="chief-title">
              <u-divider
                :text="item.name"
                textColor="#999999"
                lineColor="#DDDDDD"
                dashed
                textPosition="left"
                :textSize="24"
              ></u-divider>
            </view>
            <div class="chief-child-box">
              <view class="chief-child-item"
                    v-for="(childItem,childIdx) in item.children"
                    :key="'childItem'+childIdx"
                    @click="handleItemClick(childItem)">
                {{ childItem.name }}
              </view>
            </div>
          </view>
        </scroll-view>
      </view>
    </view>
    <view class="chief-footer" v-if="showDayList">
      <view class="day-item"
            v-for="item in dayList"
            :key="item"
            @click="handleDayClick(item)">
        {{ item }}
      </view>
    </view>
  </view>

</template>
<script>
import ConsultationService from "@/api/consultation";

export default {
  name: "ChiefComplaint",
  props: {
    value: {
      type: String,
      default: ''
    },
    dataSource: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      dayList: ['偶尔', '1天', '2天', '3天', '4天', '5天', '6天', '7天', '一周', '两周', '三周', '1月', '2月', '3月', '半年', '1年', '2年'],
      activeIndex: 0,
      showDayList: false,
      scrollTop: 0,
      oldScrollTop: 0
    }
  },
  computed: {
    getChildrenList() {
      return this.dataSource[this.activeIndex]?.children || [];
    }
  },
  watch: {},
  created() {
  },
  methods: {
    handleItemClick(item) {
      // 获取当前输入框的值
      this.$emit('handleSelect', item.name);
      // 记录当前症状并显示天数列表
      this.showDayList = true;
    },
    handleDayClick(day) {
      // 获取当前输入框的值
      this.$emit('handlerDayClick', day);
      this.showDayList = false;
    },
    hideDayList() {
      if (!this.showDayList) return;
      this.showDayList = false;
    },
    handleMenuClick(index) {
      this.activeIndex = index;
      // 滚动到顶部
      this.goTop();
    },
    scroll: function(e) {
				this.oldScrollTop = e.detail.scrollTop
			},
    goTop: function(e) {
				// 解决view层不同步的问题
				this.scrollTop = this.oldScrollTop
				this.$nextTick(function() {
					this.scrollTop = 0
				});
			}
  },
}
</script>
<style scoped lang="scss">
.content-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.chief-wrapper {
  display: flex;
  margin-top: 2rpx;
  flex: 1;
  min-height: 0;
  overflow: hidden;

  .chief-menus {
    background: #FAFAFB;
    width: 192rpx;
    flex: none;
    overflow-y: auto;

    .chief-menu {
      height: 88rpx;
      text-align: center;
      @include flex($align: center, $justify: center);

      .chief-menu-title {
        font-weight: 500;
        font-size: 28rpx;
      }

      &.active {
        background: #FFFFFF;
        color: $color-primary;
        position: relative;

        &::before {
          content: "";
          position: absolute;
          top: 24rpx;
          left: 0;
          width: 6rpx;
          height: 40rpx;
          background: $color-primary;
        }
      }
    }
  }

  .chief-content {
    flex: 1;
    overflow-y: scroll;
    padding: 40rpx;
    min-height: 0;

    .chief-item {
      .chief-title {

      }

      .chief-child-box {
        display: flex;
        flex-wrap: wrap;
        gap: 32rpx 48rpx;
        margin-bottom: 8rpx;

        .chief-child-item {
          font-size: 28rpx;
          color: #333333;
          line-height: 40rpx;

          &:active {
            color: $color-primary;
          }
        }
      }
    }
  }
}

.chief-footer {
  display: flex;
  align-items: center;
  background: #EDF3FC;
  padding: 20rpx 40rpx;
  justify-content: space-between;
  height: 96rpx;
  flex: none;

  .day-item {
    font-size: 24rpx;
    color: #666666;
    line-height: 32rpx;
    padding: 6rpx;
    cursor: pointer;

    &:active {
      background: #F5F5F5;
      color: #115bd4;
    }
  }
}
</style>
