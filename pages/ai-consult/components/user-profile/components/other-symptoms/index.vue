<template>
  <view class="content-wrapper">
    <view class="diagnosis-wrapper">
      <view class="diagnosis-menus">
        <view :class="['diagnosis-menu', activeIndex === index? 'active' : '' ]" v-for="(item,index) in getFinalTree"
              :key="item.name + index"
              @click="handleMenuClick(index)">
          <text class="diagnosis-menu-title">{{ item.name }}</text>
        </view>
      </view>
      <view class="diagnosis-content">
        <view class="mask" v-if="loading && isIntelligentTab">
          <view class="loading-icon">
            <u-loading-icon size="32" color="#115bd4"></u-loading-icon>
          </view>
          <text class="loading-text">
            AI正在生成智能问诊...
          </text>
        </view>
        <template v-if="getChildrenList.length">
          <view class="intelligent-tips" v-if="isIntelligentTab">
            通过AI诊断分析，患者可能存在以下问题:
          </view>
          <view class="diagnosis-item" v-for="(item,index) in getChildrenList" :key="'item'+index">
            <view class="diagnosis-title">
              <u-divider
                  :text="item.name"
                  textColor="#999999"
                  lineColor="#DDDDDD"
                  dashed
                  textPosition="left"
                  :textSize="24"
              ></u-divider>
            </view>
            <div class="diagnosis-child-box">
              <view class="diagnosis-child-item"
                    v-for="(childItem,childIdx) in item.children"
                    :key="'childItem'+childIdx"
                    @click="handleItemClick(childItem)">
                {{ childItem.name }}
              </view>
            </div>
          </view>
        </template>
        <template v-else>
          <u-empty class="empty-box" mode="data" text="暂无相关症状" iconSize="260">
          </u-empty>
        </template>
        <view class="guide-btn" v-if="canGuide && isIntelligentTab">
          <view class="flex-1"></view>
          <button type="primary" plain @click="getAssistantWwq">继续引导</button>
        </view>
      </view>
    </view>
  </view>

</template>
<script>
import {cloneDeep} from 'lodash';
import ConsultationService from "@/api/consultation";
import util from "@/utils/utils";

export default {
  name: "OtherSymptoms",
  props: {
    value: {
      type: String,
      default: ''
    },
    dataSource: {
      type: Array,
      default: () => []
    },
    formData: {
      type: Object,
      default: () => ({})
    },
    // 判断来源，2:出诊表(展示智能问诊)
    source: {
      type: String,
      default: ''
    }
  },
  onMounted() {
    if (this.associatedFormText) {
      this.canGuide = true;
    }
  },
  data() {
    return {
      list: [],
      activeIndex: 0,
      showDayList: false,
      recommendSysTree: {
        name: '智能问诊',
        children: [],
      },
      canGuide: true,
      hasGetRecommend: false,
      loading: false,
    }
  },
  computed: {
    getChildrenList() {
      return this.getFinalTree[this.activeIndex]?.children || [];
    },
    getFinalTree() {
      if (this.source !== '2') {
        return this.dataSource.concat(this.recommendSysTree);
      }else{
        return this.dataSource
      }
    },
    isIntelligentTab() {
      return this.activeIndex === this.dataSource.length;
    },
    associatedFormText() {
      return this.formData.engrave_text + this.formData.diagnostic_methods + this.formData.present_medical_history;
    },
  },
  watch: {
    associatedFormText: {
      handler(val, old) {
        if (val && val !== old) {
          this.canGuide = true;
        } else {
          this.canGuide = false;
        }
      },
    },
  },
  methods: {
    handleMenuClick(index) {
      this.activeIndex = index;
      if (index === this.dataSource.length && !this.hasGetRecommend && this.canGuide) {
        this.getAssistantWwq();
      }
    },
    getAssistantWwq() {
      this.loading = true;
      const query = this.$store.getters['global/getQuery'] || {}
      const params = {
        mr_id: query.mr_id,
        chief_complaint: this.formData.engrave_text,
        present_desc: this.formData.diagnostic_methods,
        present_illness: this.formData.present_medical_history,
        past_medical_history: this.formData.past,
        physical_examination: this.formData.physique,
      };
      ConsultationService.getIntelligentConsultation(params)
          .then(res => {
            this.recommendSysTree = res[0];
            this.hasGetRecommend = true;
          })
          .finally(() => {
            this.loading = false;
            this.canGuide = false;
          });
    },
    handleItemClick(item) {
      // 获取当前输入框的值
      this.$emit('handleSelect', item.name);
      // 记录当前症状并显示天数列表
      this.showDayList = true;
    },
    handleDayClick(day) {
      // 获取当前输入框的值
      this.$emit('handlerDayClick', day);
      this.showDayList = false;
    },
    hideDayList() {
      if (!this.showDayList) return;
      this.showDayList = false;
    }
  },
}
</script>
<style scoped lang="scss">
.content-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.diagnosis-wrapper {
  display: flex;
  margin-top: 2rpx;
  flex: 1;
  min-height: 0;
  overflow: hidden;

  .diagnosis-menus {
    background: #FAFAFB;
    width: 192rpx;
    flex: none;
    overflow-y: auto;

    .diagnosis-menu {
      height: 88rpx;
      text-align: center;
      @include flex($align: center, $justify: center);

      .diagnosis-menu-title {
        font-weight: 500;
        font-size: 28rpx;
      }

      &.active {
        background: #FFFFFF;
        color: $color-primary;
        position: relative;

        &::before {
          content: "";
          position: absolute;
          top: 24rpx;
          left: 0;
          width: 6rpx;
          height: 40rpx;
          background: $color-primary;
        }
      }
    }
  }

  .diagnosis-content {
    flex: 1;
    overflow-y: auto;
    padding: 40rpx;
    min-height: 0;
    position: relative;

    .mask {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: hsla(0, 0%, 100%, .9);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: #FFFFFF;
      font-size: 28rpx;
      font-weight: 500;
      z-index: 999;

      .loading-text {
        color: #115bd4;
        margin-top: 20rpx;
      }
    }

    .intelligent-tips {
      font-size: 24rpx;
      color: #999999;
      line-height: 32rpx;
      margin-bottom: 40rpx;
    }

    .diagnosis-item {

      .diagnosis-child-box {
        display: flex;
        flex-wrap: wrap;
        gap: 32rpx 48rpx;
        margin-bottom: 8rpx;

        .diagnosis-child-item {
          font-size: 28rpx;
          color: #333333;
          line-height: 40rpx;

          &:active {
            color: $color-primary;
          }
        }
      }
    }

    .guide-btn {
      display: flex;
      justify-content: space-between;
      flex: 1;
      text-align: right;
      position: absolute;
      bottom: 40rpx;
      right: 40rpx;
    }
  }
}
.empty-box{
  height: 80%;
  width: 100%;
  ::v-deep.u-empty__text{
    font-size: 28rpx!important;
    color: #999999;
  }
}
</style>
