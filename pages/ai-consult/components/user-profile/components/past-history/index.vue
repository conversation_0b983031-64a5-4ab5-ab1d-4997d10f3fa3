<template>
  <view class="past-wrapper">
    <view class="past-box" v-for="(item, index) in dataSource" :key="'past'+index">
      <u-divider
          v-if="item.name"
          :text="item.name"
          textColor="#999999"
          lineColor="#DDDDDD"
          dashed
          textPosition="left"
          :textSize="24"
      ></u-divider>
      <view class="past-item">
        <view class="past-children" @click.stop="handleSelect(child)" v-for="(child, index) in item.children" :key="'child'+index">{{child}}</view>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  name: "PastHistory",
  props: {
    dataSource: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {}
  },
  methods: {
    handleSelect(item) {
      this.$emit("handleSelect", item)
    }
  },

}
</script>

<style scoped lang="scss">
.past-wrapper{
  padding: 40rpx;
  .past-box{
    margin-bottom: 40rpx;
    .past-item{
      display: flex;
      flex-wrap: wrap;
      gap: 32rpx 48rpx;
      .past-children{
        font-size: 28rpx;
        color: #333333;
        line-height: 40rpx;
        &:active{
          color: $color-primary;
        }
      }
    }
  }
}
</style>
