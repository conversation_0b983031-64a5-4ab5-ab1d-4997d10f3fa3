<template>
  <view class="physical-wrapper">
    <view class="physical-menus">
      <view :class="['physical-menu', activeIndex === index? 'active' : '' ]"
            v-for="(item,index) in dataSource"
            :key="item.name"
            @click="activeIndex = index">
        <text class="physical-menu-title">{{ item.name }}</text>
      </view>
    </view>
    <view class="physical-content">
      <view class="physical-unit" v-if="getData.unit">{{ getData.unit }}</view>
      <view class="physical-tabs" v-if="isMultiChildren">
        <view
          v-for="(item, index) in getData.tabs"
          :key="index"
          :class="['physical-tab', { active: activeChildIndex === index }]"
          @click="handleTabClick(index)"
        >
          <view class="physical-tab-title">{{ item.name }}</view>
        </view>
      </view>
      <view class="physical-box">
        <view class="physical-item" v-for="(item,index) in getData.children" :key="index"
              @click="handleItemClick(item)">
          <view class="physical-item-title">{{ item }}</view>
        </view>
      </view>

    </view>
  </view>
</template>
<script>

export default {
  name: "PhysicalExamination",
  props: {
    dataSource: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      list: [],
      activeIndex: 0,
      activeChildIndex: 0,
      tabs: [
        {name: '体格检查'},
        {name: '生命体征'}
      ]
    }
  },
  computed: {
    isMultiChildren() {
      if (!this.dataSource || !this.dataSource[this.activeIndex]) return false;
      const currentData = this.dataSource[this.activeIndex].children;
      return currentData && currentData[0] && typeof currentData[0] === 'object';
    },
    getData() {
      if (!this.dataSource || !this.dataSource[this.activeIndex]) return {};
      const currentData = this.dataSource[this.activeIndex];

      if (this.isMultiChildren) {
        const childData = currentData.children[this.activeChildIndex];
        return {
          children: childData ? childData.children : [],
          unit: currentData.unit,
          tabs: currentData.children,
          name: currentData.name
        }
      } else {
        return currentData;
      }
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    handleTabClick(index) {
      this.activeChildIndex = index;
    },
    handleItemClick(item) {
      let res = '';
      const parent = this.getData
      if (this.getData.name === '血压') {
        res = `${this.getData.tabs[this.activeChildIndex].name} ${item} ${this.getData.unit}`
      } else {
        res = parent.name === '体征' ? item : `${parent.name} ${item} ${parent.unit}`
      }
      this.$emit('handleSelect', res);
    }
  },
  watch: {
    // 只监听左侧菜单切换
    activeIndex: {
      handler(val) {
        this.activeChildIndex = 0; // 重置子tab的索引
      }
    }
  }
}
</script>
<style scoped lang="scss">
.physical-wrapper {
  display: flex;
  height: 100%;
  margin-top: 2rpx;

  .physical-menus {
    background: #FAFAFB;
    width: 192rpx;

    .physical-menu {
      height: 88rpx;
      text-align: center;
      @include flex($align: center, $justify: center);

      .physical-menu-title {
        font-weight: 500;
        font-size: 28rpx;
      }

      &.active {
        background: #FFFFFF;
        color: $color-primary;
        position: relative;

        &::before {
          content: "";
          position: absolute;
          top: 24rpx;
          left: 0;
          width: 6rpx;
          height: 40rpx;
          background: $color-primary;
        }
      }
    }
  }

  .physical-content {
    flex: 1;
    padding: 40rpx;

    .physical-unit {
      font-weight: 500;
      font-size: 48rpx;
      color: #E0E0E0;
      line-height: 64rpx;
      margin-bottom: 40rpx;
    }

    .physical-box {
      display: flex;
      flex-wrap: wrap;
      gap: 32rpx 48rpx;

      .physical-item {
        font-size: 28rpx;
        color: #333333;
        line-height: 40rpx;
        min-width: 60rpx;

        &:active {
          color: $color-primary;
        }
      }
    }

    .physical-tabs {
      display: flex;
      position: relative;
      margin-bottom: 40rpx;

      &::after {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        height: 2rpx;
        background: #EBEDF0;
      }

      .physical-tab {
        padding: 24rpx 40rpx;
        font-size: 28rpx;
        color: #666666;
        position: relative;
        cursor: pointer;

        &.active {
          color: $color-primary;
          font-weight: 500;

          &::after {
            content: '';
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            height: 2rpx;
            background: $color-primary;
            z-index: 10;
          }
        }
      }
    }
  }
}
</style>
