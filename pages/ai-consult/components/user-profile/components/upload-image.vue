<template>
    <cus-drawer v-model="drawerVisible" title="上传影像信息" @on-open="onOpen" @on-close="onClose" :loading="confirmLoading">
      <view slot="content">
        <scroll-view scroll-y scroll-x style="height: 100%">
            <view class="upload-item">
              <view class="item-title">舌面照片(1～3张)：</view>
              <upload :maxCount="3" v-model="tongue_on_images">
                <image class="custom-img" src="https://static.rsjxx.com/image/2025/0119/181921_38668.png" />
              </upload>
            </view>

            <view class="upload-item">
              <view class="item-title">舌底照片(1～3张)：</view>
              <upload :maxCount="3" v-model="tongue_lower_images">
                <image class="custom-img" src="https://static.rsjxx.com/image/2025/0119/182117_68180.png" />
              </upload>
            </view>

            <view class="upload-item">
              <view class="item-title">手、面等部位照片(1～9张)：</view>
              <upload :maxCount="9" v-model="position_images">
                <image class="custom-img" src="https://static.rsjxx.com/image/2025/0119/182205_13191.png" />
              </upload>
            </view>

            <view class="upload-item">
              <view class="item-title">附加照片(1～9张，医疗影像/处方/化验单等)：</view>
              <upload :maxCount="9" v-model="additional_images">
                <image class="custom-img" src="https://static.rsjxx.com/image/2025/0119/182117_12298.png" />
              </upload>
            </view>
          </scroll-view>
      </view>

      <view slot="footer">
        <view><button class="ui-btn-drawer-base ui-btn-drawer-primary" type="primary" :loading="confirmLoading" @click="confirm">确认上传</button></view>
      </view>
    </cus-drawer>
</template>

<script>
import cusDrawer from '@/components/cus-drawer/index.vue'
import upload from '@/components/upload/index.vue'
import ConsultationService from "@/api/consultation";

export default {
  name: 'UploadImage',
  components: {
    cusDrawer,
    upload
  },
  mixins: [],
  props: {
    value: {
      type: Boolean,
      default: () => false
    },
    mr_id: {
      type: [String, Number],
      default: ''
    },
    tongue_image: {
      type: Object,
      default: () => {}
    },
  },
  data() {
    return {
      drawerVisible: false,
      confirmLoading: false,
      tongue_on_images: [], // 舌面照片
      tongue_lower_images: [], // 舌底照片
      position_images: [], // 手、面等部位照片
      additional_images: [], // 附加照片，化验单等
    };
  },
  computed: {},
  watch: {
    value (val) {
      if ( val ) {
        this.drawerVisible = true
      }else{
        this.drawerVisible = false
      }
    },
  },
  created() {},
  mounted() {},
  methods: {
    init (res) {
      // 获取图片
      this.tongue_on_images = res.tongue_on_images;
      this.tongue_lower_images = res.tongue_lower_images;
      this.position_images = res.position_images;
      this.additional_images = res.additional_images;
    },
    confirm () {
      if ( this.validField() ) {
        this.uploadRecordSaveimages()
      }
    },
    validField () {
      if (this.tongue_on_images.length == 0 && this.tongue_lower_images.length == 0 && this.position_images.length == 0 && this.additional_images.length == 0 ) {
        uni.showToast({
          title: '请先上传影像信息',
          icon: 'none'
        });
        return false
      }
      return true
    },
    onOpen () {
      if ( Object.keys(this.tongue_image)?.length > 0 ) {
        this.init(this.tongue_image)
      }
    },
    onClose () {
      this.closeDrawer()
    },
    closeDrawer () {
      this.$emit('input', false)
    },
    clearData () {},

    uploadRecordSaveimages () {
      this.confirmLoading = true
      let params = {
        images: {
          tongue_on_images: this.tongue_on_images,
          tongue_lower_images: this.tongue_lower_images,
          additional_images: this.additional_images,
          position_images: this.position_images,
        },
        mr_id: this.mr_id
      }
      if (!this.mr_id) return
      ConsultationService.uploadRecordSaveimages(params).then( res => {
        this.confirmLoading = false
        this.$emit('success')
        this.closeDrawer()
      }).finally(() => this.confirmLoading = false)
    },
  },
};
</script>

<style scoped lang="scss">
.upload-item {
  margin-bottom: 10rpx;
  .item-title {
    font-weight: 400;
    font-size: 28rpx;
    color: #333333;
    line-height: 40rpx;
    margin-bottom: 24rpx;
  }
}
.custom-img {
  width: 160rpx;
  height: 160rpx;
}
</style>

