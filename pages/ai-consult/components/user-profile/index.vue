<template>
  <view class="profile-wrapper">
    <view class="profile-header">
      <view class="profile-title">用户档案</view>
      <view class="profile-action  flex-gap-large" v-if="!isReadOnly">
        <view class="link-primary flex" @click="uploadTongue">
          <u-loading-icon v-if="imgLoading"></u-loading-icon>
          <image v-else class="link-icon" src="/static/image/main/profile/tongue-icon.png"></image>
          上传舌像信息
        </view>
        <view class="link-primary" @click="uploadChecklist">
          <image class="link-icon" src="/static/image/main/profile/checklist-icon.png"></image>
          上传检查单
        </view>
        <button type="warn" color="#F99C3F" class="status-btn" :disabled="fetchLoading||!can_copilot || (!updateAiParams && hasFetchAI)"
          :loading="fetchLoading" @click="getAiStreamInfo(true)">
          {{ getAIStatusText.btnText }}
        </button>
      </view>
    </view>
    <view class="profile-content">
      <uni-forms class="profile-form" label-position="left" label-align="right" :label-width="85">
        <uni-forms-item label="影像信息：" style="overflow: hidden">
          <view class="image-info" style="height: auto;">
            <!-- 患者图片 -->
            <patientPhoto :tongue_image="tongue_image" :mr_id="mr_id" :isCanDelete="isReadOnly ? false : true" @deleteChange="deleteChange"></patientPhoto>
          </view>
        </uni-forms-item>
        <uni-forms-item label="检查单：">
          <view class="check-card" @click="showCheckListDetail" v-if="patientRecord.check_list_title">
            <image class="check-card-icon" src="/static/image/main/profile/check-card.png"></image>
            <view>{{ patientRecord.check_list_title }}</view>
          </view>
          <view v-else style="margin-top: 14rpx">-</view>
        </uni-forms-item>
        <uni-forms-item :label="item.label + '：'" :required="item.required" v-for="item in recordItems"
          :key="item.propKey">
          <uni-easyinput v-model="patientRecord[item.propKey]" type="textarea" auto-height v-if="item.isTextarea"
            class="record-textarea" :disabled="isReadOnly" :placeholder="item.placeholder"></uni-easyinput>
          <view :class="['cus-input', isReadOnly ? 'cus-input--disabled' : '']" @click="recordItemHandler(item)" v-else>
            <text class="input-value" v-if="patientRecord[item.propKey]">
              {{ patientRecord[item.propKey] }}
            </text>
            <text class="cus-input-placeholder" v-else>{{ item.placeholder }}</text>
          </view>
        </uni-forms-item>
      </uni-forms>
    </view>
    <view class="xxx"></view>
    <record-input-drawer v-model="inputDrawerVisible" :form-data="patientRecord" :record-item="editRecordItem"
      @saveRecordItem="saveRecordItem"></record-input-drawer>
    <upload-image v-model="uploadVisible" :mr_id="mr_id" :tongue_image="tongue_image"
      @success="uploadSuccess"></upload-image>
    <check-list-dialog ref="checkDialog" @openDrawer="openCheckListDrawer"></check-list-dialog>
    <check-list-drawer v-model="checkVisible" :check-list-key="checkListKey" :mr_id="mr_id"
      :is-detail="isGetCheckListDetail" :is-disabled="isReadOnly" @success="checkListCommitSuccess"></check-list-drawer>
  </view>
</template>

<script>
import RecordInputDrawer
  from "@/pages/ai-consult/components/user-profile/components/RecordInputDrawer.vue";
import uploadImage from "@/pages/ai-consult/components/user-profile/components/upload-image.vue";
import patientPhoto from '@/components/patient-photo/index.vue'
import checkListDialog from './components/checkListDialog.vue'
import checkListDrawer from "./components/checkListDrawer.vue";
import { cloneDeep, debounce } from "lodash";
import ConsultationService from "@/api/consultation";
export default {
  name: 'UserProfile',
  components: {
    RecordInputDrawer,
    uploadImage,
    patientPhoto,
    checkListDialog,
    checkListDrawer
  },
  props: {
    userInfo: {
      type: Object,
      default: () => {
        return {
          name: '',
          age: 18
        }
      }
    },
    draftData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    isReadOnly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      patientRecord: {
        present_medical_history: '', //现病史
        engrave_text: '', //主诉
        diagnostic_methods: '', //望闻问切
        past: '', //既往史
        physique: '', // 体格检查
        check_list_title: '', // 检查单名称
        check_list_key: '', // 检查单类型
      },
      recordItems: [{
        propKey: 'engrave_text',
        label: '主诉',
        placeholder: '请输入主诉',
        required: true,
        showDayList: true,
        componentName: 'ChiefComplaint'
      },
      {
        propKey: 'diagnostic_methods',
        label: '其他症状',
        placeholder: '请输入其他症状',
        componentName: 'OtherSymptoms'
      },
      {
        propKey: 'present_medical_history',
        label: '现病史',
        placeholder: '请输入现病史',
        componentName: 'MedicalHistory',
        isTextarea: true
      },
      {
        propKey: 'past',
        label: '既往史',
        placeholder: '请输入既往史',
        componentName: 'PastHistory'
      },
      {
        propKey: 'physique',
        label: '体格检查',
        placeholder: '请输入体格检查',
        componentName: 'PhysicalExamination'
      }
      ],
      inputDrawerVisible: false,
      editRecordItem: {
        propKey: '',
        label: '',
        placeholder: '',
        required: false,
        showDayList: false
      },
      uploadVisible: false,
      //影像资料数据
      tongue_image: {
        tongue_on_images: [],
        tongue_lower_images: [],
        position_images: [],
        additional_images: [],
        tongue_diag_images: [],
        tongue_ai: [],
      },
      can_copilot: false,
      hasFetchAI: false,
      updateAiParams: false,
      fetchLoading: false,
      mr_id: '',
      checkVisible: false,
      checkListKey: '', // 填写检测单id
      isGetCheckListDetail: false,
      imgLoading: false,
    }
  },
  computed: {
    associatedFormText() {
      return this.patientRecord.engrave_text + this.patientRecord.diagnostic_methods + this.patientRecord.present_medical_history;
    },
    getAIStatusText() {
      if (!this.can_copilot) {
        return { statusText: '暂无诊断', btnText: 'AI智能诊断' };
      } else {
        if (this.fetchLoading) {
          return { statusText: '', btnText: '病历分析中...' };
        }
        if (this.hasFetchAI) {
          if (this.updateAiParams) {
            return { statusText: '患者症状已变更，可重新获取AI诊断报告', btnText: '重新诊断' };
          } else {
            return { statusText: 'AI诊断报告已生成', btnText: '诊断已生成' };
          }
        }
        return { statusText: '', btnText: 'AI智能诊断' };
      }
    },
  },

  mounted() {
    uni.$on('importAiRecordEvent', this.importRecord);
    uni.$on('getFetchAIStatus', this.updateAIStatus);
    uni.$on('handleQueryAiV4', this.handleQueryAiV4);
    // 获取页面参数
    const params = this.$store.getters['global/getQuery'] || {}
    this.mr_id = params.mr_id;
    if (this.mr_id) {
      this.getImages()
    }
  },
  beforeDestroy() {
    uni.$off('importRecordEvent');
    uni.$off('getFetchAIStatus');
    uni.$off('handleQueryAiV4');
  },
  watch: {
    draftData: {
      handler(val, oldVal) {
        if (val) {
          this.patientRecord = { ...this.patientRecord, ...val };
        }
      },
      deep: true,
    },
    associatedFormText: {
      handler(val, oldVal) {
        if (!val) {
          this.can_copilot = 0;
          this.updateAiParams = false;
          return;
        }
        if (val !== oldVal) {
          if (oldVal) {
            this.updateAiParams = true;
          }
          this.can_copilot = 1;
        }
        // this.getAiStreamInfo();
      },
      immediate: false,
    },
    patientRecord: {
      handler(val, oldVal) {
        this.$store.dispatch('consultation/saveDraft', {
          mr_id: this.mr_id,
          type: 'record_info',
          value: this.patientRecord
        })
      },
      deep: true,
    }
  },
  methods: {
    validateForm() {
      if (!this.patientRecord.engrave_text) {
        uni.showToast({
          title: '主诉内容不可为空，请填写用户本次咨询目的！',
          icon: 'none',
        })
        return false;
      }
      return true;
    },
    // 获取图片
    getImages() {
      return new Promise((resolve) => {
        let params = {
          mr_id: this.mr_id
        }
        if (!this.mr_id) {
          resolve()
          return
        }
        ConsultationService.getRecordImages(params).then(res => {
          this.$set(this.tongue_image, 'tongue_on_images', res.tongue_on_images)
          this.$set(this.tongue_image, 'tongue_lower_images', res.tongue_lower_images)
          this.$set(this.tongue_image, 'position_images', res.position_images)
          this.$set(this.tongue_image, 'additional_images', res.additional_images)
          this.$set(this.tongue_image, 'tongue_diag_images', res.tongue_diag_images)
          this.$set(this.tongue_image, 'tongue_ai', res.tongue_ai)
          resolve()
        }).finally(() => this.imgLoading = false)
      })
    },

    deleteChange: debounce(function (){
      this.getImages()
    }, 500,{
      leading: false,
      trailing: true,
    }),
    uploadSuccess() {
      this.getImages()
    },
    handleQueryAiV4() {
      this.getAiStreamInfo(true);
    },
    importRecord(data = {}) {
      if ('medical_case' in data) {
        for (const dataKey in data.medical_case) {
          data.medical_case[dataKey] && this.$set(this.patientRecord, dataKey, data.medical_case[dataKey]);
        }
        if (!data.isCopyAll) {
          // uni.showToast({
          //   title: '导入成功',
          //   icon: 'none',
          // });
        }
      }
    },
    // 获取ai诊断
    getAiStreamInfo: debounce(
      function (run_copilot) {
        if (!this.patientRecord.engrave_text) {
          uni.showToast({
            title: '主诉内容不可为空，请填写患者本次就诊目的！',
            icon: 'none',
          })
          return;
        }
        if (run_copilot) {
          this.fetchLoading = true;
        }
        const params = {
          mr_id: this.mr_id,
          chief_complaint: this.patientRecord.engrave_text,
          present_desc: this.patientRecord.diagnostic_methods,
          present_illness: this.patientRecord.present_medical_history,
          past_medical_history: this.patientRecord.past,
          physical_examination: this.patientRecord.physique,
          validation_can_copilot: '1',
          run_copilot: run_copilot ? '1' : '0',
        };
        if (!this.mr_id) return;
        ConsultationService.commitAssistantMedical(params)
          .then(res => {
            this.can_copilot = res?.can_copilot === '1';
            if (res?.can_copilot === '1' && run_copilot) {
              this.handleGetAiStream();
            } else {
              this.fetchLoading = false;
              this.updateAiParams = false;
              uni.showToast({
                title: '您输入的内容无法分析，请修改后重试！',
                icon: 'none',
              });
            }
          })
          .catch((e) => {
            this.fetchLoading = false;
          });
      },
      600,
      {
        leading: true,
        trailing: false,
      }
    ),
    handleGetAiStream() {
      this.updateAiParams = false;
      uni.$emit('handleUpdateAiAnswerInfo', {
        mr_id: this.mr_id,
        record_info: this.patientRecord,
      });
      uni.$emit('queryAiInfoEvent', true);
      uni.$emit('changeRightTabIndex', 2);
    },
    updateAIStatus(status) {
      this.hasFetchAI = status;
      this.fetchLoading = false;
    },
    updateFetchStatus(status) {
      this.fetchLoading = false;
      this.hasFetchAI = status !== 'stop';
    },
    saveRecordItem(value, propKey) {
      this.patientRecord[propKey] = value;
    },
    uploadTongue() {
      if (this.imgLoading) return
      this.imgLoading = true
      this.getImages().then( res => {
        this.uploadVisible = true
        this.imgLoading = false
      } )
    },
    uploadChecklist() {
      this.$refs.checkDialog.openDialog()
    },
    //   选择主诉
    recordItemHandler(item) {
      if (this.isReadOnly) return;
      this.editRecordItem = {
        ...item,
        value: this.patientRecord[item.propKey]
      }
      this.inputDrawerVisible = true;
    },

    openCheckListDrawer(key) {
      this.isGetCheckListDetail = false
      this.checkListKey = key
      this.checkVisible = true
    },
    checkListCommitSuccess(val) {
      this.patientRecord = val
    },
    showCheckListDetail() {
      this.isGetCheckListDetail = true
      this.checkListKey = this.patientRecord.check_list_key
      this.checkVisible = true
    },
  },
}
</script>

<style lang="scss" scoped>
.profile-wrapper {
  background: #FFFFFF;
  border-radius: 0 0 $border-radius-base $border-radius-base;

  .profile-header {
    @include flex($justify: space-between);
    padding: $spacing-base $spacing-large;
    box-shadow: 0 6rpx 12rpx 0 rgba(20, 21, 22, 0.04);

    .profile-title {
      font-weight: 600;
      font-size: 32rpx;
      color: #262626;
      line-height: 40rpx;
    }

    .profile-action {
      @include flex();

      .link-primary {
        @include flex();

        .link-icon {
          width: 28rpx;
          height: 28rpx;
        }
      }
      .status-btn {
        border-radius: $ui-border-radius-base;
        height: $ui-btn-base-height;
        box-sizing: border-box;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: 400;
        font-size: 28rpx;
        margin: 0;
      }
    }
  }

  .profile-content {
    padding: $spacing-large;

    .profile-form {
      .image-info {
        display: flex;
        align-items: center;
        height: 80rpx;
      }

      ::v-deep .uni-forms-item {
        margin-bottom: 16rpx;

        .uni-forms-item__label {
          font-size: 26rpx;
          padding-right: 8rpx;
        }
      }

      .check-card {
        background: #F5F6F8;
        border-radius: 8rpx;
        padding: 14rpx 20rpx;
        font-size: 28rpx;
        color: #333333;
        line-height: 40rpx;
        display: inline-flex;
        align-items: center;

        .check-card-icon {
          width: 52rpx;
          height: 52rpx;
          margin-right: 20rpx;
        }
      }

      ::v-deep .record-textarea {
        .is-disabled {
          background-color: #F5F6F8 !important;
          color: rgb(213, 213, 213);

          .uni-textarea-placeholder {
            color: rgb(213, 213, 213);
            line-height: 44rpx;
          }
        }
      }

      .cus-input {
        display: flex;
        align-items: center;
        padding: 10rpx 24rpx;
        border: 1px solid #E5E5E5;
        border-radius: 8rpx;
        font-size: 26rpx;
        line-height: 44rpx;

        &:not(.cus-input--disabled):active {
          border-color: #115bd4;
          background: #F5F6F8;
        }

        .cus-input-placeholder {
          color: #999999;
        }

        .input-value {
          color: #333333;
        }

        &--disabled {
          background: #F5F6F8;

          .input-value,
          .cus-input-placeholder {
            color: rgb(213, 213, 213)
          }
        }
      }
    }
  }
}

::v-deep .uni-easyinput__content {
  box-sizing: border-box;

  .uni-easyinput__content-textarea {
    min-height: 0;
  }

  &.is-focused {
    border-color: #115bd4 !important;
  }

  .uni-easyinput__placeholder-class {
    color: #999999;
  }
}

::v-deep .uni-forms-item__content {
  width: calc(100% - 170rpx);
}
</style>
