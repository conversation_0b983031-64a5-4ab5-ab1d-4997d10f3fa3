<template>
  <view class="page">
    <view class="container" v-if="!pageLoading">
      <template v-if="mr_id">
        <!-- 中间内容区 -->
        <view class="middle-section">
          <Main ref="main"></Main>
        </view>

        <!-- 右侧栏 -->
        <view class="right-section">
          <template v-if="mr_id">
            <right-panel :patientInfo="patientInfo"></right-panel>
          </template>
          <template v-else>
            <view class="empty-box">
              <u-empty
                mode="data"
                text="暂无历史记录"
                iconSize="220"
                textSize="28"
              ></u-empty>
            </view>
          </template>
        </view>
      </template>
      <template v-else>
        <Empty class="flex-1"></Empty>
      </template>
    </view>
    <c-loading :show="pageLoading" />
  </view>
</template>

<script>
import Main from './components/Main.vue'
import Empty from "./components/Empty.vue";
import RightPanel from './components/RightPanel/RightPanel.vue';
import ConsultationService from "../../api/consultation";
import CLoading from "../../components/cLoading/loading.vue";

export default {
  name: 'Ai-Consult',
  components: {
    CLoading,
    Main,
    RightPanel,
    Empty
  },
  provide () {
    return {
      getVm: () => {
        return this.$refs.main
      }
    }
  },
  data() {
    return {
      patientInfo: {},
      mr_id: '',
      pageLoading: false,
    }
  },
  created() {
    this.pageLoading = true;
  },
  async mounted() {
    console.log('组件已重建', Date.now())
    // 获取页面参数
    const params = this.$store.getters['global/getQuery'] || {}
    if (params.mr_id) {
      this.mr_id = params.mr_id
      await this.getPatientInfoByMrId(params.mr_id)
    } else {
      this.pageLoading = false
    }
  },
  beforeDestroy() {
   this.$store.commit('consultation/CLEAR_ALL_LIST')
  },
  methods: {
    getPatientInfoByMrId(id) {
      return new Promise((resolve) => {
        if (!id) {
          this.pageLoading = false;
          resolve()
          return
        };
        this.pageLoading = true
        ConsultationService.getPatientInfoByMrId({
          mr_id: id,
        }).then((res) => {
          this.patientInfo = res?.info || {}
          this.$nextTick(() => {
            setTimeout(() => {
              this.$refs.main.init()
            }, 300)
          })
        }).finally(() => {
          this.pageLoading = false
          resolve()
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
}
.container {
  width: 100%;
  height: 100%;
  padding: $spacing-base;
  display: flex;
  overflow-y: hidden;
  .middle-section {
    flex: 1;
    overflow-y: auto;
  }

  .right-section {
    width: $right-section-width;
    padding-left: $spacing-base;
    overflow-y: hidden;
    flex-shrink: 0;
  }
}
</style>
