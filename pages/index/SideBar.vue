<template>
  <view class="sidebar-wrapper">
    <image class="side-logo" src="/static/image/main/rgj-logo.png"></image>
    <view class="side-menu-wrapper">
      <view
        :class="['side-menu', activeId === menu.id ? 'side-menu-active':'']"
        v-for="menu in menus"
        :key="menu.id"
        @click="routeJump(menu)"
      >
        <image :src=" activeId === menu.id ? menu.activedPath : menu.iconPath" class="menu-icon"></image>
        <view class="menu-title" :class="{ 'menu-title--active': activeId === menu.id }">{{ menu.name}}</view>
      </view>
    </view>
    <view class="logout-box" :style="{backgroundColor: getColorForName}" @click="logout">{{ getLastName }}
    </view>
    <view class="version-code">V{{ codeVersion }}</view>
    <logout-dialog ref="logoutDialog"></logout-dialog>
  </view>
</template>

<script>
import {getUserInfo} from '@/utils/runtime'
import logoutDialog from "@/pages/ai-consult/components/logout-dialog/index.vue";

export default {
  name: 'SideBar',
  components: {logoutDialog},
  props: {
    activeId: String,
    menus: Array,
  },
  model: {
    prop: 'activeId',
    event: 'changeActiveId'
  },
  computed: {
    getColorForName() {
      const name = this.userInfo.user_name;
      if (!name) return 'hsl(0, 0%, 50%)';

      // 简单哈希算法
      let hash = 0;
      for (let i = 0; i < name.length; i++) {
        hash = name.charCodeAt(i) + ((hash << 5) - hash);
      }

      // 使用哈希值生成HSL颜色
      const hue = Math.abs(hash) % 360;
      return `hsl(${hue}, 80%, 60%)`;
    },
    getLastName() {
      const name = this.userInfo.user_name;
      if (!name) return '';
      return name.slice(-2);
    }
  },
  data() {
    return {
      userInfo: getUserInfo(),
      codeVersion: uni.getAppBaseInfo()?.appVersion,
    }
  },
  methods: {
    routeJump({id}) {
      this.$emit('changeActiveId', id)
      // this.activeMenu = id
      // const pages = getCurrentPages();
      // const currentPage = pages[pages.length - 1];
      // const route = '/'+ currentPage.route
      // if ( route === path) return
      // console.log(path, 'path')
      // const userMrIds = getUserMrId();
      // if (userMrIds?.mr_id && path === userMrIds?.path) {
      //   uni.redirectTo({
      //     url: `${path}?mr_id=${userMrIds.mr_id}&pt_id=${userMrIds.pt_id}`
      //   })
      //   return;
      // }
      // uni.redirectTo({
      //   url: path
      // })
    },
    logout(){
      this.$refs.logoutDialog.open()
    },
  }
}
</script>

<style lang="scss" scoped>
.uni-left-window{
  height: 100%;
}
.sidebar-wrapper {
  position: relative;
  color: #fff;
  height: 100%;
  background: #0A163D;
  width: 120rpx;
  .side-logo {
    width: 108rpx;
    height: 48rpx;
    margin: 36rpx 6rpx 24rpx;
  }

  .side-menu-wrapper {

    .side-menu {
      height: 160rpx;
      width: 100%;
      @include flex(column);
      .menu-icon{
        width: 56rpx;
        height: 56rpx;
        margin-top: 32rpx;
        margin-bottom: 16rpx;
      }
      .menu-title{
        width: 84rpx;
        height: 32rpx;
        font-size: 28rpx;
        color: rgba(255, 255, 255, 0.75);
        line-height: 32rpx;
        text-align: center;
      }
      .menu-title--active {
        color: #FFFFFF;
      }
      &-active {
        background: #155BD4;
      }
    }

  }

  .logout-box{
    position: absolute;
    bottom: 69rpx;
    left: 24rpx;
    width: 72rpx;
    height: 72rpx;
    border-radius: 64rpx;
    //border: 1rpx solid #EBEDF0;
    //background-color: #ffffff;
    color: #fff;
    line-height: 70rpx;
    text-align:center;
    font-size: 26rpx;
  }

  .version-code{
    position: absolute;
    bottom: 24rpx;
    width: 120rpx;
    height: 32rpx;
    font-size: 24rpx;
    color: rgba(255,255,255,0.35);
    line-height: 32rpx;
    text-align: center;
  }


}
</style>
