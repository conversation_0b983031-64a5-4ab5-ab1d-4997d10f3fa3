<script>
  import SideBar from './SideBar.vue';
  import UserImg from '@/static/image/main/menu-icon/user.png'
  import UserActive from '@/static/image/main/menu-icon/user-active.png'
  import ProductImg from '@/static/image/main/menu-icon/product.png'
  import ProductActive from '@/static/image/main/menu-icon/product-active.png'
  import MenuAI from '@/static/image/main/menu-icon/menu-ai.png'
  import MenuAIActive from '@/static/image/main/menu-icon/menu-ai-active.png'
  import ReserveImg from '@/static/image/main/menu-icon/reserve.png'
  import ReserveActive from '@/static/image/main/menu-icon/reserve-active.png'
  import MeetingImg from '@/static/image/main/menu-icon/meeting.png'
  import MeetingActive from '@/static/image/main/menu-icon/meeting-active.png'

  import AiConsult from "@/pages/ai-consult/index.vue";
  import User from '@/pages/user/index.vue'
  import Product from '@/pages/product/index.vue'
  import Reserve from "@/pages/reserve/index.vue";
  import Meeting from "@/pages/meeting/index.vue"
  import { isDigitalStore } from '@/utils/runtime'


  export default {
    name: "MenuLayOut",
    components: {
      SideBar,
      AiConsult,
      User,
      Product,
      Reserve,
      Meeting
    },
    data() {
      return {
        activeId: '0',
        activeMenuKey: '3',
        menus: [{
            name: '用户',
            id: '1',
            iconPath: UserImg,
            activedPath: UserActive,
            componentName: 'User',
          },
          {
            name: '产品',
            id: '2',
            iconPath: ProductImg,
            activedPath: ProductActive,
            componentName: 'Product',
          },
          {
            name: 'AI咨询',
            id: '0',
            iconPath: MenuAI,
            activedPath: MenuAIActive,
            componentName: 'AiConsult',
          },
          {
            name: '预约',
            id: '3',
            iconPath: ReserveImg,
            activedPath: ReserveActive,
            componentName: 'Reserve',
          },
          {
            name: '日会',
            id: '4',
            iconPath: MeetingImg,
            activedPath: MeetingActive,
            componentName: 'Meeting',
          },
          // {name: '产品库', id: '1', path: '/pages/product/index', iconPath: MenuAI}
        ]
      }
    },
    computed: {
      // 根据数字化状态过滤菜单
      filteredMenus() {
        if (isDigitalStore()) {
          // 数字化门店显示所有菜单
          return this.menus;
        } else {
          // 非数字化门店只显示【用户】和【AI咨询】菜单
          return this.menus.filter(menu => menu.id === '1' || menu.id === '0');
        }
      },
      targetComponent() {
        return this.filteredMenus.find(menu => menu.id === this.activeId) || {};
      },
      // 添加计算属性获取query
      routeQuery() {
        return this.$store.getters['global/getQuery'];
      }
    },
    watch: {
      // 监听计算属性而不是直接监听state
      routeQuery: {
        immediate: true,
        deep: true,
        handler(query) {
          if (!query?.menuId) return
          console.log("=>(index/index.vue:55) Route参数，menuId: 跳转目标菜单Id", query);

          let targetMenuId = query.menuId;

          // 检查非数字化门店的菜单权限
          if (!isDigitalStore()) {
            const allowedMenuIds = ['1', '0']; // 用户和AI咨询
            if (!allowedMenuIds.includes(targetMenuId)) {
              console.log("非数字化门店，重定向到AI咨询菜单");
              targetMenuId = '0'; // 默认跳转到AI咨询
            }
          }

          this.activeId = ''
          this.activeMenuKey = ''
          setTimeout(() => {
            this.activeId = targetMenuId
            this.activeMenuKey = Date.now().toString() + 'menuId-' + targetMenuId
          }, 0)
        }
      }
    },
    mounted() {
      // 设置路由参数
      // this.$store.commit('global/SET_QUERY', query)
      // 获取路由参数
      // this.$store.getters['global/getQuery'];
    },
    deactivated () {
      console.log("=>(index.vue:24) console");
    }
  }
</script>

<template>
  <view class="page-container">
    <!-- 主内容区域 -->
    <view class="main-content">
      <!--      &lt;!&ndash; 左侧栏 &ndash;&gt;-->
      <view class="left-section">
        <side-bar v-model="activeId" :menus="filteredMenus"></side-bar>
      </view>
      <view :key="activeMenuKey + 'main'" v-if="activeId" class="main">
        <component :key="activeMenuKey" :is="targetComponent.componentName" />
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
  .page-container {
    width: 100vw;
    height: 100vh;
    background-color: $bg-color-page;
    overflow: hidden;

    .main-content {
      width: 100%;
      height: 100%;
      display: flex;
      overflow: hidden;

      .left-section {
        width: $side-bar-width;
        height: 100%;
        background-color: $bg-color-white;
        overflow-y: auto;
      }

      .main {
        flex: 1;
        height: 100%;
        overflow: hidden;
      }
    }
  }
</style>
