<template>
  <view class="login-container" v-if="!pageError">
    <!-- 添加加载状态 -->
    <view v-if="pageLoading" class="loading-overlay">
      <view class="loading-content">
        <text>加载中...</text>
      </view>
    </view>

    <!-- Logo -->
    <view class="header">
      <view class="logo-box">
        <image class="logo" src="/static/image/rsj-logo.png" mode="aspectFit"></image>
      </view>
      <text class="title">移动管理平台</text>
    </view>
    <view class="login-wrapper">
      <view class="qr-confirm-box" v-if="qrLoginSuccess">
        <view class="user-avatar" :style="{ backgroundColor: getColorForName }"> {{ getFirstName }} </view>
        <view class="user-name">{{ userInfo.user_name }}</view>
        <button type="primary" style="width: 100%" @click="confirmLogin" :loading="loading">确认登录</button>
        <view class="link-primary" @click="cancelLogin">取消</view>
      </view>
      <view style="position: relative" class="login-area" v-else>
        <view @click="isScanLogin = !isScanLogin">
          <view class="mark"></view>
          <template v-if="isScanLogin">
            <image class="mobile-image" src="/static/image/login/login-mobile.png" mode="aspectFit"></image>
            <image class="auth-image" src="/static/image/login/auth-login.png" mode="aspectFit"></image>
          </template>
          <template v-else>
            <image class="mobile-image" src="/static/image/login/login-scan.png" mode="aspectFit"></image>
            <image class="auth-image" src="/static/image/login/scan-login.png" mode="aspectFit"></image>
          </template>
        </view>

        <view class="qr-box login-bx" v-if="isScanLogin">
          <view class="auth-title">扫码登录</view>
          <image src="/static/image/login/scan-bg.png" mode="aspectFit" class="scan-bg"></image>
          <button type="primary" class="scan-btn" @click="scanToLogin">扫描二维码</button>
        </view>
        <view class="mobile-box login-bx" v-else>
          <view class="auth-title">验证码登录</view>
          <uni-forms
            ref="loginForm"
            :model="formData"
            :rules="rules"
            validateTrigger="blur"
            class="login-forms"
            :label-width="0"
            style="width: 100%"
          >
            <uni-forms-item label="" name="mobile">
              <view class="input-box">
                <uni-easyinput
                  v-model="formData.mobile"
                  placeholder="请输入手机号"
                  trim
                  style="width: 100%"
                  @blur="getClinicList"
                  type="tel"
                  :maxlength="11"
                >
                  <view slot="left">
                    <image
                      src="https://static.rsjxx.com/image/2025/0714/153114_75094.png"
                      mode="aspectFit"
                      class="input-icon"
                    ></image>
                  </view>
                </uni-easyinput>
              </view>
            </uni-forms-item>
            <uni-forms-item label="" name="clinic">
              <view class="input-box">
                <uni-login-select
                  class="icon-select"
                  v-model="formData.clinic_id"
                  :localdata="clinicList"
                  placeholder="请选择诊所"
                  emptyTips="暂无数据"
                  style="width: 100%"
                >
                </uni-login-select>
                <image
                  src="https://static.rsjxx.com/image/2025/0714/153326_34099.png"
                  mode="aspectFit"
                  class="input-icon select-prefix"
                ></image>
              </view>
            </uni-forms-item>
            <uni-forms-item label="" name="auth_code">
              <view class="input-box auth-box">
                <uni-easyinput v-model="formData.auth_code" type="number" :maxlength="6" placeholder="请输入验证码">
                  <view slot="left">
                    <image
                      src="https://static.rsjxx.com/image/2025/0714/153326_22068.png"
                      mode="aspectFit"
                      class="input-icon"
                    ></image>
                  </view>
                  <view slot="right" class="flex" @click="getAuthCode">
                    <view class="divider"></view>
                    <view class="auth-btn" @click="getAuthCode">{{ getCountdownText }}</view>
                  </view>
                </uni-easyinput>
              </view>
            </uni-forms-item>
            <uni-forms-item>
              <button type="primary" class="login-btn" @click="loginHandler" :loading="loading">登录</button>
            </uni-forms-item>
          </uni-forms>
        </view>
      </view>
    </view>
    <!-- 扫码登录区域 -->
  </view>

  <!-- 错误状态 -->
  <view v-else class="error-container">
    <text>页面加载失败，请重试</text>
    <button @click="retryLoad">重新加载</button>
  </view>
</template>

<script>
import LoginService from '@/api/login';
import { writeLoginInfo } from '@/utils/runtime';
import uniLoginSelect from './components/uni-login-select.vue';
export default {
  components: { uniLoginSelect },
  data() {
    return {
      pageError: false, // 页面错误状态
      qrCode: '', // 二维码图片地址
      timer: null, // 轮询定时器
      countDown: 60, // 验证码倒计时
      hasCountdown: false, // 验证码倒计时
      isScanLogin: false, // 是否扫码登录
      screenHeight: null,
      formData: {
        mobile: '',
        auth_code: '',
        clinic_id: '',
      },
      rules: {
        mobile: {
          rules: [{ required: true, errorMessage: '请输入正确的手机号', pattern: /^1[3-9]\d{9}$/ }],
        },
        auth_code: {
          rules: [{ required: true, errorMessage: '请输入验证码', pattern: /^1[3-9]\d{9}$/ }],
        },
      },
      clinicList: [],
      qrLoginSuccess: false,
      userInfo: {
        user_name: '',
      },
      loading: false,
    };
  },
  async onLoad() {
    try {
      await this.initPage();
    } catch (error) {
      console.error('页面初始化失败:', error);
      this.pageError = true;
    } finally {
      this.pageLoading = false;
    }
  },
  onUnload() {
    if (this.timer) {
      clearInterval(this.timer);
    }
  },
  computed: {
    getFirstName() {
      return this.userInfo.user_name?.trim()?.slice(-2);
    },
    getCountdownText() {
      if (this.timer) {
        return `重新获取（${this.countDown}）`;
      }
      return this.hasCountdown ? '重新获取' : `获取验证码`;
    },
    getColorForName() {
      const name = this.userInfo.user_name.trim();
      if (!name) return 'hsl(0, 0%, 50%)';

      // 简单哈希算法
      let hash = 0;
      for (let i = 0; i < name.length; i++) {
        hash = name.charCodeAt(i) + ((hash << 5) - hash);
      }

      // 使用哈希值生成HSL颜色
      const hue = Math.abs(hash) % 360;
      return `hsl(${hue}, 80%, 60%)`;
    },
  },
  methods: {
    async initPage() {
      // 预加载关键资源
      await this.preloadImages();
      // 其他初始化逻辑可以在这里添加
    },

    preloadImages() {
      return new Promise(resolve => {
        const images = [
          '/static/image/rsj-logo.png',
          '/static/image/login/login-bg.png',
          '/static/image/login/login-mobile.png',
          '/static/image/login/auth-login.png',
          '/static/image/login/login-scan.png',
          '/static/image/login/scan-login.png',
          '/static/image/login/scan-bg.png',
        ];

        let loadedCount = 0;
        const totalCount = images.length;

        if (totalCount === 0) {
          resolve();
          return;
        }

        images.forEach(src => {
          // 在uni-app中使用uni.getImageInfo来预加载图片
          uni.getImageInfo({
            src: src,
            success: () => {
              loadedCount++;
              if (loadedCount === totalCount) {
                resolve();
              }
            },
            fail: () => {
              loadedCount++;
              if (loadedCount === totalCount) {
                resolve();
              }
            },
          });
        });

        // 超时处理，避免无限等待
        setTimeout(() => {
          resolve();
        }, 3000);
      });
    },

    retryLoad() {
      this.pageError = false;
      this.pageLoading = true;
      this.initPage()
        .catch(error => {
          console.error('重新加载失败:', error);
          this.pageError = true;
        })
        .finally(() => {
          this.pageLoading = false;
        });
    },

    cancelLogin() {
      this.qrLoginSuccess = false;
      this.userInfo = { user_name: '' };
    },
    getRandomColor() {
      const hue = Math.floor(Math.random() * 360);
      const saturation = Math.floor(Math.random() * 30) + 40; // 40-70%
      const lightness = Math.floor(Math.random() * 30) + 50; // 50-80%
      // 生成 HSL 颜色字符串
      const backgroundColor = `hsl(${hue}, ${saturation}%, ${lightness}%)`;
      return backgroundColor;
    },
    getClinicList() {
      if (!this.formData.mobile) return;
      LoginService.getClinicList({ mobile: this.formData.mobile }, { hideLoading: true }).then(res => {
        this.clinicList = res.list.map(item => {
          return {
            ...item,
            text: item.name,
            value: item.id,
          };
        });
        this.formData.clinic_id = this.clinicList[0].value;
      });
    },
    loginHandler() {
      this.$refs.loginForm.validate(valid => {
        if (!valid) {
          this.loading = true;
          LoginService.login({
            mobile: this.formData.mobile,
            auth_code: this.formData.auth_code,
            clinic_id: this.formData.clinic_id,
          })
            .then(res => {
              console.log("🚀 ~ loginHandler ~ res: ", res);
              this.handleLoginInfo(res);
            })
            .catch(err => {})
            .finally(() => {
              this.loading = false;
            });
        } else {
          uni.showToast({
            title: '请正确填写信息',
            icon: 'none',
          });
        }
      });
    },
    getAuthCode() {
      if (this.timer) return;
      const mobileReg = /^1[3-9]\d{9}$/;
      if (!mobileReg.test(this.formData.mobile)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none',
        });
        return;
      }
      this.startCountdown();
      LoginService.getAuthCode({ mobile: this.formData.mobile }).then(res => {
        uni.showToast({
          title: '验证码已发送',
          icon: 'none',
        });
      });
    },
    startCountdown() {
      this.countDown = 60;
      this.timer = setInterval(() => {
        this.countDown--;
        if (this.countDown === 0) {
          this.hasCountdown = true;
          clearInterval(this.timer);
          this.timer = null;
          this.countDown = 60;
        }
      }, 1000);
    },
    scanConfirmLogin() {
      LoginService.qrLogin({ token: this.qrToken })
        .then(res => {
          this.userInfo = res;
          this.handleLoginInfo(res);
        })
        .catch(err => {
          uni.showToast({
            title: err.errmsg,
            icon: 'none',
          });
        });
    },
    scanToLogin() {
      uni.scanCode({
        success: res => {
          if (res.result) {
            try {
              const result = res.result.split('-');
              console.log('result: ', result);
              this.userInfo.user_name = result[0];
              this.qrToken = result[1];
              this.qrLoginSuccess = true;
            } catch (e) {
              uni.showToast({
                title: '请扫描正确的二维码',
                icon: 'none',
              });
            }
          } else {
            uni.showToast({
              title: '扫码失败',
              icon: 'none',
            });
          }
        },
        fail: res => {
          uni.showToast({
            title: '扫码失败',
            icon: 'none',
          });
        },
      });
    },
    confirmLogin() {
      this.scanConfirmLogin();
    },
    handleLoginInfo(info) {
      writeLoginInfo(info, info.token, info.refresh_token);
      const query = {
        menuId: '1',
      };
      LoginService.getDayLastRecord()
        .then(res => {
          if (res.is_exist === '1') {
            query.mr_id = res.mr_id;
            query.pt_id = res.pt_id;
          }
          this.$store.commit('global/SET_QUERY', query);
          uni.reLaunch({
            url: '/pages/index/index',
          });
        })
        .catch(e => {
          this.$store.commit('global/SET_QUERY', query);
          uni.reLaunch({
            url: '/pages/index/index',
          });
        });
    },
  },
};
</script>

<style lang="scss" scoped>
page {
  background-color: #f5f7fa;
  height: 100%;
}

.login-container {
  background-color: #f5f7fa; // 添加fallback背景色
  background-image: url('/static/image/login/login-bg.png');
  width: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  user-select: none;
  height: 100%;
  min-height: 100vh; // 确保最小高度
  /* 包括手势区域 */
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  .loading-content {
    text-align: center;
    font-size: 32rpx;
    color: #666;
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: 40rpx;
  background-color: #f5f7fa;

  text {
    font-size: 32rpx;
    color: #666;
    margin-bottom: 40rpx;
  }

  button {
    background: #155bd4;
    color: white;
    border: none;
    padding: 20rpx 40rpx;
    border-radius: 8rpx;
    font-size: 28rpx;
  }
}

.header {
  display: flex;
  align-items: center;
  margin-left: 96rpx;
  padding-top: 96rpx;

  .logo-box {
    border-right: 2rpx solid rgba(0, 0, 0, 0.64);
    padding-right: 24rpx;

    .logo {
      display: block;
      width: 256rpx;
      height: 60rpx;
    }
  }

  .title {
    display: inline-block;
    margin-left: 24rpx;
    font-weight: 500;
    font-size: 32rpx;
    color: #262626;
    align-self: flex-end;
    line-height: 1;
  }
}
.input-icon {
  width: 32rpx;
  min-width: 32rpx;
  height: 32rpx;
  margin-left: 10px;
  margin-top: 4px;
}
.login-wrapper {
  position: absolute;
  top: 50%;
  right: 240rpx;
  transform: translateY(-50%);
  display: flex;
  width: 688rpx;
  height: 792rpx;
  background: #ffffff;
  border-radius: 8rpx;

  .login-area {
    width: 100%;
    height: 100%;

    .mark {
      position: absolute;
      top: 0;
      right: 0;
      width: 0;
      height: 0;
      border-top: 112rpx solid $color-primary;
      border-left: 112rpx solid transparent;
      border-radius: $border-radius-base;
      z-index: 1;
    }

    .mobile-image {
      position: absolute;
      right: 12rpx;
      top: 12rpx;
      width: 48rpx;
      height: 48rpx;
      z-index: 2;
    }

    .auth-image {
      position: absolute;
      right: 84rpx;
      top: 28rpx;
      width: 124rpx;
      height: 32rpx;
      z-index: 2;
    }
  }

  .mobile-box {
    padding-left: 80rpx;
    padding-right: 80rpx;

    .input-box {
      position: relative;

      .input-prefix {
        margin-left: 24rpx;
        margin-right: 4rpx;
      }

      .icon-select {
        ::v-deep .uni-stat-box .uni-select {
          padding-left: 72rpx;
        }
        ::v-deep .uni-select__input-text {
          max-width: 193px;
        }
        ::v-deep .uni-select__selector-item {
          text-align: left;
        }
      }
      .select-prefix {
        position: absolute;
        top: -2rpx;
        left: 0rpx;
        transform: translateY(50%);
      }
    }

    .auth-box {
      @include flex(row, center, center);

      .auth-btn {
        color: $color-primary;
        font-size: 26rpx;
        margin-right: 24rpx;
      }

      .divider {
        width: 2rpx;
        height: 40rpx;
        background: #ebedf0;
        margin-right: 24rpx;
      }
    }
  }

  .login-bx {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;

    .auth-title {
      font-weight: bold;
      font-size: 36rpx;
      color: #262626;
      line-height: 48rpx;
      text-align: center;
      margin-top: 96rpx;
    }

    .scan-bg {
      width: 376rpx;
      height: 256rpx;
      margin-top: 88rpx;
      margin-bottom: 112rpx;
    }

    .scan-btn {
      width: 528rpx;
    }
  }

  .login-forms {
    margin-top: 80rpx;

    .uni-forms-item {
      margin-bottom: 40rpx;
    }

    .login-btn {
      margin-top: 34rpx;
    }
  }

  .qr-confirm-box {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0 80rpx;

    .user-avatar {
      height: 208rpx;
      width: 208rpx;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 100rpx;
      color: #fff;
      font-size: 28px;
    }

    .user-name {
      font-weight: 600;
      font-size: 40rpx;
      color: #262626;
      line-height: 56rpx;
      margin-top: 32rpx;
      margin-bottom: 136rpx;
    }

    .link-primary {
      font-size: 28rpx;
      color: #155bd4;
      line-height: 40rpx;
      margin-top: 32rpx;
    }
  }
}
</style>
