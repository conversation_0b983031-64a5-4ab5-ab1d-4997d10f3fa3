<template>
  <uni-popup ref="popup" type="dialog" :is-mask-click="isMask">
    <view class="confirm-dialog">
      <view class="dialog-header">
        <text class="dialog-title">{{ title }}</text>
        <uni-icons v-if="showClose" type="closeempty" size="16" color="#AAAAAA" @click="handleClose"></uni-icons>
      </view>
      <scroll-view style="height: 500rpx;margin: 20rpx 32rpx" scroll-y="true" @touchmove.stop>
        <view class="dialog-content">
          <uni-data-checkbox multiple v-model="selectedUserIds" :localdata="userList"
            @change="changeUser"></uni-data-checkbox>
        </view>
      </scroll-view>
      <view class="dialog-footer">
        <button class="dialog-btn cancel-btn" @click="handleClose" v-if="showCancel">{{ cancelText }}</button>
        <button class="dialog-btn confirm-btn" type="primary" @click="handleConfirm">{{ confirmText }}</button>
      </view>
    </view>
  </uni-popup>
</template>

<script>
  export default {
    name: "changeDialog",
    props: {
      value: {
        type: Boolean,
        default: () => false
      },
      isMask: {
        type: Boolean,
        default: () => false
      },
      // 标题
      title: {
        type: String,
        default: '调整当日目标'
      },
      // 内容
      message: {
        type: String,
        default: ''
      },
      // 确认按钮文字
      confirmText: {
        type: String,
        default: '确定'
      },
      // 取消按钮文字
      cancelText: {
        type: String,
        default: '取消'
      },
      // 是否显示取消按钮
      showCancel: {
        type: Boolean,
        default: true
      },
      // 是否显示关闭按钮
      showClose: {
        type: Boolean,
        default: false
      },
      userList: {
        type: Array,
        default: () => []
      },
      echoIds: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        selectedUserIds: [],
        selectedUser: [],
      }
    },
    watch: {
      value (val) {
        this.$nextTick(() => {
          if (val) {
            this.$refs.popup.open()
            this.init()
            uni.hideKeyboard()
          }else{
            this.$refs.popup.close()
          }
        })
      }
    },
    methods: {
      init () {
        this.selectedUserIds = this.echoIds
      },
      changeUser(e) {
        this.selectedUser = e.detail.data
      },
      handleClose () {
        this.clearData()
        this.$emit('input', false)
      },
      clearData () {
        this.selectedUser =  []
        this.selectedUserIds =  []
      },
      // 处理确认
      handleConfirm() {
        this.$emit('confirm', this.selectedUser);
        this.handleClose();
      },
    }
  }
</script>

<style lang="scss" scoped>
  .confirm-dialog {
    width: 726rpx;

    background: #FFFFFF;
    border-radius: 12rpx;
    overflow: hidden;

    .dialog-header {
      position: relative;
      padding: 32rpx;
      text-align: center;
      border-bottom: 2rpx solid #EBEDF0;

      .dialog-title {
        font-weight: 600;
        font-size: 32rpx;
        color: #333333;
        line-height: 48rpx;
      }

      .uni-icons {
        position: absolute;
        right: 32rpx;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
      }
    }

    .dialog-content {
      min-height: 120rpx;
      max-height: 800rpx;
      //display: flex;
      //align-items: center;
      //justify-content: center;
      overflow: auto;

      .dialog-message {
        font-size: 28rpx;
        color: #666666;
        line-height: 40rpx;
        text-align: center;
      }
    }

    .dialog-footer {
      padding: 0 32rpx 32rpx;
      display: flex;
      gap: 24rpx;

      .dialog-btn {
        flex: 1;
        height: 80rpx;
        line-height: 80rpx;
        font-size: 28rpx;
        border-radius: 8rpx;

        &.cancel-btn {
          background: #F5F6F8;
          color: #666666;
          border: none;

          &:active {
            background: darken(#F5F6F8, 5%);
          }
        }

        &.confirm-btn {
          &:active {
            opacity: 0.9;
          }
        }
      }
    }
  }

  ::v-deep .uni-data-checklist .checklist-group {
    display: block;
  }
  ::v-deep .uni-data-checklist .checklist-group .checklist-box{
    margin-bottom: 20rpx;
  }
</style>
