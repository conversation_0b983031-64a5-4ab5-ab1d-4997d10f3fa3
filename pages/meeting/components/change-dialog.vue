<template>
<uni-popup ref="popup" type="dialog" @change="changeVisible" :is-mask-click="isMask">
  <view class="confirm-dialog">
    <view class="dialog-header">
      <text class="dialog-title">{{ title }}</text>
      <uni-icons v-if="showClose" type="closeempty" size="16" color="#AAAAAA" @click="handleClose"></uni-icons>
    </view>
    <view class="dialog-content">
      <uni-easyinput ref="target" v-model="daily_target" type="number" placeholder="请输入今日目标业绩" @clear="clearTarget"></uni-easyinput>
    </view>
    <view class="tips">
      <image src="/static/image/meeting/notice.png" class="notice-icon"></image>
      <view class="mr20">{{target_performance.monthly_performance_text}}</view>
    </view>
    <view class="dialog-footer">
      <button class="dialog-btn cancel-btn" @click="handleCancel" v-if="showCancel">{{ cancelText }}</button>
      <button class="dialog-btn confirm-btn" type="primary" @click="setMeetingDailyTarget">{{ confirmText }}</button>
    </view>
  </view>
</uni-popup>
</template>

<script>
import MeetingService from "@/api/meeting";

export default {
  name: "changeDialog",
  props: {
    isMask: {
      type: Boolean,
      default: () => false
    },
    // 标题
    title: {
      type: String,
      default: '调整当日目标'
    },
    // 内容
    message: {
      type: String,
      default: ''
    },
    // 确认按钮文字
    confirmText: {
      type: String,
      default: '确定'
    },
    // 取消按钮文字
    cancelText: {
      type: String,
      default: '取消'
    },
    // 是否显示取消按钮
    showCancel: {
      type: Boolean,
      default: true
    },
    // 是否显示关闭按钮
    showClose: {
      type: Boolean,
      default: false
    },
    target_performance: {
      type: Object,
      default: () => {}
    },
    date: {
      type: String,
      default: '',
    },
    echoTarget: {
      type: String,
      default: '',
    },
  },
  data(){
    return {
      daily_target: null,
    }
  },
  methods: {
    // 打开弹窗
    open() {
      this.$refs.popup.open();
    },
    // 关闭弹窗
    close() {
      this.$refs.popup.close();
    },

    changeVisible(e){
      console.log("=>(change-dialog.vue:89) e", e);
      if(!e.show){
        this.daily_target = null
      }else{
        this.daily_target = this.target_performance.daily_target
      }
    },

    // 处理确认
    handleConfirm() {
      this.setMeetingDailyTarget()
      // this.$emit('confirm');
      // this.close();
    },
    // 处理取消
    handleCancel() {
      this.$emit('cancel');
      this.close();
    },
    // 处理关闭
    handleClose() {
      this.$emit('close');
      this.close();
    },
    setMeetingDailyTarget(){
      if(!this.daily_target){
        uni.showToast({
          title: '请输入今日目标业绩',
          icon: 'none'
        });
        return
      }
      let params = {
        date: this.date,
        daily_target: this.daily_target,
      }
      MeetingService.setMeetingDailyTarget(params).then(res => {
        console.log("=>(change-dialog.vue:105) res", res);
        uni.showToast({
          title: '修改成功',
          icon: 'none'
        });
        this.close();
        this.$emit('refresh');
      })
    },

    clearTarget(){
      this.$nextTick(() => {
        console.log("=>(change-dialog.vue:142) this.$refs.target", this.$refs.target);
        this.$refs.target.onFocus()
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.confirm-dialog {
  width: 726rpx;
  background: #FFFFFF;
  border-radius: 12rpx;
  overflow: hidden;

  .dialog-header {
    position: relative;
    padding: 32rpx;
    text-align: center;
    border-bottom: 2rpx solid #EBEDF0;

    .dialog-title {
      font-weight: 600;
      font-size: 32rpx;
      color: #333333;
      line-height: 48rpx;
    }

    .uni-icons {
      position: absolute;
      right: 32rpx;
      top: 50%;
      transform: translateY(-50%);
      cursor: pointer;
    }
  }

  .dialog-content {
    padding: 48rpx 32rpx;
    min-height: 120rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    .dialog-message {
      font-size: 28rpx;
      color: #666666;
      line-height: 40rpx;
      text-align: center;
    }
  }

  .dialog-footer {
    padding: 0 32rpx 32rpx;
    display: flex;
    gap: 24rpx;

    .dialog-btn {
      flex: 1;
      height: 80rpx;
      line-height: 80rpx;
      font-size: 28rpx;
      border-radius: 8rpx;

      &.cancel-btn {
        background: #F5F6F8;
        color: #666666;
        border: none;

        &:active {
          background: darken(#F5F6F8, 5%);
        }
      }

      &.confirm-btn {
        &:active {
          opacity: 0.9;
        }
      }
    }
  }
}
.tips{
  display: flex;
  font-weight: 400;
  font-size: 24rpx;
  color: #999999;
  padding: 0 24rpx;
  margin-bottom: 20rpx;
  .notice-icon{
    width: 24rpx;
    min-width: 24rpx;
    height: 24rpx;
    margin-right: 10rpx;
    padding-top: 5rpx;
  }
}
</style>
