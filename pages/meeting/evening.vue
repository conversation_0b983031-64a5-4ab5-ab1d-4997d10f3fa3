<template>
  <view>
    <view class="evening-wrap" @touchmove.stop v-if="!searchLoading">
      <view class="top-area">
        <view class="flex flex-items-center">
          <view class="back-button" @click="back">
            <image src="/static/image/meeting/back-arrow.png" class="back-icon"></image>
            <view>返回</view>
          </view>
          <view class="meeting-info">
            <view class="mr32">{{ dateFormat }}</view>
            <view class="mr32">{{ getWeekDay }}</view>
            <view>夕会表</view>
          </view>
        </view>
        <view v-if="enableSubmit">
          <button type="primary" class="ui-btn-base ui-btn-primary" style="width: 176rpx;" @click="submit" :loading="loading">提交夕会</button>
        </view>

      </view>


      <scroll-view style="height: calc(100% - 64rpx)" scroll-y="true" >
        <view class="middle-area">
          <view class="middle-block">
            <view class="left-box">
              <view class="sub-title" style="margin-bottom: 56rpx;">当日业绩</view>
              <view class="price-box">
                <view class="price-text">¥ {{Number(daily_transaction_amount.today.daily_transaction_amount).toFixed(2)}}</view>
                <image :src="comparePercent(daily_transaction_amount, 'daily_transaction_amount').image" class="trend-icon" ></image>
                <view v-if="comparePercent(daily_transaction_amount, 'daily_transaction_amount').isShow" :class="comparePercent(daily_transaction_amount, 'daily_transaction_amount').className">{{comparePercent(daily_transaction_amount, 'daily_transaction_amount').percent}}%</view>
              </view>
              <view class="rate-box">
                <view class="flex">
                  <image src="/static/image/meeting/morning/ring-blue.png" class="ring-blue"></image>
                  <view class="rate-label">目标达成率</view>
                </view>
                <view class="rate-content">{{ daily_transaction_amount.today.target_completion_rate}}%</view>
              </view>
            </view>
            <view class="right-box">
              <view class="daily-info">
                <view class="daily-item" v-for="item in dailyList" :key="item.key">
                  <view class="daily-title">{{item.title}}</view>
                  <view class="daily-content"> <text v-if="item.isPrice">￥</text> {{ daily_transaction_amount.today[item.key] }}</view>
                </view>
              </view>
            </view>
          </view>
          <view class="middle-block">
            <view class="left-box">
              <view class="sub-title" style="margin-bottom: 40rpx;">分项目成交</view>
              <view class="programme-box">
                <view class="programme-item">
                  <view class="programme-label">医疗消耗</view>
                  <view class="programme-content">
                    <view style="margin-right: 24rpx;">{{ formatPrice(project_deals.today.medical_treatment) }}</view>
                    <view class="flex flex-items-center">
                      <image :src="comparePercent(project_deals, 'medical_treatment').image" class="trend-icon"></image>
                      <view v-if="comparePercent(project_deals, 'medical_treatment').isShow" :class="comparePercent(project_deals, 'medical_treatment').className">{{comparePercent(project_deals, 'medical_treatment').percent}}%</view>
                    </view>
                  </view>
                </view>
                <view class="programme-item">
                  <view class="programme-label">理疗消耗</view>
                  <view class="programme-content">
                    <view style="margin-right: 24rpx;">{{ formatPrice(project_deals.today.physiotherapy) }}</view>
                    <view class="flex flex-items-center">
                      <image :src="comparePercent(project_deals, 'physiotherapy').image" class="trend-icon"></image>
                      <view v-if="comparePercent(project_deals, 'physiotherapy').isShow" :class="comparePercent(project_deals, 'physiotherapy').className">{{comparePercent(project_deals, 'physiotherapy').percent}}%</view>
                    </view>
                  </view>
                </view>
                <view class="programme-item">
                  <view class="programme-label">养疗消耗</view>
                  <view class="programme-content">
                    <view style="margin-right: 24rpx;">{{ formatPrice(project_deals.today.health_care_therapy) }}</view>
                    <view class="flex flex-items-center">
                      <image :src="comparePercent(project_deals, 'health_care_therapy').image" class="trend-icon"></image>
                      <view v-if="comparePercent(project_deals, 'health_care_therapy').isShow" :class="comparePercent(project_deals, 'health_care_therapy').className">{{comparePercent(project_deals, 'health_care_therapy').percent}}%</view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <view class="right-box">
              <view class="performance-box">
                <view class="doctor-box">
                  <view class="sub-title" style="margin-bottom: 48rpx;">医生业绩</view>
                  <scroll-view class="scroll-box" style="height: calc(100% - 72rpx)" scroll-y="true" >
                    <view class="sub-scroll-box-wrap">
                      <view class="doctor-item" v-for="item in staff_performance.doctor_performance" :key="item.id">
                        <view class="doctor-info">
                          <view class="doctor-name">{{item.name}}</view>
                          <view class="doctor-content">
                            <view class="doctor-perform" style="margin-right: 10rpx;">￥{{item.today}}</view>
                            <view class="flex flex-items-center">
                              <image :src="compareRow(item).image" class="trend-icon"></image>
                              <view v-if="Number(item.before) > 0 && Number(item.today) > 0" :class="compareRow(item).className">{{compareRow(item).percent}}%</view>
                            </view>
                          </view>
                        </view>
                        <u-line-progress :percentage="item.percentage" activeColor="#2C72FF" :showText="false"></u-line-progress>
                      </view>
                    </view>
                  </scroll-view>
                </view>
                <view class="physical-box">
                  <view class="sub-title" style="margin-bottom: 48rpx;">理疗师业绩</view>
                  <scroll-view class="scroll-box" style="height: calc(100% - 72rpx)" scroll-y="true" >
                    <view class="sub-scroll-box-wrap">
                      <view class="physical-item" v-for="item in staff_performance.physical_therapist_performance" :key="item.id">
                        <view class="physical-info">
                          <view class="physical-name">{{item.name}}</view>
                          <view class="physical-content">
                            <view class="physical-perform" style="margin-right: 10rpx;">￥{{item.today}}</view>
                            <view class="flex flex-items-center">
                              <image :src="compareRow(item).image" class="trend-icon"></image>
                              <view v-if="Number(item.before) > 0 && Number(item.today) > 0" :class="compareRow(item).className">{{compareRow(item).percent}}%</view>
                            </view>
                          </view>
                        </view>
                        <u-line-progress :percentage="item.percentage" activeColor="#2C72FF" :showText="false"></u-line-progress>
                      </view>
                    </view>
                  </scroll-view>
                </view>
              </view>

            </view>
          </view>
        </view>
        <view class="bottom-area">
          <view class="flex" style="margin-bottom: 40rpx;">
            <view class="bottom-label" style="margin-top: 16rpx;">优秀案例分享：</view>
            <uni-easyinput type="textarea" :disabled="!enableSubmit" v-model="excellent_cases" maxlength="200" placeholder="请输入优秀案例"></uni-easyinput>
          </view>
          <view class="flex flex-items-center">
            <view class="bottom-label">院感检查：</view>
            <uni-data-checkbox v-model="nosocomial_inspection" :localdata="nosocomialList" :disabled="!enableSubmit" @change="changeInspection"></uni-data-checkbox>
          </view>
        </view>
        <view style="height: 64rpx;width: 100%;"></view>
      </scroll-view>
      <confirm-dialog ref="confirm" title="确认提交今日夕会吗" message="记得院感检查哦~"
                      @confirm="submitEvening"></confirm-dialog>

    </view>
    <c-loading :show="searchLoading" />
  </view>
</template>

<script>

import ChangeDialog from "@/pages/meeting/components/change-dialog.vue";
import ConfirmDialog from "@/components/confirm-dialog/index.vue";
import addPerson from "@/pages/meeting/components/add-person.vue";
import MeetingService from "@/api/meeting";
import S from "@/utils/utils";
import {$operator} from "@/utils/operation";
import formatMixin from '@/mixins/format'
import CLoading from "@/components/cLoading/loading.vue";

export default {
  name: "evening",

  components: {CLoading, addPerson, ConfirmDialog, ChangeDialog},
  mixins: [formatMixin],
  props: {
    selectedDate: {
      type: String,
      default: '',
    }
  },
  data() {
    return {
      enableSubmit: false,
      loading: false,
      excellent_cases: '',
      nosocomialList: [{value: '1', text: '已检查'},{value: '2', text: '未检查'}],
      nosocomial_inspection: '',
      daily_transaction_amount: {
        today: {
          daily_transaction_amount: 0,
          target_completion_rate: 0,
          transaction_user_count: 0,
          new_customer_transaction: 0,
          member_transaction: 0,
          recharge_user_count: 0,
          daily_revenue: 0,
          today_recharge_amount: 0,
          physiotherapy_transaction: 0,
          physiotherapy_consumption: 0,
        },
        before: {
          daily_transaction_amount: 0,
          target_completion_rate: 0,
          transaction_user_count: 0,
          new_customer_transaction: 0,
          member_transaction: 0,
          recharge_user_count: 0,
          daily_revenue: 0,
          today_recharge_amount: 0,
          physiotherapy_transaction: 0,
          physiotherapy_consumption: 0,
        }
      },
      project_deals: {
        today: {
          health_care_therapy: 0,
          medical_treatment: 0,
          physiotherapy: 0,
        },
        before: {
          health_care_therapy: 0,
          medical_treatment: 0,
          physiotherapy: 0,
        },
      },
      staff_performance: [],
      dailyList: [
        {key: 'transaction_user_count', title: '交易人数'},
        {key: 'new_customer_transaction', title: '新客交易'},
        {key: 'member_transaction', title: '会员交易'},
        {key: 'recharge_user_count', title: '储值人数'},
        {key: 'daily_revenue', title: '当日营收', isPrice: true},
        {key: 'today_recharge_amount', title: '今日储值', isPrice: true},
        {key: 'physiotherapy_transaction', title: '理疗成交'},
        {key: 'physiotherapy_consumption', title: '理疗消耗'},
      ],
      searchLoading: false
    };
  },
  computed: {
    dateFormat(){
      return S.dayjs(this.selectedDate).format('YYYY年MM月DD')
    },
    getWeekDay(){
      const weekCN = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
      return weekCN[S.dayjs(this.selectedDate).day()];
    },
    comparePercent(){
      return (obj,key) => {
        let today = obj.today[key] || {}
        let before = obj.before[key] || {}
        let diff = $operator.subtract(today, before) || 0
        let percent = $operator.divide(diff || 0,before)
        let isShow = Number(today) > 0 && Number(before) > 0


        let image = ''
        let className = ''
        if(today > before){
          image = '/static/image/meeting/evening/up.png'
          className = 'up'
        }else if(today === before){
          image = '/static/image/meeting/evening/flat.png'
          className = 'flat'
        }else if(today < before){
          image = '/static/image/meeting/evening/down.png'
          className = 'down'
        }else{
          image = ''
        }

        return {
          image: image,
          className: className,
          percent: Math.abs(percent),
          isShow: isShow
        }
      }
    },
    compareRow(){
      return item => {
        let today = item.today
        let before = item.before
        let diff = $operator.subtract(today, before) || 0
        let percent = $operator.divide(diff || 0,before)


        let image = ''
        let className = ''
        if(today > before){
          image = '/static/image/meeting/evening/up.png'
          className = 'up'
        }else if(today === before){
          image = '/static/image/meeting/evening/flat.png'
          className = 'flat'
        }else if(today < before){
          image = '/static/image/meeting/evening/down.png'
          className = 'down'
        }else{
          image = ''
        }

        return {
          image: image,
          className: className,
          percent: Math.abs(percent)
        }
      }
    },
  },
  watch: {},
  created() {
  },
  mounted() {
    console.log("=>(evening.vue:174) this.selectedDate", this.selectedDate);
    if(this.selectedDate){
      this.getMeetingDailyEvening()
    }
  },
  methods: {
    submit(){
      this.$refs.confirm.open();
    },
    submitEvening(){
      this.loading = true
      let params = {
        date: this.selectedDate,
        excellent_cases: this.excellent_cases,
        nosocomial_inspection: this.nosocomial_inspection
      }
      MeetingService.submitDailyEvening(params).then(res => {
        uni.showToast({
          title: '提交成功',
          icon: 'none'
        });
        this.$emit('changePageTab', 'home')
      }).finally(() => this.loading = false)
    },
    changeInspection(e){
      console.log("=>(evening.vue:100) e", e);
    },
    back(){
      this.$emit('changePageTab', 'home')
    },
    getMeetingDailyEvening(){
      this.searchLoading = true
      let params = {date: this.selectedDate}
      MeetingService.getMeetingDailyEvening(params).then(res => {
        console.log("=>(morning.vue:216) res", res);
        this.enableSubmit = res.submit_button === '1'
        this.daily_transaction_amount = res.daily_transaction_amount
        this.project_deals = res.project_deals
        this.staff_performance = res.staff_performance
        this.excellent_cases = res.excellent_cases
        this.nosocomial_inspection = res.nosocomial_inspection
      }).finally(() => this.searchLoading = false)
    },
  },


};
</script>
<style lang="scss" scoped>
.evening-wrap{
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  padding: 24rpx 24rpx 0;
  .top-area {
    width: 100%;
    display: flex;
    justify-content: space-between;
    margin-bottom: 24rpx;
    //align-items: center;
    .back-button {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 160rpx;
      height: 64rpx;
      background: #ffffff;
      border-radius: 32rpx;
      font-weight: 400;
      font-size: 26rpx;
      color: #333333;
      .back-icon {
        width: 28rpx;
        height: 28rpx;
        margin-right: 10rpx;
      }
    }
    .meeting-info {
      display: flex;
      font-weight: 500;
      font-size: 26rpx;
      color: #333333;
      margin-left: 20rpx;
      .mr32 {
        margin-right: 32rpx;
      }
    }
  }
  .middle-area{
    //margin-bottom: 24rpx;
    .middle-block{
      display: flex;
      gap: 24rpx;
      margin-bottom: 24rpx;
      .left-box{
        position: relative;
        width: 648rpx;
        min-width: 648rpx;
        max-width: 648rpx;
        padding: 32rpx;
        background-color: #fff;
        border-radius: 8rpx;
      }
      .right-box{
        flex: 1;
      }


      .price-box{
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 58rpx;
        .price-text{
          font-weight: 600;
          font-size: 40rpx;
          color: #333333;
          margin-right: 16rpx;
        }
      }
      .rate-box{
        position: absolute;
        bottom: 24rpx;
        left: 24rpx;
        width: calc(100% - 48rpx);
        padding: 24rpx 32rpx;
        background-color: #F9FAFB;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 8rpx;
        .ring-blue{
          width: 40rpx;
          height: 40rpx;
          margin-right: 16rpx;
        }
        .rate-label{
          font-weight: 400;
          font-size: 26rpx;
          color: #333333;
        }
        .rate-content{
          font-weight: 500;
          font-size: 28rpx;
          color: #333333;
        }
      }
      .daily-info{
        display: flex;
        align-items: center;
        gap: 24rpx;
        flex-wrap: wrap;
        margin-right: -24rpx;
        .daily-item{
          width: calc(25% - 24rpx);
          background: #FFFFFF;
          border-radius: 8rpx;
          padding: 32rpx;
          .daily-title{
            font-weight: 400;
            font-size: 26rpx;
            color: #999999;
            margin-bottom: 32rpx;
          }
          .daily-content{
            font-weight: 600;
            font-size: 44rpx;
            color: #333333;
          }
        }
      }
      .programme-box{
        display: flex;
        gap: 24rpx;
        flex-direction: column;
        .programme-item{
          display: flex;
          justify-content: space-between;
          align-items: center;
          background: linear-gradient( 90deg, #F3F7FF 0%, rgba(245,246,248,0) 100%);
          border-radius: 8rpx;
          padding: 28rpx 32rpx;
          .programme-label {
            font-weight: 400;
            font-size: 26rpx;
            color: #333333;
          }
          .programme-content {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 36rpx;
            color: #333333;
          }
        }
      }
      .performance-box{
        display: flex;
        padding: 32rpx;
        border-radius: 8rpx;
        background-color: #fff;
        height: 512rpx;

        .doctor-box{
          flex: 1;
          border-right: 1px solid #EBEDF0;
          margin-right: 32rpx;
          padding-right: 32rpx;

        }
        .physical-box{
          flex: 2;
        }
      }
    }

  }
  .bottom-area{
    padding: 32rpx 32rpx;
    background: #FFFFFF;
    border-radius: 8rpx;
    .bottom-label{
      min-width: 200rpx;
      font-weight: 400;
      font-size: 26rpx;
      color: #333333;
      text-align: right;

    }
    .add-button{
      min-width: fit-content;
      display: flex;
      align-items: center;
      font-weight: 400;
      font-size: 28rpx;
      color: #155BD4;
      .add-icon{
        width: 20rpx;
        height: 20rpx;
        margin-right: 10rpx;
      }
    }
  }
  .fixed-area{
    position: fixed;
    bottom: 0;
    left: 120rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #fff;
    width: 100%;
    height: 150rpx;
  }

  .sub-title{
    font-weight: 600;
    font-size: 30rpx;
    color: #333333;
  }

  .trend-icon{
    width: 24rpx;
    min-width: 24rpx;
    height: 24rpx;
    margin-right: 10rpx;
  }
  .scroll-box {
    position: relative;
    width: 100%;
    .sub-scroll-box{
      width: 100%;
      display: flex;
      flex-wrap: nowrap;
      .customer-item {
        display: inline-flex;
        align-items: center;
        flex-shrink: 0;
        .customer-avatar {
          width: 64rpx;
          height: 64rpx;
          border: 1rpx solid #eeeeee;
          border-radius: 50%;
          margin-right: 16rpx;
          background-color: skyblue;
        }
        .customer-name {
          font-weight: 400;
          font-size: 24rpx;
          color: #333333;
          padding-right: 32rpx;
          border-right: 2rpx solid #e3e3e3;
          margin-right: 32rpx;
        }
        &:last-child{
          .customer-name{
            border: 0;
            margin-right: 0;
            padding-right: 0;
          }
        }
      }
      .appoint-item{
        display: inline-flex;
        align-items: center;
        flex-shrink: 0;
        padding: 10rpx 24rpx;
        font-weight: 400;
        font-size: 26rpx;
        color: #333333;
        background: #F5F6F8;
        border-radius: 8rpx;
        margin-right: 24rpx;
      }

    }
    .sub-scroll-box-wrap{
      width: 100%;
      height: 100%;
      display: flex;
      gap: 24rpx;
      flex-wrap: wrap;
      .doctor-item{
        width: 100%;
        .doctor-info{
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 12rpx;
          .doctor-name{
            font-weight: 400;
            font-size: 26rpx;
            color: #333333;
          }
          .doctor-content{
            display: flex;
            align-items: center;
            .doctor-perform{
              font-weight: 500;
              font-size: 20rpx;
              color: #333333;
            }

          }
        }
      }
      .physical-item{
        width: calc(50% - 24rpx);
        .physical-info{
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 12rpx;
          .physical-name{
            font-weight: 400;
            font-size: 26rpx;
            color: #333333;
          }
          .physical-content{
            display: flex;
            align-items: center;
            .physical-perform{
              font-weight: 500;
              font-size: 20rpx;
              color: #333333;
            }

          }
        }
      }
    }
  }
  .up{
    font-weight: 400;
    font-size: 24rpx;
    color: #EE3838;
  }
  .flat{
    font-weight: 400;
    font-size: 24rpx;
    color: #155BD4;
  }
  .down{
    font-weight: 400;
    font-size: 24rpx;
    color: #25B830;
  }
}
</style>
