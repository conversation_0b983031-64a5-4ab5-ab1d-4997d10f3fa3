<template>
<view class="meeting-wrap">
  <view class="left-wrap">
    <view class="morning-wrap">
      <image src="/static/image/meeting/morning.png" class="side-img" mode="heightFix"></image>
      <view class="report-wrap">
        <view class="report-title">晨会表</view>
        <view class="report-button morning" :class="select_morning ? 'committed-morning' : ''" @click="goMorning">{{ select_morning ? '晨会概况' : '填报晨会' }}</view>
      </view>
    </view>


    <view class="evening-wrap">
      <image src="/static/image/meeting/evening.png" class="side-img" mode="heightFix"></image>
      <view class="report-wrap">
        <view class="report-title">夕会表</view>
        <view class="report-button evening" :class="select_evening ? 'committed-evening' : ''" @click="goEvening">{{ select_evening ? '夕会概况' : '填报夕会' }}</view>
      </view>
    </view>
  </view>
  <view class="right-wrap">
    <uni-calendar class="uni-calendar--hook" :selected="info.selected" :showMonth="false" @change="change" @monthSwitch="monthSwitch" :isHideDot="true" />
    <view class="line"></view>
    <view class="right-content">
      <view class="record">
        <view class="record-item">
          <image :src="select_morning ? '/static/image/meeting/success.png' : '/static/image/meeting/fail.png'" class="notice-icon"></image>
          <view class="mr20">晨：当日晨会{{ select_morning? '已' : '未' }}提交</view>
        </view>
        <view class="record-item">
          <image :src="select_evening ? '/static/image/meeting/success.png' : '/static/image/meeting/fail.png'" class="notice-icon"></image>
          <view>夕：当日夕会{{ select_evening? '已' : '未' }}提交</view>
        </view>


      </view>
    </view>
  </view>
</view>
</template>

<script>
import S from "@/utils/utils";
import MeetingService from "@/api/meeting";

export default {
  name: "meeting-page",

  components: {},
  props: {
    pageTabKey: {
      type: String,
      default: '',
    }
  },
  data() {
    return {
      info: {
        lunar: true,
        range: true,
        insert: false,
        selected: [],
      },
      month: '',
      todayDate: '',
      todayInfo: {},
      selectedDate: '',
      day_list: [],
    };
  },
  computed: {
    today_morning(){
      return this.todayInfo?.is_morning === '1' || false
    },
    today_evening(){
      return this.todayInfo?.is_evening === '1' || false
    },
    select_morning(){
      return this.day_list?.find(item => this.selectedDate === item.date)?.is_morning === '1' || false
    },
    select_evening(){
      return this.day_list?.find(item => this.selectedDate === item.date)?.is_evening === '1' || false
    },
  },
  watch: {
    pageTabKey: {
      handler(val){
        if(val === 'home'){
          this.getMeetingDailyInfo()
        }
      }
    }
  },
  created() {
    this.month = S.dayjs().format('YYYY-MM')
    this.todayDate = S.dayjs().format('YYYY-MM-DD')
    this.selectedDate = S.dayjs().format('YYYY-MM-DD')
    // this.getMeetingDailyInfo()
    this.$emit('changePageTab', 'home')
  },
  mounted() {
  },
  methods: {
    change(e) {
      console.log('change 返回:', e)
      this.selectedDate = e.fulldate
    },
    confirm(e) {
      console.log('confirm 返回:', e)
    },
    monthSwitch(e) {
      console.log('monthSwitchs 返回:', e)
      this.month = `${e.year}-${e.month}`
      this.info.selected = []
      this.getMeetingDailyInfo()
    },
    getMeetingDailyInfo(){
      let params = {
        month: this.month
      }
      MeetingService.getMeetingDailyInfo(params).then(res => {
        console.log("=>(index.vue:94) res", res);


        // todo mock
        // res.day_list.forEach(item => {
        //   if(item.weekday === '一'){
        //     item.is_morning = '1'
        //   }
        //   if(item.weekday === '五'){
        //     item.is_evening = '1'
        //   }
        //   if(item.weekday === '日'){
        //     item.is_morning = '1'
        //     item.is_evening = '1'
        //   }
        // })
        this.day_list = res.day_list
        console.log("=>(index.vue:130) this.day_list", this.day_list);




        res.day_list.forEach(item => {
          const info =
            item.is_morning === '1' && item.is_evening === '1' ? '晨 夕' :
              item.is_morning === '1' ? '晨' :
                item.is_evening === '1' ? '夕' : '';
          if(info){
            this.info.selected.push({
              date: item.date,
              info: info
            })
          }
          if(item.is_now === '1'){
            this.todayInfo = item
          }
        })
      })
    },
    goMorning(){
      // 设置路由参数
      this.$store.commit('global/SET_QUERY', {})
      this.$emit('changePageTab', 'morning', this.selectedDate)
    },
    goEvening(){
      // 设置路由参数
      this.$store.commit('global/SET_QUERY', {})
      this.$emit('changePageTab', 'evening', this.selectedDate)
    },
  },


};
</script>
<style scoped lang="scss">
.meeting-wrap{
  width: 100%;
  height: 100vh;
  display: flex;
  padding: 24rpx;
  .left-wrap{
    //width: 754rpx;
    width: 36%;
    display: flex;
    flex-direction: column;
    margin-right: 26rpx;
    .morning-wrap{
      display: flex;
      flex: 1;
      height: 50%;
      background-color: #fff;
      margin-bottom: 26rpx;
      border-radius: 16rpx;
    }
    .evening-wrap{
      display: flex;
      flex: 1;
      background-color: #fff;
      border-radius: 16rpx;
    }
    .side-img{
      height: 100%;
    }
    .report-wrap{
      position: relative;
      display: flex;
      flex: 1;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      //width: 328rpx;
      text-align: center;
      .report-title{
        font-weight: 400;
        font-size: 36rpx;
        color: #333333;
        margin-bottom: 24rpx;
      }
      .report-button{
        width: 180rpx;
        padding: 12rpx 0;
        //height: 64rpx;
        border-radius: 100rpx;
        color: #ffffff;

      }
      .committed-morning{
        background-color: #fff !important;
        color: #319C8A;
      }
      .committed-evening{
        background-color: #fff !important;
        color: #155BD4;
      }
      .report-record{
        width: 100%;
        position: absolute;
        display: flex;
        justify-content: center;
        align-items: center;
        bottom: 28rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #999999;
        .submit-icon{
          width: 28rpx;
          height: 28rpx;
          margin-right: 12rpx;
        }
      }
      .morning{
        background: #319C8A;
        border: 1px solid #319C8A;
      }
      .evening{
        background: #155BD4;
        border: 1px solid #155BD4;
      }
    }
  }
  .right-wrap{
    flex: 1;
    border-radius: 16rpx;
    background-color: #fff;
    padding-top: 40rpx;

    .right-content{
      padding: 64rpx 60rpx;
      .record{
        display: flex;
        align-items: center;
        width: 100%;
        //padding: 25rpx;
        //background-color: #F6F6F6;
        border-radius: 8rpx;
        font-weight: 400;
        font-size: 24rpx;
        color: #333333;
        .record-item{
          display: flex;
          align-items: center;
          background-color: #F6F6F6;
          border-radius: 8rpx;
          padding: 12rpx 25rpx;
          margin-right: 20rpx;
        }
        .notice-icon{
          width: 24rpx;
          height: 24rpx;
          margin-right: 12rpx;
        }
      }
    }
  }
}
::v-deep .uni-calendar__backtoday{
  padding: 14rpx 60rpx;
  top: 0;
  background-color: #155BD4;
  color: #fff;
}
::v-deep .uni-calendar__header-text{
  font-weight: 600;
  font-size: 40rpx;
  color: #333333;
}
::v-deep .uni-calendar__header-btn-box{
  margin: 20rpx;
  padding: 20rpx 26rpx 20rpx 20rpx;
  border: 2rpx solid #E2E2EA;
  border-radius: 8rpx;
  width: unset;
  height: unset;
  &:first-child{
    padding-left: 26rpx;
    padding-right: 20rpx;
  }
}

::v-deep .uni-calendar-item__weeks-box-item {
  //height: 60px;
}
::v-deep .uni-calendar-item--checked{
  border-radius: 16rpx;
  opacity: 1;

}
::v-deep .uni-calendar-item--isDay{
  border-radius: 16rpx;
  color: #fff;
  background-color: #155BD4;
  opacity: 1;
}
.line{
  margin: 20rpx 60rpx;
  height: 1px;
  background-color: #EAEAEA;
}
.mr20{
  margin-right: 20rpx;
}
</style>
