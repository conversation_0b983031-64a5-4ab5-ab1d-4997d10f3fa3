<script>
import Home from "./home.vue";
import Morning from "./morning.vue";
import Evening from './evening.vue'

export default {
  name: "index",
  components: {Home, Morning, Evening},
  data() {
    return {
      pageTabKey: 'morning', // list 列表  detail 详情
      selectedDate: '',
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    changePageTab(targetKey, date) {
      this.pageTabKey = targetKey;
      this.selectedDate = date
      console.log("=>(index.vue:24) this.selectedDate", this.selectedDate);
    }
  }
}
</script>

<template>
<view>
  <Home v-show="pageTabKey === 'home'" :pageTabKey="pageTabKey" @changePageTab="changePageTab"></Home>
  <Morning v-if="pageTabKey === 'morning'" :selectedDate="selectedDate" @changePageTab="changePageTab" />
  <Evening v-if="pageTabKey === 'evening'" :selectedDate="selectedDate" @changePageTab="changePageTab" />
</view>
</template>

<style scoped lang="scss">
</style>
