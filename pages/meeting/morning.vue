<template>
<view>
  <view class="morning-wrap" v-if="!searchLoading">
    <view class="top-area">
      <view class="flex flex-items-center">
        <view class="back-button" @click="back">
          <image src="/static/image/meeting/back-arrow.png" class="back-icon"></image>
          <view>返回</view>
        </view>
        <view class="meeting-info">
          <view class="mr32">{{ dateFormat }}</view>
          <view class="mr32">{{ getWeekDay }}</view>
          <view>晨会表</view>
        </view>
      </view>
      <view v-if="enableSubmit">
        <button type="primary" class="ui-btn-base ui-btn-primary" style="width: 176rpx;" @click="submit" :loading="loading">提交晨会</button>
      </view>

    </view>
    <scroll-view style="height: calc(100% - 72rpx)" scroll-y="true" @touchmove.stop>
      <view class="middle-area">
        <!--  左侧卡片栏  -->
        <view class="left-content">
          <view class="card-box" style="margin-bottom: 24rpx;">
            <view class="card-title">当日预约用户人数</view>
            <view class="card-num">{{reserve.user_num || 0 }}<text class="card-unit">人</text> </view>
            <image class="card-img" src="/static/image/meeting/morning/customer.png"></image>
          </view>
          <view class="card-box">
            <view class="card-title">当日预约量</view>
            <view class="card-num">{{reserve.num || 0 }}<text class="card-unit">单</text></view>
            <image class="card-img" src="/static/image/meeting/morning/clock.png"></image>
          </view>
        </view>
        <!--  右侧多格布局    -->
        <view class="right-content">
          <view class="main-box">
            <view class="left-box">
              <!-- 当日预约用户信息  -->
              <view class="common-box" style="height: 196rpx;box-sizing: border-box;border-bottom: 2rpx solid #F6F6F6;border-radius: 0 8rpx 0 0">
                <view class="common-title">当日预约用户信息</view>
                <scroll-view class="scroll-box" scroll-x="true">
                  <view class="sub-scroll-box">
                    <view class="customer-item" v-for="(item, index) in reserve.user_list" :key="index">
                      <image :src="item.avatar || default_avatar(item.sex)" class="customer-avatar"></image>
                      <view class="customer-name">{{ item.name }}</view>
                    </view>
                  </view>
                </scroll-view>
                <view class="empty" v-if="reserve.user_list.length === 0">暂无数据</view>
              </view>
              <!--  当日员工预约数量      -->
              <view class="common-box" style="height: 170rpx;box-sizing: border-box;margin-bottom: 24rpx;border-radius: 0 0 8rpx 0">
                <view class="common-title">当日员工预约数量</view>
                <scroll-view class="scroll-box" scroll-x="true">
                  <view class="sub-scroll-box">
                    <view class="appoint-item" v-for="(item, index) in reserve.staff_list" :key="index">
                      <view>{{ item.name }}：预约{{item.num}}位</view>
                    </view>
                  </view>
                </scroll-view>
                <view class="empty" v-if="reserve.staff_list.length === 0">暂无数据</view>
              </view>

              <view class="service-box">
                <view class="common-box small-box" style="border-right: 2rpx solid #F6F6F6">
                  <view class="common-title">预约医生</view>
                  <view class="first-diagnosis">
                    <image src="/static/image/meeting/morning/initial-consult.png" class="consult-icon"></image>
                    <view>当日初诊：{{reserve.doctor_consult_list[0].num || 0}}</view>
                  </view>
                  <view class="second-diagnosis">
                    <image src="/static/image/meeting/morning/follow-up-visit.png" class="consult-icon"></image>
                    <view>当日复诊：{{reserve.doctor_consult_list[1].num || 0}}</view>
                  </view>
                </view>
                <view class="common-box" style="border-radius: 0 8rpx 8rpx 0">
                  <view class="common-title">预约服务</view>
                  <view class="empty" style="height: 70%" v-if="reserve.service_list.length === 0">暂无数据</view>
                  <scroll-view class="scroll-box" style="height: calc(100% - 72rpx)" scroll-y="true">
                    <view class="sub-scroll-box-wrap">
                      <view class="service-item" v-for="(item, index) in reserve.service_list" :key="index">
                        <view>{{ item.title }}：{{item.num}}</view>
                      </view>
                    </view>
                  </scroll-view>

                </view>
              </view>
            </view>
            <!-- 目标业绩 -->
            <view class="right-box">
              <view class="right-title">目标业绩</view>
              <view class="right-num">
                <view class="price">￥{{Number(target_performance.daily_target || 0).toFixed(2)}}</view>
                <view v-if="enableSubmit" class="button-text" @click="changeTarget">调整</view>
              </view>
              <view class="right-bottom-box">
                <view class="progress-box">
                  <image src="/static/image/meeting/morning/ring-blue.png" class="progress-circle"></image>
                  <view class="progress-title">本月目标进度：</view>
                  <view class="progress-num">{{target_performance.monthly_completion_rate}}%</view>
                </view>
                <view class="progress-box">
                  <image src="/static/image/meeting/morning/ring-skyblue.png" class="progress-circle"></image>
                  <view class="progress-title">本月时间进度：</view>
                  <view class="progress-num">{{target_performance.monthly_time_progress}}%</view>
                </view>
              </view>
            </view>
          </view>

        </view>
      </view>

      <view class="bottom-area">
        <!--   今日重点用户跟进     -->
        <view class="common-box" style="width:480rpx;min-width: 480rpx; border-right: 2rpx solid #F6F6F6;border-radius: 8rpx 0 0 8rpx">
          <view class="flex flex-between mb24">
            <view class="common-title" style="margin-bottom: 0;">重点用户跟进</view>
            <view v-if="enableSubmit" class="add-button" @click="addUser('customer')">
              <image src="/static/image/meeting/morning/plus.png" class="add-icon"></image>
              <view>添加用户</view>
            </view>
          </view>
          <view class="empty" v-if="priority_user_follow.length === 0" style="height: 80%">暂无数据</view>
          <scroll-view v-else class="scroll-box" style="height: calc(100% - 72rpx)" scroll-y="true">
            <view class="sub-scroll-box-wrap">
              <view class="important-customer-item" v-for="(item, index) in priority_user_follow" :key="index">
                <view>{{ item.name }}</view>
                <view class="divide-line">|</view>
                <view>19:00到店</view>
              </view>
            </view>
          </scroll-view>
        </view>
        <!--   今日计划/备注     -->
        <view class="common-box" style="border-right: 2rpx solid #F6F6F6;">
          <view class="common-title">计划/备注</view>
          <view class="input-box">
            <uni-easyinput type="textarea" v-model="study_plan" maxlength="200"
                           placeholder="1.请输入学习计划" class="study-input" :disabled="!enableSubmit"></uni-easyinput>
            <uni-easyinput type="textarea" v-model="remark" maxlength="200" placeholder="2.请输入其他备注" class="remark-input" :disabled="!enableSubmit"></uni-easyinput>
          </view>
        </view>
        <!--   参会人     -->
        <view class="common-box" style="min-width: 476rpx; width: 476rpx;border-radius: 0 8rpx 8rpx 0">
          <view class="flex flex-between mb24">
            <view class="common-title" style="margin-bottom: 0;">参会人：</view>
            <view v-if="enableSubmit" class="add-button" @click="addUser('staff')">
              <image src="/static/image/meeting/morning/plus.png" class="add-icon"></image>
              <view>添加参会人</view>
            </view>
          </view>


          <view v-if="attendee_text" class="attendee_text">{{attendee_text}}</view>
          <view class="empty" v-else style="height: 80%">暂无参会人</view>


        </view>



      </view>
      <view style="height: 50rpx;width: 100%;"></view>
    </scroll-view>


    <confirm-dialog ref="confirm" title="确认提交今日晨会吗" message="一旦提交就不可以修改了哦" @confirm="submitMorning"></confirm-dialog>
    <change-dialog ref="change" title="调整当日目标" :date="selectedDate" :target_performance="target_performance"
                   :echoTarget="echoTarget" @refresh="getMeetingDailyTarget"></change-dialog>
    <add-person v-model="addPersonVisible" :title="addTitle" :userList="userList" :echo-ids="echoIds"
                @confirm="addUserList"></add-person>
  </view>
  <c-loading :show="searchLoading" />
</view>

</template>

<script>
  import MeetingService from "@/api/meeting";
  import ConfirmDialog from '@/components/confirm-dialog/index.vue';
  import ChangeDialog from "./components/change-dialog.vue";
  import addPerson from "./components/add-person.vue";
  import patientService from "@/api/patient";
  import {
    getUid
  } from "@/utils/runtime";
  import S from "@/utils/utils";
  import CLoading from "@/components/cLoading/loading.vue";
  export default {
    name: 'morning',

    components: {
      CLoading,
      ConfirmDialog,
      ChangeDialog,
      addPerson
    },
    props: {
      selectedDate: {
        type: String,
        default: '',
      }
    },
    data() {
      return {
        loading: false,
        priority_user_follow: [], // 重点用户跟进
        study_plan: '',
        remark: '',
        attendee_list: [],
        enableSubmit: false,
        reserve: {
          user_num: 0,
          user_list: [],
          staff_list: [],
          num: 0,
          doctor_consult_list: [{
              name: '初诊',
              num: 0
            },
            {
              name: '复诊',
              num: 0
            }
          ],
          service_list: [],
        },
        target_performance: {},

        addKey: '',
        addTitle: '',
        userList: [],
        staff_list: [],
        echoIds: [], // 回显用户
        echoTarget: '',
        addPersonVisible: false,
        searchLoading: false,
      };
    },
    computed: {
      attendee_text() {
        return this.attendee_list.map(item => item.name).join(', ')
      },
      dateFormat() {
        return S.dayjs(this.selectedDate).format('YYYY年MM月DD')
      },
      getWeekDay() {
        const weekCN = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
        return weekCN[S.dayjs(this.selectedDate).day()];
      },
      default_avatar () {
        return sex => {
          return S.defaultAvatar(sex)
        }
      },
    },
    watch: {},
    created() {},
    mounted() {
      this.getClinicMemberList()
      if (this.selectedDate) {
        this.getMeetingDailyMorning()
      }
    },
    methods: {
      submit() {
        if (this.attendee_list.length === 0) {
          uni.showToast({
            title: '请选择参会人',
            icon: 'none'
          });
          return
        }
        this.$refs.confirm.open();
      },
      submitMorning() {
        this.loading = true
        let params = {
          date: this.selectedDate,
          priority_user_follow: this.priority_user_follow,
          study_plan: this.study_plan,
          remark: this.remark,
          attendee_id_list: this.attendee_list.map(item => item.id) || []
        }
        MeetingService.submitDailyMorning(params).then(res => {
          uni.showToast({
            title: '提交成功',
            icon: 'none'
          });
          this.$emit('changePageTab', 'home')
        }).finally(() => this.loading = false)
      },
      back() {
        this.$emit('changePageTab', 'home')
      },
      changeTarget() {
        this.$refs.change.open();
      },
      getMeetingDailyMorning() {
        this.searchLoading = true
        let params = {
          date: this.selectedDate
        }
        MeetingService.getMeetingDailyMorning(params).then(res => {
          console.log("=>(morning.vue:216) res", res);
          this.attendee_list = res.attendee_list
          this.priority_user_follow = res.priority_user_follow
          this.remark = res.remark
          this.reserve = res.reserve
          this.study_plan = res.study_plan
          this.enableSubmit = res.submit_button === '1'
          this.target_performance = res.target_performance
        }).finally(() =>    this.searchLoading = false)
      },
      addUser(key) {
        this.addKey = key
        console.log('key', key)
        if (key === 'customer') {
          this.addTitle = '添加重点用户'
          this.getMeetingDailyUserList()
        } else if (key === 'staff') {
          this.addTitle = '添加参会人'
          this.userList = this.staff_list
          this.echoIds = this.attendee_list.map(item => item.id)
          console.log("=>(morning.vue:268) this.echoIds", this.echoIds);
          this.addPersonVisible = true
        }

      },
      getClinicMemberList() {
        patientService.getClinicMemberList().then(res => {
          res.list.forEach(item => {
            item.value = item.id
            item.text = item.name
          })
          this.staff_list = res.list
        })
      },
      addUserList(list) {
        if (this.addKey === 'customer') {
          this.priority_user_follow = list
        } else if (this.addKey === 'staff') {
          this.attendee_list = list
        }
      },
      getMeetingDailyUserList() {
        let params = {
          date: this.selectedDate
        }
        this.echoIds = this.priority_user_follow.map(item => item.value)
        MeetingService.getMeetingDailyUserList(params).then(res => {
          res.forEach(item => {
            item.text = item.name
            item.value = item.reserve_id
          })
          this.userList = res
          this.addPersonVisible = true

        })
      },
      getMeetingDailyTarget() {
        let params = {
          date: this.selectedDate
        }
        MeetingService.getMeetingDailyTarget(params).then(res => {
          this.target_performance = res
        })
      },

    },
  };
</script>
<style scoped lang="scss">
  .morning-wrap {
    position: relative;
    width: 100%;
    height: 100vh;
    overflow: hidden;
    //padding: 24rpx 24rpx 160rpx;
    padding: 24rpx 24rpx 0;

    .top-area {
      width: 100%;
      display: flex;
      justify-content: space-between;
      margin-bottom: 24rpx;

      //align-items: center;
      .back-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 160rpx;
        height: 64rpx;
        background: #ffffff;
        border-radius: 32rpx;
        font-weight: 400;
        font-size: 26rpx;
        color: #333333;

        .back-icon {
          width: 28rpx;
          height: 28rpx;
          margin-right: 10rpx;
        }
      }

      .meeting-info {
        display: flex;
        font-weight: 500;
        font-size: 26rpx;
        color: #333333;
        margin-left: 20rpx;

        .mr32 {
          margin-right: 32rpx;
        }
      }
    }

    .middle-area {
      width: 100%;
      display: flex;

      .left-content {
        min-width: 400rpx;
        width: 400rpx;
        flex-shrink: 0;
        display: flex;
        flex-direction: column;
        //gap: 24rpx;
        //margin-right: 20rpx;

        .card-box {
          position: relative;
          padding: 120rpx 40rpx 188rpx 32rpx; // 待处理
          background-color: #fff;
          border-radius: 8rpx 0 0 8rpx;
          height: 364rpx;
          box-sizing: border-box;
          border-right: 2rpx solid #F6F6F6;

          .card-title {
            font-weight: 500;
            font-size: 32rpx;
            color: #333333;
            margin-bottom: 24rpx;
          }

          .card-num {
            font-weight: 600;
            font-size: 44rpx;
            color: #333333;
            .card-unit{
              font-size: 32rpx;
            }
          }

          .card-img {
            position: absolute;
            right: 20rpx;
            bottom: 20rpx;
            width: 144rpx;
            height: 144rpx;
          }
        }
      }

      .right-content {
        //flex: 1;
        width: calc(100% - 416rpx);
        //width: calc(100% - 416rpx);

        .main-box {
          width: 100%;
          display: flex;
          gap: 20rpx;
          margin-bottom: 24rpx;
          max-height: 752rpx;

          .left-box {
            display: flex;
            flex-direction: column;
            //gap: 24rpx;
            min-width: calc(100% - 466rpx);
            width: calc(100% - 466rpx);
            //width: 200px;
            //flex: 1;

            .service-box {
              display: flex;
              min-height: 364rpx;
              max-height: 364rpx;

              .first-diagnosis {
                display: flex;
                align-items: center;
                width: 100%;
                padding: 32rpx 30rpx;
                background: #F4F7FD;
                border-radius: 8rpx;
                font-weight: 400;
                font-size: 28rpx;
                color: #75829E;
                margin-bottom: 20rpx;
              }

              .second-diagnosis {
                display: flex;
                align-items: center;
                width: 100%;
                padding: 32rpx 30rpx;
                background: #F4FBFD;
                border-radius: 8rpx;
                font-weight: 400;
                font-size: 28rpx;
                color: #6FA8B8;
              }

              .consult-icon {
                width: 52rpx;
                height: 52rpx;
                margin-right: 32rpx;
              }
            }
          }

          .right-box {
            min-width: 466rpx;
            width: 466rpx;
            position: relative;
            padding: 32rpx;
            background: #ffffff;
            border-radius: 8rpx;
            box-sizing: border-box;

            .right-title {
              font-weight: 600;
              font-size: 30rpx;
              color: #333333;
              margin-bottom: 190rpx;
            }

            .right-num {
              display: flex;
              justify-content: center;
              align-items: center;
              margin-bottom: 200rpx; // todo

              .price {
                font-weight: 600;
                font-size: 40rpx;
                color: #333333;
                margin-right: 16rpx;
              }

              .button-text {
                font-weight: 400;
                font-size: 24rpx;
                color: #155bd4;
                text-decoration-line: underline;
              }
            }

            .right-bottom-box {
              position: absolute;
              bottom: 24rpx;
              left: 24rpx;
              background: #f9fafb;
              border-radius: 8rpx;
              width: calc(100% - 48rpx);
              padding: 24rpx;

              .progress-box {
                display: flex;
                align-items: center;

                .progress-circle {
                  width: 40rpx;
                  height: 40rpx;
                  margin-right: 16rpx;
                }

                .progress-title {
                  font-weight: 400;
                  font-size: 26rpx;
                  color: #999999;
                }

                .progress-num {
                  font-weight: 500;
                  font-size: 28rpx;
                  color: #333333;
                }

                &:first-child {
                  margin-bottom: 24rpx;
                }
              }
            }
          }
        }

        .sub-box {
          display: flex;
          gap: 20rpx;
          height: 364rpx;

          .add-button {
            display: flex;
            align-items: center;
            font-weight: 400;
            font-size: 24rpx;
            color: #155BD4;

            .add-icon {
              width: 20rpx;
              height: 20rpx;
              margin-right: 10rpx;
            }
          }


        }



        .small-box {
          width: 436rpx;
          min-width: 436rpx;
          max-width: 436rpx;
        }


      }
    }

    .bottom-area {
      display: flex;
      background: #FFFFFF;
      border-radius: 8rpx;
      .common-box{
        padding: 32rpx 24rpx;
      }

      .bottom-title {
        min-width: fit-content;
      }

      .add-button {
        min-width: fit-content;
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 28rpx;
        color: #155BD4;

        .add-icon {
          width: 20rpx;
          height: 20rpx;
          margin-right: 10rpx;
        }
      }

      .input-box {
        display: flex;
      }
      .attendee_text{
        margin-right: 32rpx;
        height: 268rpx;
        overflow: auto;
        font-weight: 400;
        font-size: 26rpx;
        color: #333333;
      }
    }

    .fixed-area {
      position: fixed;
      bottom: 0;
      left: 120rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #fff;
      width: 100%;
      height: 150rpx;
    }

    .scroll-box {
      position: relative;
      width: 100%;

      .sub-scroll-box {
        width: 100%;
        display: flex;
        flex-wrap: nowrap;

        .customer-item {
          display: inline-flex;
          align-items: center;
          flex-shrink: 0;

          .customer-avatar {
            width: 64rpx;
            height: 64rpx;
            border: 1rpx solid #eeeeee;
            border-radius: 50%;
            margin-right: 16rpx;
            background-color: skyblue;
          }

          .customer-name {
            font-weight: 400;
            font-size: 24rpx;
            color: #333333;
            padding-right: 32rpx;
            border-right: 2rpx solid #e3e3e3;
            margin-right: 32rpx;
          }

          &:last-child {
            .customer-name {
              border: 0;
              margin-right: 0;
              padding-right: 0;
            }
          }
        }

        .appoint-item {
          display: inline-flex;
          align-items: center;
          flex-shrink: 0;
          padding: 10rpx 24rpx;
          font-weight: 400;
          font-size: 26rpx;
          color: #333333;
          background: #F5F6F8;
          border-radius: 8rpx;
          margin-right: 24rpx;
        }

      }

      .sub-scroll-box-wrap {
        width: 100%;
        height: 100%;
        display: flex;
        gap: 24rpx;
        flex-wrap: wrap;

        .service-item {
          background: #FFFFFF;
          border-radius: 8rpx;
          border: 2rpx solid #DCDFE6;
          padding: 10rpx 24rpx;
          font-weight: 400;
          font-size: 26rpx;
          color: #333333;
          height: 60rpx;
        }

        .important-customer-item {
          position: relative;
          display: flex;
          align-items: center;
          width: 100%;
          padding: 12rpx 24rpx;
          background: linear-gradient( 90deg, #F3F7FF 0%, rgba(245,246,248,0) 100%);
          font-weight: 400;
          font-size: 24rpx;
          color: #444444;
          max-height: 76rpx;

          .divide-line {
            font-weight: 400;
            font-size: 24rpx;
            color: #DCDFE6;
            margin: 0 12rpx;
          }

          &:before {
            position: absolute;
            top: 0;
            left: 0;
            content: '';
            display: inline-block;
            width: 4rpx;
            height: 100%;
            background-color: #155BD4;

          }
        }
      }
    }
  }

  .common-box {
    padding: 24rpx;
    background: #ffffff;
    //border-radius: 8rpx;
    width: 100%;

    .common-title {
      font-weight: 400;
      font-size: 26rpx;
      color: #666666;
      margin-bottom: 24rpx;
    }
  }

  .mb24 {
    margin-bottom: 24rpx;
  }

  .study-input{
    ::v-deep .is-input-border{
      border-radius: 8rpx 0 0 8rpx;
    }
    ::v-deep .uni-easyinput__content-textarea{
      height: 268rpx;
    }
  }
  .remark-input{
    ::v-deep .is-input-border{
      border-radius: 0 8rpx 8rpx 0;
    }
    ::v-deep .uni-easyinput__content-textarea{
      height: 268rpx;
    }
  }
  .empty{
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    font-weight: 400;
    font-size: 13px;
    color: #999999;
    height: 40%
  }
</style>
