<template>
  <view>
    <view class="circle-box" @click="change">
      <u-badge class="badge" numberType="overflow" max="99" :value="cartNums"></u-badge>
      <img class="circle-icon" src="https://static.rsjxx.com/image/2025/0604/203848_30803.png"></img>
      <view class="circle-text">去结算</view>
    </view>
  </view>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  name: 'cart-icon',
  components: {},
  mixins: [],
  data() {
    return {
    };
  },
  computed: {
    ...mapGetters('cart', {
      list: 'getGoodsList'
    }),
    cartNums () {
      return this.list.reduce((acc, item) => acc + Number(item.cart_num || 0), 0)
    },
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    change () {
      this.$emit('change')
    }
  },
};
</script>

<style lang="scss" scoped>
.circle-box {
  box-sizing: border-box;
  width: 212rpx;
  height: 96rpx;
  background: linear-gradient( 135deg, #2C7AFF 0%, #1640D8 100%);
  box-shadow: 0rpx 4rpx 12rpx 0rpx rgba(8,32,110,0.2);
  border-radius: 48rpx;
  position: fixed;
  right: 40rpx;
  bottom: 40rpx;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  .badge {
    position: absolute;
    top: -6rpx;
    right: 0rpx;
    min-width: 40rpx;
    height: 40rpx;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .circle-icon {
    width: 44rpx;
    height: 44rpx;
  }
  .circle-text {
    margin-left: 16rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #FFFFFF;
    line-height: 40rpx;
  }
}
</style>
