<template>
  <view class="shop-cart-box">
    <cus-drawer v-model="drawerVisible" :title="title" @on-open="onOpen" @on-close="onClose" :loading="confirmLoading">
      <view slot="content" class="content">
        <view class="sticky-box">
          <view style="display: flex; justify-content: center">
            <view v-if="current_user_info.uid" class="current-user">
              <view class="current-user-left">
                <view class="avatar-box">
                  <img
                    v-if="current_user_info.vip_info && current_user_info.vip_info.length && getVipIcon(current_user_info.vip_info)"
                    class="vip-icon" :src="getVipIcon(current_user_info.vip_info)" />
                  <img class="avatar" :src="current_user_info.avatar || default_avatar" />
                </view>

                <view class="user-info">
                  <view class="user-info-top">
                    <view class="user-info-name ecs">{{ current_user_info.real_name }}</view>
                    <view class="user-info-sex">
                      <span>{{ current_user_info.sex_text }}</span>
                      <span v-if="current_user_info.sex_text && current_user_info.age">｜</span>
                      <span>{{ current_user_info.age ? `${current_user_info.age}岁` : current_user_info.age }}</span>
                    </view>
                  </view>
                  <view class="user-info-mobile-box">
                    <view class="info-mobile">{{ current_user_info.mobile }}</view>
                    <view class="info-stage-mobile" v-if="current_user_info.show_staging_mobile == '1'">
                      <span class="stage-tag">暂存</span>
                      <span class="stage-mobile">{{ current_user_info.staging_mobile }}</span>
                    </view>
                  </view>
                </view>
              </view>

              <view class="delete-user-icon-box">
                <img
                  class="delete-user-icon"
                  @click="deleteUserInfo"
                  src="https://static.rsjxx.com/image/2025/0529/160357_34073.png"
                />
              </view>
            </view>
            <view v-else class="select-remote-box" >
              <user-select-remote @on-change="selectChange" />
            </view>
          </view>

          <view class="head-box">
            <view class="head-title">消费明细（{{ goods_list.length }}）</view>
            <a class="clear-btn" v-if="goods_list.length" @click="clearGoods">清空购物车</a>
          </view>
        </view>

        <uni-swipe-action ref="swipeAction" class="action-box" v-if="goods_list.length">
          <uni-swipe-action-item
            class="action-item"
            :threshold="0"
            v-for="(item, index) in goods_list"
          >
            <view slot="right" class="right-delete-box">
              <image class="delete-img" src="https://static.rsjxx.com/image/2025/0528/230336_99906.png" mode="aspectFit"></image>
              <view class="delete-text" @click="deleteGoods(item)">删除</view>
            </view>
            <view class="action-item-content">
              <view class="goods-img-box">
                <image class="goods-img" v-if="item.main_img" :src="item.main_img" @click="previewImage(item.main_img)" mode="aspectFit"></image>
              </view>
              <view class="goods-info">
                <view class="goods-info-name ecs ecs-2">{{ item.name }}</view>
                <view class="goods-info-tag" v-if="item.category_desc">{{ item.category_desc }}</view>
              </view>

              <view class="goods-action">
                <u-number-box class="number-input" v-model="item.cart_num" :min="1" :max="item.stock" @blur="blur($event, item)" @change="inputChange($event, item)" :step="1" integer inputWidth="80"
                              buttonSize="60"></u-number-box>
                <view class="goods-price">
                  <span class="price-unit">¥</span>
                  <span class="price-int">{{ getPrice(item.price)[0] }}</span>
                  <span class="price-dot">.{{ getPrice(item.price)[1] }}</span>
                </view>
              </view>
            </view>
          </uni-swipe-action-item>
        </uni-swipe-action>

        <!-- 空购物车 -->
        <view class="empty-wrapper" v-else>
          <image class="empty-img" src="https://static.rsjxx.com/image/2025/0529/104000_14166.png" mode="aspectFit"></image>
          <view class="empty-text">您还没有添加产品哦～</view>
          <u-button type="primary" class="empty-add-btn" color="#155BD4" @click="addGoods">
            <image class="add-img" src="https://static.rsjxx.com/image/2025/0529/104627_88701.png" mode="aspectFit"></image>
            <view class="add-text">去添加</view>
          </u-button>
        </view>
      </view>

      <view slot="footer" class="footer">
        <view class="total-price-box">
          <view class="total-num">共{{ cartNums }}件，</view>
          <view class="total-text">合计：</view>
          <view class="total-price">
            <span class="price-unit">¥</span>
            <span class="price-int">{{ getPrice(totalPrice)[0] }}</span>
            <span class="price-dot">.{{ getPrice(totalPrice)[1] }}</span>
          </view>
        </view>
        <view class="flex">
          <button class="ui-btn-drawer-base ui-btn-drawer-default" type="default"  @click="closeDrawer">取消</button>
          <button class="ui-btn-drawer-base ui-btn-drawer-primary ui-btn-distance-l" type="primary" :loading="confirmLoading" @click="confirm">确认收款</button>
        </view>
      </view>
    </cus-drawer>
    <!-- 支付 -->
    <pay-drawer v-model="payVisible" showOrderBtn :order_id="order_id" @close="refresh" @success="closeDrawer" @seeOrder="toOrder"></pay-drawer>
  </view>
</template>

<script>
import S from "@/utils/utils";
import {$operator} from "@/utils/operation";
import { mapGetters } from 'vuex'
import cusDrawer from '@/components/cus-drawer/index.vue'
import userSelectRemote from '@/components/user-select-remote/index.vue'
import productService from '@/api/product'
import payDrawer from '@/components/pay-drawer/index.vue'
import VIP980 from '@/static/image/order/vip_980.png'
import VIP9800 from '@/static/image/order/vip_9800.png'

export default {
  name: 'shop-cart',
  components: {
    cusDrawer,
    userSelectRemote,
    payDrawer
  },
  mixins: [],
  props: {
    value: {
      type: Boolean,
      default: () => false
    },
    title: {
      type: String,
      default: '下单用户'
    },
  },
  data() {
    return {
      confirmLoading: false,
      drawerVisible: false,
      options: [{
        text: '删除',
        style: {
          backgroundColor: '#E63E3E'
        }
      }],
      quantity: 1,
      current_user_info: {},
      payVisible: false,
      order_id: '',
      uid: '',
    };
  },
  computed: {
    default_avatar () {
      return S.defaultAvatar(this.current_user_info?.sex)
    },
    ...mapGetters('cart', {
      goods_list: 'getGoodsList'
    }),
    cartNums () {
      return this.goods_list.reduce((acc, item) => acc + Number(item.cart_num || 0), 0)
    },
    // 总价格
    totalPrice () {
      let price = 0
      this.goods_list.forEach(item => {
        let item_price = $operator.multiply(Number(item.cart_num || 0), Number(item.price || 0))
        price = $operator.add(price, item_price)
      })
      return price
    },
    getPrice () {
      return item => {
        return S.splitPrice(item)
      }
    },
    getVipIcon() {
      const typeEnum = {
        1: {
          desc: 'vip_980',
          url: 'https://static.rsjxx.com/image/2025/0607/161825_62294.png',
        },
        4: {
          desc: 'vip_298，榕粉会员',
          url: 'https://static.rsjxx.com/image/2025/0607/161825_62294.png',
        },
        // 3: {
        //   desc: 'vip_9800',
        //   url: 'https://static.rsjxx.com/image/2025/0607/161825_65262.png'
        // }
      };
      return vipInfo => {
        const user_type = vipInfo[vipInfo.length - 1].user_type;
        if (user_type in typeEnum) {
          return typeEnum[user_type].url
        }
        return '';
      };
    },
  },
  watch: {
    value (val) {
      if ( val ) {
        this.drawerVisible = true
      }else{
        this.drawerVisible = false
      }
    },
  },
  created() {},
  mounted() {},
  methods: {
    refresh () {
      this.$emit('refresh')
    },
    toOrder () {
      this.$store.commit('global/SET_QUERY', {
        menuId: '1',
        uid: this.uid,
        tabKey: '1',
      })
      this.$emit('changePageTab', 'detail')
    },
    // 补充change事件输入空，change事件不触发的问题
    blur ( $event, row ) {
      if ( Number($event.value || 0) == 0 && Number(row.cart_num || 0) !== 0 ) {
        this.$store.dispatch('cart/updateGoods', {
          type: 'change',
          row,
          num: $event.value
        })
      }
    },
    inputChange (value, row) {
      this.$store.dispatch('cart/updateGoods', {
        type: 'change',
        row,
        num: value.value
      })
    },
    clearGoods () {
      this.$store.commit('cart/CLEAR_GOODS_LIST')
    },
    deleteUserInfo() {
      this.current_user_info = {};
    },
    selectChange (item) {
      this.uid = item.uid
      this.current_user_info = item
    },
    addGoods () {
      this.$emit('on-add')
    },
    deleteGoods (item) {
      this.$store.dispatch('cart/deleteGoods', item.id)
      uni.showToast({
        title: '删除成功',
        icon: 'none'
      });
      this.resetSwipeAction()
    },
    resetSwipeAction() {
      this.$nextTick(() => {
        this.$refs.swipeAction?.closeAll();
        this.$refs.swipeAction?.resize();
      })
    },
    previewImage(imgUrl) {
      if (!imgUrl) return
      uni.previewImage({
        urls: [imgUrl]
      })
    },
    // 条件校验
    validData () {
      if (!+this.current_user_info.uid) {
        uni.showToast({
          title: '请选择下单用户',
          icon: 'none'
        })
        return false
      }

      let list = this.goods_list.filter(item => Number(item.cart_num || 0) == 0)
      if ( list[0]?.id) {
        uni.showToast({
          title: `【${list[0].name}】的数量不能为0`,
          icon: 'none'
        })
        return false
      }

      return true
    },
    handleListOf () {
      let obj = {}
      this.goods_list.forEach(item => {
        obj[item.clinic_goods_id] = item.cart_num
      })
      return obj || []
    },
    confirm () {
      if (this.validData()) {
        let params = {
          uid: this.current_user_info.uid,
          list_of: this.handleListOf()
        }
        this.confirmLoading = true
        productService.getOrderCreate(params).then( res => {
          this.payVisible = true
          this.closeDrawer()
          this.order_id = res.id
          this.$store.commit('cart/CLEAR_GOODS_LIST')
        } ).finally(() => this.confirmLoading = false)
      }
    },
    onOpen () {
    },
    onClose () {
      this.closeDrawer()
    },
    closeDrawer () {
      this.$emit('input', false)
      this.clearData()
    },
    clearData () {
      this.current_user_info = {}
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  height: 100%;
  .sticky-box {
    position: sticky;
    top: 0rpx;
    z-index: 2;
    background: #fff;

    .current-user {
      box-sizing: border-box;
      margin-top: 4rpx;
      height: 120rpx;
      background: #FFFFFF;
      box-shadow: 0rpx 4rpx 12rpx 0rpx rgba(0,0,0,0.1);
      border-radius: 8rpx;
      padding: 16rpx 32rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .current-user-left {
        width: 720rpx;
        max-width: 720rpx;
        display: flex;
        align-items: center;

        .avatar-box {
          width: 72rpx;
          height: 72rpx;
          border-radius: 50%;
          position: relative;
          box-sizing: content-box;
          //border: 2rpx solid #EBEDF0;
          //background: #EBEDF0;
          display: flex;
          align-items: center;
          justify-content: center;
          .vip-icon {
            width: 68rpx;
            min-width: 60rpx;
            height: 28rpx;
            position: absolute;
            bottom: -10rpx;
            left: 0;
          }
          .avatar {
            width: 72rpx;
            min-width: 72rpx;
            height: 72rpx;
            border-radius: 50%;
          }
        }

        .user-info {
          margin-left: 20rpx;

          .user-info-top {
            display: flex;
            align-items: center;
            .user-info-name {
              font-weight: 600;
              font-size: 32rpx;
              color: #303133;
              max-width: 480rpx;
              //line-height: 28rpx;
            }

            .user-info-sex {
              margin-left: 24rpx;
              font-weight: 400;
              font-size: 24rpx;
              color: #909399;
              //line-height: 36rpx;
            }
          }

          .user-info-mobile-box {
            display: flex;
            align-items: center;
            margin-top: 8rpx;

            .info-mobile {
              font-weight: 400;
              font-size: 26rpx;
              color: #303133;
              line-height: 40rpx;
            }

            .info-stage-mobile {
              margin-left: 20rpx;
              font-weight: 400;
              font-size: 26rpx;
              color: #909399;
              line-height: 40rpx;
              display: flex;
              align-items: center;

              .stage-tag {
                background: #FFF3DF;
                border-radius: 4rpx;
                padding: 2rpx 8rpx;
                font-weight: 400;
                font-size: 24rpx;
                color: #FFA300;
                line-height: 36rpx;
                min-width: fit-content;
              }

              .stage-mobile {
                margin-left: 12rpx;
                font-weight: 400;
                font-size: 26rpx;
                color: #909399;
                line-height: 40rpx;
              }
            }
          }
        }
      }
      .delete-user-icon {
        width: 32rpx;
        height: 32rpx;
        cursor: pointer;
      }
    }

    .select-remote-box {
      display: flex;
      align-items: center;
      height: 124rpx;
    }

    .head-box {
      margin-top: 20rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-bottom: 24rpx;

      .head-title {
        font-weight: 600;
        font-size: 32rpx;
        color: #333333;
        line-height: 48rpx;
      }
      .clear-btn {
        font-weight: 400;
        font-size: 24rpx;
        color: #155BD4;
        line-height: 36rpx;
      }
    }
  }

  .action-item {
    &:first-child {
      .action-item-content {
        padding-top: 0rpx !important;
      }
    }
    border-bottom: 2rpx solid #EBEDF0;
    .action-item-content {
      display: flex;
      align-items: center;
      padding:24rpx 0rpx;
      .goods-img-box {
        box-sizing: border-box;
        width: 160rpx;
        min-width: 160rpx;
        height: 160rpx;
        background: #F5F6F8;
        border-radius: 8rpx;
        .goods-img {
          max-height: 100%;
          max-width: 100%;
        }
      }

      .goods-info {
        margin: 0rpx 24rpx;
        flex: 1;
        display: flex;
        align-items: center;
        .goods-info-name {
          font-weight: 600;
          font-size: 30rpx;
          color: #333333;
          line-height: 44rpx;
        }
        .goods-info-tag {
          min-width: fit-content;
          margin-left: 8rpx;
          border-radius: 4rpx;
          border: 2rpx solid #155BD4;
          display: flex;
          justify-content: center;
          align-items: center;
          font-weight: 400;
          font-size: 22rpx;
          color: #155BD4;
          line-height: 32rpx;
          padding: 0 8rpx;
          height: 40rpx;
          box-sizing: border-box;
        }
      }

      .goods-action {
        display: flex;
        align-items: center;
        padding-right: 32rpx;
        min-width: 480rpx;
        .number-input {
          margin-right: 48rpx;
        }
        .goods-price {
          margin-top: 8rpx;
          font-weight: 600;
          font-size: 24rpx;
          color: #EE3838;
          line-height: 44rpx;
          display: flex;
          align-items: baseline;
          .price-unit {
            font-size: 24rpx;
            margin-right: 4rpx;
            position: relative;
          }
          .price-int {
            font-size: 32rpx;
          }
          .price-dot {
            font-size: 24rpx;
            position: relative;
          }
        }
      }
    }
  }

  .empty-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .empty-img {
      width: 402rpx;
      min-width: 402rpx;
      height: 400rpx;
    }
    .empty-text {
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      line-height: 34rpx;
    }
    .empty-add-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      max-width: fit-content;
      height: 64rpx;
      background: #155BD4;
      border-radius: 32rpx;
      margin-top: 32rpx;
      .add-img {
        width: 24rpx;
        min-width: 24rpx;
        height: 24rpx;
      }
      .add-text {
        margin-left: 16rpx;
        font-weight: 400;
        font-size: 26rpx;
        color: #FFFFFF;
        line-height: 36rpx;
      }
    }
  }
}
.ml20 {
  margin-left: 20rpx;
}
.right-delete-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 128rpx;
  background: #E63E3E;
  .delete-img {
    width: 32rpx;
    height: 32rpx;
  }
  .delete-text {
    margin-top: 16rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #FFFFFF;
    line-height: 32rpx;
  }
}

.footer {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .total-price-box {
    display: flex;
    align-items: baseline;
    .total-num {
      font-weight: 400;
      font-size: 26rpx;
      color: #999999;
      line-height: 40rpx;
    }
    .total-text {
      font-weight: 400;
      font-size: 26rpx;
      color: #333333;
      line-height: 40rpx;
    }
    .total-price {
      font-weight: 600;
      font-size: 24rpx;
      color: #EE3838;
      line-height: 44rpx;
      display: flex;
      align-items: baseline;
      .price-unit {
        font-size: 28rpx;
        margin-right: 4rpx;
        position: relative;
      }
      .price-int {
        font-size: 40rpx;
      }
      .price-dot {
        font-size: 28rpx;
        position: relative;
      }
    }
  }
}
</style>
