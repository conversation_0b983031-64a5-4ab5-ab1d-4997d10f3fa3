<template>
  <view class="product-wrapper">
    <scroll-view :scroll-y="true" v-if="list.length" style="height: calc(100vh - 64rpx)" scroll-with-animation>
      <view class="product-box" v-for="(product_item, product_index) in list" :key="'product'+product_index">
        <view class="product-head">
          <image class="product-logo" :src="product_item.img_url" v-if="product_item.img_url" mode="aspectFit"></image>
          <view class="product-title">{{ product_item.title }}</view>
        </view>

        <view class="type-box" v-for="(type_item, type_index) in product_item.sub_menus" :key="'type'+type_index">
          <u-divider :text="type_item.title" textPosition="left" dashed textSize="24" :hairline="false"></u-divider>
          <view class="type-product-box">
            <view class="product-item" v-for="(item, index) in type_item.items" :key="index" @click="seeDetail(item)">
              <view class="img-box">
                <image class="item-img" :src="item.main_img + '-B.w300'" mode="aspectFit"></image>
              </view>
              <view class="item-info">
                <view class="item-name ecs">{{ item.name }}</view>
                <view class="item-desc ecs ecs-2">{{ item.ai_detail && item.ai_detail.intro_text }}</view>
                <view class="item-price">
                  <span class="price-unit">¥</span>
                  <span class="price-int">{{ getPrice(item.price)[0] }}</span>
                  <span class="price-dot">.{{ getPrice(item.price)[1] }}</span>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
    <u-empty
      v-if="!list.length && !searchLoading"
      mode="car"
      width="400"
      height="400"
      textSize="26"
      style="height: 100%"
      icon="https://static.rsjxx.com/image/2025/0604/210007_84414.png-B.w300">
    </u-empty>
    <c-loading :show="searchLoading" />
    <cart-icon @change="cartChange"  v-if="isShowIcon && !searchLoading"></cart-icon>
    <!-- 产品详情 -->
    <product-detail v-model="detailVisible" :row="current_row"></product-detail>
    <!-- 购物车 -->
    <shop-cart ref="shopCar" v-model="shopCartVisible" @on-add="addGoods" @refresh="init"></shop-cart>
  </view>
</template>

<script>
import productService from '@/api/product'
import productDetail from './components/product-detail.vue'
import shopCart from './components/shop-cart.vue'
import cartIcon from './components/cart-icon.vue'
import S from "@/utils/utils";
import CLoading from "../../components/cLoading/loading.vue";
export default {
  name: 'index',
  components: {
    CLoading,
    productDetail,
    shopCart,
    cartIcon
  },
  mixins: [],
  data() {
    return {
      detailVisible: false,
      shopCartVisible: false,
      current_row: {},
      list: [],
      searchLoading: false,
    };
  },
  computed: {
    getPrice () {
      return item => {
        return S.splitPrice(item)
      }
    },
    // 是否展示去结算的按钮
    isShowIcon () {
      return !this.shopCartVisible && !this.$refs?.shopCar?.payVisible
    },
  },
  watch: {},
  created() {
    this.init()
  },
  mounted() {},
  methods: {
    init () {
      this.getList()
    },
    getList () {
      this.searchLoading = true
      productService.getGoodsPlatGoods().then( res => {
        this.list = res.list || []
      } ).finally(() => {
        this.searchLoading = false
      })
    },
    // 去添加
    addGoods () {
      this.shopCartVisible = false
    },
    // 去结算
    cartChange () {
      this.detailVisible = false
      this.shopCartVisible = true
    },
    // 查看详情
    seeDetail (row) {
      this.detailVisible = true
      this.current_row = row
    },
    // previewImage(imgUrl) {
    //   if (!imgUrl) return
    //   uni.previewImage({
    //     urls: [imgUrl]
    //   })
    // },
  },
};
</script>

<style lang="scss" scoped>
.product-wrapper {
  background: #fff;
  padding: 32rpx;
  overflow-y: auto;
  height: 100vh;

  .product-box {
    padding-bottom: 32rpx;
    .product-head {
      display: flex;
      align-items: center;
      .product-logo {
        width: 36rpx;
        min-width: 36rpx;
        height: 36rpx;
      }
      .product-title {
        margin-left: 16rpx;
        font-weight: 600;
        font-size: 32rpx;
        color: #333333;
        line-height: 28rpx;
      }
    }

    .type-box {
      margin-top: 32rpx;

      .type-product-box {
        display: flex;
        flex-wrap: wrap;

        .product-item {
          &:nth-child(3n-1) {
            margin: 0 0.5%;
          }
          //width: 321px;
          overflow: hidden;
          width: 33%;
          height: 240rpx;
          //margin-right: 34rpx;
          background: #FFFFFF;
          box-shadow: 4rpx 4rpx 12rpx 0rpx rgba(0,0,0,0.08);
          border-radius: 8rpx;
          padding: 8rpx;
          box-sizing: border-box;
          margin-bottom: 24rpx;
          display: flex;
          .img-box {
            width: 224rpx;
            min-width: 224rpx;
            height: 224rpx;
            background: #F5F6F8;
            border-radius: 8rpx 8rpx 0rpx 0rpx;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
            box-sizing: border-box;
            padding: 32rpx;
            .item-img {
              max-width: 100%;
              max-height: 100%;
              object-fit: contain;
            }
          }
          .item-info {
            padding: 0 24rpx 0rpx;

            .item-name {
              margin-top: 24rpx;
              font-weight: 600;
              font-size: 30rpx;
              color: #333333;
              line-height: 44rpx;
            }
            .item-desc {
              margin-top: 8rpx;
              font-weight: 400;
              font-size: 24rpx;
              color: #999999;
              line-height: 36rpx;
            }
            .item-price {
              margin-top: 8rpx;
              font-weight: 600;
              font-size: 24rpx;
              color: #EE3838;
              line-height: 44rpx;
              display: flex;
              align-items: baseline;
              .price-unit {
                font-size: 24rpx;
                margin-right: 4rpx;
                position: relative;
              }
              .price-int {
                font-size: 32rpx;
              }
              .price-dot {
                font-size: 24rpx;
                position: relative;
              }
            }
          }
        }
      }
    }
  }
}

</style>
