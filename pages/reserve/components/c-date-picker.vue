<script>
import dayjs from "dayjs";

/**
 * 自定义日期选择器组件
 *
 * 主要功能：
 * 1. 支持日期和时间选择（date/time-h/time-m/time-s四种模式）
 * 2. 支持startDate限制，隐藏指定日期之前的所有日期
 * 3. 解决了原生picker-view在动态数据更新时的同步问题
 *
 * 核心特性：
 * - startDate限制：当设置startDate时，只显示该日期及之后的可选日期
 * - 智能月份过滤：同年同月只显示startDate当天及之后，未来月份显示全部日期
 * - 视图同步：通过响应式key确保picker-view与数据完全同步
 * - 第一次操作修复：解决了第一次切换月份时渲染不同步的问题
 *
 * 使用示例：
 * <c-date-picker
 *   v-model="showPicker"
 *   :date="selectedDate"
 *   :start-date="dayjs().format('YYYY-MM-DD')"
 *   @on-ok="handleDateSelect"
 * />
 */
export default {
  name: 'c-date-picker',
  props: {
    // 控制弹窗显示/隐藏的双向绑定值
    value: Boolean,

    // 初始日期值
    date: {
      type: [Number, String],
      default: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    },

    // 日期选择器类型
    // date: YYYY-MM-DD
    // time-h: YYYY-MM-DD HH
    // time-m: YYYY-MM-DD HH:mm
    // time-s: YYYY-MM-DD HH:mm:ss
    type: {
      type: String,
      default: 'date',
    },

    // 弹窗标题
    title: {
      type: String,
      default: '选择时间',
    },

    // 最早可选日期限制
    // 当设置此值时，该日期之前的所有日期都将被隐藏，无法选择
    // 例如：startDate="2025-06-09"，则6月只能选择9号及之后的日期
    // 切换到7月时，可以选择1号开始的所有日期
    startDate: {
      type: [String, Date],
      default: null, // 默认为空，无日期限制
    }
  },
  data() {
    return {
      show: false,
      years: [],
      months: [],
      days: [],
      hours: [],
      minutes: [],
      seconds: [],
      selectedYear: 0,
      selectedMonth: 0,
      selectedDay: 0,
      selectedHour: 0,
      selectedMinute: 0,
      selectedSecond: 0,
      indicatorStyle: 'height: 80rpx;',

      // 缓存上一次的picker-view选中值，用于检测变化
      previousValues: null,

      // picker-view的响应式key，用于强制重新渲染
      // 每次更新天数列表时递增，确保视图与数据同步
      // 这是解决第一次操作时渲染不同步问题的关键
      pickerKey: 0
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(flag) {
        if (flag) {
          this.$nextTick(() => {
            if (typeof this.$refs.cDatePickPop?.open !== 'function') return
            this.$refs.cDatePickPop.open()
          })
          return
        }
        this.$nextTick(() => {
          if (typeof this.$refs.cDatePickPop?.close !== 'function') return
          this.$refs.cDatePickPop.close()
        })
      }
    },
    date: {
      handler(val) {
        this.initValue();
      },
      immediate: true
    },
    startDate: {
      handler(val) {
        // 当startDate变化时，重新初始化所有选择器数据
        this.updateYears();
        this.updateMonths();
        this.updateDays();

        // 确保选中的年份、月份、日期都有效
        if (this.selectedYear >= this.years.length) {
          this.selectedYear = 0;
        }
        if (this.selectedMonth >= this.months.length) {
          this.selectedMonth = 0;
        }
        if (this.selectedDay >= this.days.length) {
          this.selectedDay = 0;
        }
      },
      immediate: true
    }
  },
  created() {
    this.initPicker();
  },
  methods: {
    // 初始化选择器数据
    initPicker() {
      this.updateYears();
      this.updateMonths();

      // 生成小时列表
      this.hours = [];
      for (let i = 0; i < 24; i++) {
        this.hours.push(i + '时');
      }

      // 生成分钟列表
      this.minutes = [];
      for (let i = 0; i < 60; i++) {
        this.minutes.push(i + '分');
      }

      // 生成秒列表
      this.seconds = [];
      for (let i = 0; i < 60; i++) {
        this.seconds.push(i + '秒');
      }

      this.updateDays();
    },

    // 更新年份列表（根据startDate限制）
    updateYears() {
      const currentYear = dayjs().year();
      this.years = [];

      if (this.startDate) {
        // 有startDate限制时：只显示startDate年份及之后的年份
        const startYear = dayjs(this.startDate).year();
        for (let i = startYear; i <= currentYear + 10; i++) {
          this.years.push(i + '年');
        }
      } else {
        // 无startDate限制时：显示当前年份前后10年
        for (let i = currentYear - 10; i <= currentYear + 10; i++) {
          this.years.push(i + '年');
        }
      }
    },

    // 更新月份列表（根据选中年份和startDate限制）
    updateMonths() {
      this.months = [];

      if (this.startDate) {
        const startDateObj = dayjs(this.startDate);
        const selectedYearStr = this.years[this.selectedYear] || '';
        const selectedYear = parseInt(selectedYearStr) || dayjs().year();

        if (selectedYear === startDateObj.year()) {
          // 选中年份与startDate同年：只显示startDate月份及之后的月份
          // 例如：startDate是2025-06-09，则显示["6月", "7月", "8月", ..., "12月"]
          const startMonth = startDateObj.month() + 1; // dayjs月份从0开始
          for (let i = startMonth; i <= 12; i++) {
            this.months.push(i + '月');
          }
        } else if (selectedYear > startDateObj.year()) {
          // 选中年份在startDate之后：显示全年所有月份
          // 例如：startDate是2025-06-09，选中2026年，则显示["1月", "2月", ..., "12月"]
          for (let i = 1; i <= 12; i++) {
            this.months.push(i + '月');
          }
        }
        // 注意：选中年份在startDate之前的情况不会发生，因为年份列表已经过滤了
      } else {
        // 无startDate限制时：显示全年所有月份
        for (let i = 1; i <= 12; i++) {
          this.months.push(i + '月');
        }
      }
    },

    // 更新天数列表（根据年月）
    // 支持两种调用方式：
    // 1. updateDays() - 使用组件当前状态的年月
    // 2. updateDays(year, month) - 使用指定的年月（用于解决第一次切换月份时的同步问题）
    updateDays(targetYear = null, targetMonth = null) {
      let year, month;

      if (targetYear !== null && targetMonth !== null) {
        // 使用传入的参数值（优先级更高，确保数据准确性）
        year = targetYear;
        month = targetMonth;
      } else {
        // 从组件状态获取当前选中的年份和月份
        const yearStr = this.years[this.selectedYear] || '';
        const monthStr = this.months[this.selectedMonth] || '';

        // 提取数字部分
        year = parseInt(yearStr) || dayjs().year();
        month = parseInt(monthStr) || 1;
      }

      // 使用dayjs计算当月天数
      const daysInMonth = dayjs(`${year}-${month.toString().padStart(2, '0')}-01`).daysInMonth();

      // 生成天数列表
      this.days = [];

      // 如果有startDate限制，需要过滤日期
      if (this.startDate) {
        const startDateObj = dayjs(this.startDate);
        const currentMonthObj = dayjs(`${year}-${month.toString().padStart(2, '0')}-01`);

        // 如果当前月份在startDate之前，则不显示任何日期
        if (currentMonthObj.isBefore(startDateObj, 'month')) {
          this.days = [];
        } else if (currentMonthObj.isSame(startDateObj, 'month') && year === startDateObj.year()) {
          // 如果是同一年同一月，只显示startDate当天及之后的日期
          const startDay = startDateObj.date();
          for (let i = startDay; i <= daysInMonth; i++) {
            this.days.push(i + '日');
          }
        } else {
          // 如果当前月份在startDate之后，或者是未来年份的同月，显示所有日期
          for (let i = 1; i <= daysInMonth; i++) {
            this.days.push(i + '日');
          }
        }
      } else {
        // 没有startDate限制时，按原逻辑显示所有日期
        for (let i = 1; i <= daysInMonth; i++) {
          this.days.push(i + '日');
        }
      }

      // 确保选中的日期索引有效
      if (this.selectedDay >= this.days.length) {
        this.selectedDay = 0; // 如果日期超出范围，设置为第一个可选日期（索引为0）
      }

      // 通过更新key强制picker-view重新渲染，确保视图与数据同步
      // 这是解决第一次操作时渲染不同步问题的关键
      this.pickerKey++;
    },

    // 初始化选中值
    initValue() {
      const date = dayjs(this.date);
      const currentYear = date.year();
      const currentMonth = date.month() + 1; // dayjs月份从0开始
      const currentDay = date.date();
      const currentHour = date.hour();
      const currentMinute = date.minute();
      const currentSecond = date.second();

      // 设置年份索引
      const yearIndex = this.years.findIndex(year => parseInt(year) === currentYear);
      this.selectedYear = yearIndex >= 0 ? yearIndex : 0; // 如果找不到，设置为第一个可选年份

      // 更新月份列表（基于选中的年份）
      this.updateMonths();

      // 设置月份索引
      const monthIndex = this.months.findIndex(month => parseInt(month) === currentMonth);
      this.selectedMonth = monthIndex >= 0 ? monthIndex : 0; // 如果找不到，设置为第一个可选月份

      // 更新天数列表
      this.updateDays();

      // 设置日期索引
      const dayIndex = this.days.findIndex(day => parseInt(day) === currentDay);
      this.selectedDay = dayIndex >= 0 ? dayIndex : 0;

      // 设置时间索引
      this.selectedHour = currentHour;
      this.selectedMinute = currentMinute;
      this.selectedSecond = currentSecond;
    },

    // 处理picker-view选择器变化事件
    // 这是解决startDate限制下日期选择问题的核心方法
    handlePickerChange(e) {
      const values = e.detail.value;

      // 检测哪一列发生了变化
      let monthChanged = false;
      let yearChanged = false;

      if (!this.previousValues) {
        // 第一次操作时，初始化缓存数据
        this.previousValues = [...values];
        // 第一次操作时强制触发月份变化处理，确保日期列表正确更新
        // 这是修复"第一次切换月份无效"问题的关键
        monthChanged = true;
      } else {
        // 检测年份是否变化
        if (this.previousValues[0] !== values[0]) {
          yearChanged = true;
        }

        // 检测月份是否变化
        if (this.previousValues[1] !== values[1]) {
          monthChanged = true;
        }
      }

      // 根据组件类型更新对应的选中值
      // values数组对应：[年份索引, 月份索引, 日期索引, 小时索引, 分钟索引, 秒索引]
      switch(this.type) {
        case 'date':
          this.selectedYear = values[0];
          this.selectedMonth = values[1];
          this.selectedDay = values[2];
          break;
        case 'time-h':
          this.selectedYear = values[0];
          this.selectedMonth = values[1];
          this.selectedDay = values[2];
          this.selectedHour = values[3];
          break;
        case 'time-m':
          this.selectedYear = values[0];
          this.selectedMonth = values[1];
          this.selectedDay = values[2];
          this.selectedHour = values[3];
          this.selectedMinute = values[4];
          break;
        case 'time-s':
          this.selectedYear = values[0];
          this.selectedMonth = values[1];
          this.selectedDay = values[2];
          this.selectedHour = values[3];
          this.selectedMinute = values[4];
          this.selectedSecond = values[5];
          break;
      }

      // 根据变化类型执行相应的更新逻辑
      if (yearChanged) {
        // 年份变化时：需要重新生成月份列表，然后更新天数列表
        this.updateMonths();

        // 确保选中的月份索引在新的月份列表范围内
        if (this.selectedMonth >= this.months.length) {
          this.selectedMonth = 0; // 设置为第一个可选月份
        }

        // 使用常规方式更新天数（因为月份列表已经更新）
        this.updateDays();
      } else if (monthChanged) {
        // 月份变化时：直接计算实际月份值来更新天数列表
        // 这里不能依赖this.months数组，因为可能存在同步问题

        const yearStr = this.years[this.selectedYear] || '';
        const year = parseInt(yearStr) || dayjs().year();

        // 计算实际的月份值（考虑startDate的限制）
        let actualMonth;
        if (this.startDate) {
          const startDateObj = dayjs(this.startDate);
          if (year === startDateObj.year()) {
            // 同一年：实际月份 = startDate月份 + 选中的月份索引
            // 例如：startDate是6月9号，months数组是["6月","7月","8月"...]
            // 用户选择索引1（7月），实际月份 = 6 + 1 = 7
            const startMonth = startDateObj.month() + 1; // dayjs月份从0开始
            actualMonth = startMonth + this.selectedMonth;
          } else {
            // 未来年份：实际月份 = 1 + 选中的月份索引
            actualMonth = this.selectedMonth + 1;
          }
        } else {
          // 无startDate限制：实际月份 = 1 + 选中的月份索引
          actualMonth = this.selectedMonth + 1;
        }

        // 使用计算出的准确年月值更新天数列表
        this.updateDays(year, actualMonth);
      }

      // 更新缓存数据
      this.previousValues = [...values];
    },

    // 关闭弹窗
    handleClose() {
      this.show = false;
      this.$emit('input', false);
    },

    // 确认选择
    handleConfirm() {
      // 获取选中的年月日时分秒
      const yearStr = this.years[this.selectedYear] || '';
      const monthStr = this.months[this.selectedMonth] || '';
      const dayStr = this.days[this.selectedDay] || '';

      // 提取数字部分
      const year = parseInt(yearStr) || dayjs().year();
      const month = parseInt(monthStr) || 1;
      const day = parseInt(dayStr) || 1;
      const hour = this.selectedHour;
      const minute = this.selectedMinute;
      const second = this.selectedSecond;

      // 格式化日期
      const monthFormatted = month.toString().padStart(2, '0');
      const dayFormatted = day.toString().padStart(2, '0');
      const hourFormatted = hour.toString().padStart(2, '0');
      const minuteFormatted = minute.toString().padStart(2, '0');
      const secondFormatted = second.toString().padStart(2, '0');

      let dateStr = '';
      switch(this.type) {
        case 'date':
          dateStr = `${year}-${monthFormatted}-${dayFormatted}`;
          break;
        case 'time-h':
          dateStr = `${year}-${monthFormatted}-${dayFormatted} ${hourFormatted}`;
          break;
        case 'time-m':
          dateStr = `${year}-${monthFormatted}-${dayFormatted} ${hourFormatted}:${minuteFormatted}`;
          break;
        case 'time-s':
          dateStr = `${year}-${monthFormatted}-${dayFormatted} ${hourFormatted}:${minuteFormatted}:${secondFormatted}`;
          break;
      }

      this.$emit('on-ok', dateStr);
      this.handleClose();
    },
    handlePopStatusChange(e) {
      this.show = e.show
      if (!e.show) {
        this.handleClose()
      }
    },
  }
}
</script>

<template>
  <uni-popup ref="cDatePickPop" type="center" :show="value" @change="handlePopStatusChange">
    <view class="picker-container">
      <!-- Header 部分 -->
      <view class="picker-header">
        <text class="title">{{ title }}</text>
        <text class="close-btn" @click="handleClose">×</text>
      </view>

      <!-- Body 部分 -->
      <view class="picker-body">
        <picker-view
          class="picker-view"
          :key="pickerKey"
          :indicator-style="indicatorStyle"
          @change="handlePickerChange"
          :value="[selectedYear, selectedMonth, selectedDay, selectedHour, selectedMinute, selectedSecond]"
        >
          <!-- 年份列 -->
          <picker-view-column>
            <view class="item" v-for="(item, index) in years" :key="'year-'+index">{{ item }}</view>
          </picker-view-column>

          <!-- 月份列 -->
          <picker-view-column>
            <view class="item" v-for="(item, index) in months" :key="'month-'+index">{{ item }}</view>
          </picker-view-column>

          <!-- 日期列 -->
          <picker-view-column>
            <view class="item" v-for="(item, index) in days" :key="'day-'+index">{{ item }}</view>
          </picker-view-column>

          <!-- 小时列 (仅在time-h, time-m, time-s模式下显示) -->
          <picker-view-column v-if="type !== 'date'">
            <view class="item" v-for="(item, index) in hours" :key="'hour-'+index">{{ item }}</view>
          </picker-view-column>

          <!-- 分钟列 (仅在time-m, time-s模式下显示) -->
          <picker-view-column v-if="type === 'time-m' || type === 'time-s'">
            <view class="item" v-for="(item, index) in minutes" :key="'minute-'+index">{{ item }}</view>
          </picker-view-column>

          <!-- 秒列 (仅在time-s模式下显示) -->
          <picker-view-column v-if="type === 'time-s'">
            <view class="item" v-for="(item, index) in seconds" :key="'second-'+index">{{ item }}</view>
          </picker-view-column>
        </picker-view>
      </view>

      <!-- Footer 部分 -->
      <view class="picker-footer">
        <button class="confirm-btn" @click="handleConfirm">确定</button>
      </view>
    </view>
  </uni-popup>
</template>

<style scoped lang="scss">
.picker-container {
  width: 800rpx;
  background-color: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.picker-header {
  position: relative;
  height: 88rpx;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #eee;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .title {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
  }

  .close-btn {
    position: absolute;
    right: 30rpx;
    font-size: 40rpx;
    color: #999;
    padding: 10rpx;
  }
}

.picker-body {
  height: 480rpx;
  padding: 20rpx 0;

  .picker-view {
    width: 100%;
    height: 100%;
  }

  .item {
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    font-size: 28rpx;
    color: #333;
  }
}

.picker-footer {
  padding: 20rpx 0 40rpx;
  display: flex;
  justify-content: center;

  .confirm-btn {
    width: 240rpx;
    height: 80rpx;
    line-height: 80rpx;
    background-color: #2979ff;
    color: #fff;
    font-size: 28rpx;
    border-radius: 40rpx;
  }
}
</style>
