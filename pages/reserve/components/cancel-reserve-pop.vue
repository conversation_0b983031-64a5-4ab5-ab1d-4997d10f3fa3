<script>
import ReserveService from "../../../api/reserve";

export default {
  name: 'cancel-reserve-pop',
  props: {
    value: Boolean,
    id: String,
    type: String,
  },
  data() {
    return {
      loading: false
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(flag) {
        if (flag) {
          this.$nextTick(() => {
            if (typeof this.$refs.cancelReservePop?.open !== 'function') return
            this.$refs.cancelReservePop.open()
          })
          return
        }
        this.$nextTick(() => {
          if (typeof this.$refs.cancelReservePop?.close !== 'function') return
          this.$refs.cancelReservePop.close()
        })
      }
    },
  },
  methods: {
    handleClose() {
      this.$emit('input', false)
    },
    cancelReserve() {
      if (this.loading) return
      this.loading = true
      ReserveService[this.type === '1' ? 'cancelUserReserve' : 'arrivalUserReserve']({
        id: this.id,
      }).then(() => {
        uni.showToast({
          title: this.type === '1' ? '取消成功' : '到店成功',
        })
        this.$emit('success')
        this.handleClose()
      }).catch(err => {
        uni.showToast({
          title: err.errmsg,
          type: 'error',
        })
      }).finally(() => {
        this.loading = false
      })
    },
    handlePopStatusChange(e) {
      if (!e.show) {
        this.handleClose()
      }
    }
  }
}
</script>

<template>
  <uni-popup ref="cancelReservePop" type="center" @change="handlePopStatusChange">
    <view class="cancel-reserve-container">
      <image class="logo" src="@/static/image/reserve/warning.png" mode="aspectFit"></image>
      <view class="title">{{ type === '1' ? '取消预约' : '确认到店'}}</view>
      <view class="desc">{{ type === '1' ? '您确定要取消预约吗？取消后将无法恢复' : '是否确认到店'}}</view>
      <view class="btn">
        <view class="ui-btn-drawer-base ui-btn-drawer-default" style="width: 248rpx" @click="handleClose">我再想想</view>
        <button
          style="width: 248rpx"
          class="ui-btn-drawer-base ui-btn-drawer-primary ui-btn-distance-l"
          :loading="loading"
          :loading-text="type === '1' ? '确认取消' : '确认到店'"
          @click="cancelReserve"
        >
          {{ type === '1' ? '确认取消' : '确认到店' }}
        </button>
      </view>
    </view>
  </uni-popup>
</template>

<style scoped lang="scss">
.cancel-reserve-container {
  width: 600rpx;
  height: 412rpx;
  background: #FFFFFF;
  border-radius: 8rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32rpx 40rpx 40rpx 40rpx;
  .logo {
    width: 64rpx;
    height: 64rpx;
    margin-bottom: 32rpx;
  }
  .title {
    height: 48rpx;
    font-weight: 500;
    font-size: 34rpx;
    color: #333333;
    line-height: 48rpx;
    margin-bottom: 32rpx;
  }
  .desc {
    height: 36px;
    font-size: 26rpx;
    color: #333333;
    line-height: 36rpx;
    margin-bottom: 48rpx;
  }
  .btn {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
