<template>
  <view>
    <cus-drawer v-model="drawerVisible" :width="440" :title="title" :maskClick="false" @on-open="onOpen" @on-close="onClose">
      <view slot="content" class="content">
        <view class="item-title">预约人信息</view>
        <view class="user-info-box">
          <view class="info-item">
            <view class="info-item-label">姓名</view>
            <view class="info-item-value">{{ detail.name || '-' }}</view>
          </view>

          <view class="info-item">
            <view class="info-item-label">手机</view>
            <view class="info-item-value">{{ detail.mobile || '-' }}</view>
          </view>
        </view>

        <view class="item-title">预约时间</view>
        <view class="reserve-time-box" @click="chooseTime">
          <view class="reserve-time-label">到店时间</view>
          <view class="reserve-time-value">
            <view class="reserve-time-text">
              <span>{{ reserve_info.reserve_date }}</span>
              <span style="margin-left: 8rpx;">{{ reserve_info.reserve_time }}</span>
            </view>
            <img class="more-img" src="https://static.rsjxx.com/image/2025/0610/161212_64018.png">
          </view>
        </view>

        <view class="item-title">预约服务</view>
        <view class="reserve-service-box" @click="chooseService">
          <view class="reserve-service-left">
            <view class="service-info">
              <view class="service-name">{{ service_item.serve_name }}</view>
              <view class="service-minute">{{ service_item.duration }}分钟</view>
            </view>

            <view class="physical-info">
              <img class="avatar" @click="previewImage(service_item.physical_avatar)" :src="service_item.physical_avatar || default_avatar">
              <view class="physical-name">{{ service_item.physical_name }}</view>
              <view class="physical-tag">{{ service_item.role_name }}</view>
              <view class="physical-level" v-if="service_item.level_name">{{ service_item.level_name }}</view>
            </view>
          </view>
          <view class="reserve-service-right">
            <view class="service-price">
              <span class="price-unit">¥</span>
              <span class="price-int">{{ getPrice(service_item.price)[0] }}</span>
              <span class="price-dot">.{{ getPrice(service_item.price)[1] }}</span>
            </view>
            <img class="more-img" src="https://static.rsjxx.com/image/2025/0610/161212_64018.png">
          </view>
        </view>

        <view class="remark-box">
          <view class="title">备注</view>
          <textarea v-model="reserve_info.remark" class="remark-area" placeholder-style="font-weight: 400;font-size: 26rpx;color: #BBBBBB;" maxlength="300" placeholder="请输入备注"/>
          <view class="word-limit">{{ reserve_info.remark && reserve_info.remark.length || 0 }}/300</view>
        </view>
      </view>

      <view slot="footer" class="footer">
        <button type="primary" class="ui-btn-drawer-base ui-btn-drawer-primary" :loading="confirmLoading" @click="confirm">确认修改</button>
      </view>
    </cus-drawer>
    <!-- 修改时间 -->
    <choose-time
      v-model="timeVisible"
      :reserve_date="reserve_info.reserve_date"
      :reserve_time="reserve_info.reserve_time"
      @choosed="timeChoosed"
    ></choose-time>
    <!-- 选择预约服务 -->
    <choose-service
      v-model="chooseServiceVisible"
      :reserve_date="reserve_info.reserve_date"
      :reserve_time="reserve_info.reserve_time"
      :service_item="service_item"
      @choosed="serviceChoosed"
    ></choose-service>
  </view>
</template>

<script>
import ReserveService from "@/api/reserve";
import cusDrawer from '@/components/cus-drawer/index.vue'
import chooseTime from '@/components/choose-time/time.vue'
import chooseService from '@/components/choose-service/index.vue'
import S from '@/utils/utils'
export default {
  name: 'edit-reserve-drawer',
  components: {
    cusDrawer,
    chooseTime,
    chooseService
  },
  mixins: [],
  props: {
    value: {
      type: Boolean,
      default: () => false
    },
    title: {
      type: String,
      default: '修改预约单'
    },
    // 预约信息
    detail: {
      type: Object,
      default: () => {}
    },
  },
  data() {
    return {
      drawerVisible: false,
      timeVisible: false,
      chooseServiceVisible: false,
      reserve_info: {
        deductible_flag: '', // 选择了是否使用卡券抵扣绑定的值
        reserve_time_flag: '', // 医生班次
        reserve_time_flag_text: '', // 医生班次时间描述
        out_sales_channel: '', // 外部预约渠道
        reserve_date: '',
        reserve_time: '',
        services: [{ goods_service_id: '', physio_id: '', physical_avatar: '', level_name: '', role_name: '' }],
        remark: '',
      },
      confirmLoading: false,
    };
  },
  computed: {
    // 理疗师/医生没有性别字段，默认展示男性头像
    default_avatar () {
      return sex => {
        return S.defaultAvatar(sex)
      }
    },
    service_item () {
      return this.reserve_info?.services[0] || {}
    },
    getPrice () {
      return item => {
        return S.splitPrice(item)
      }
    },
  },
  watch: {
    value (val) {
      if ( val ) {
        this.drawerVisible = true
        this.handleEchoData(this.detail)
      }else{
        this.drawerVisible = false
      }
    },
  },
  created() {},
  mounted() {},
  methods: {
    handleEchoData(data = {}) {
      let services = [];
      data?.services?.map(item => {
        services.push({
          serve_name: item.goods_service.name,
          physical_name: item.physio.name,
          physical_avatar: item.physio.avatar,
          goods_service_id: item.goods_service_id,
          physio_id: item.physio_id,
          duration: data.duration.minute,
          service_duration: data.service_duration.minute,
          prepare_duration: data.prepare_duration.minute,
          role_name: item.physio.role_name,
          level_name: item.physio.level_name,
          out_sales_channel_list: data.out_sales_channel_list, // 服务绑定了哪些外部渠道
          bind_sales_channel: data.bind_sales_channel, // 服务是否绑定了外部渠道
          price: item?.goods_service?.price,
          vip_price: item?.goods_service?.vip_price,
        });
      });
      this.reserve_info = {
        deductible_flag: data.deductible_flag,
        services: services || [],
        reserve_date: data.reserve_date,
        reserve_time: data.reserve_time,
        reserve_time_flag: data.reserve_time_flag,
        reserve_time_flag_text: data.reserve_time_flag_text,
        out_sales_channel: data.out_sales_channel,
        duration: {
          // hour: Number(data?.duration?.hour || 0) || null,
          minute: Number(data?.duration?.minute || 0) || null,
        },
        remark: data.remark
      };
    },
    timeChoosed ({ reserve_date, reserve_time }) {
      this.reserve_info.reserve_date = reserve_date
      this.reserve_info.reserve_time = reserve_time
    },
    serviceChoosed ({ physio, serve_info }) {
      // 整合信息进行同步
      let item = {
        // 以下同步服务信息
        serve_name: serve_info.name,
        goods_service_id: serve_info.id,
        price: serve_info.price,
        vip_price: serve_info.vip_price,

        duration: serve_info.duration, // 总时长
        service_duration: serve_info.service_duration, // 服务时长
        prepare_duration: serve_info.prepare_duration, // 准备时长
        out_goods_name: serve_info.out_goods_name,
        bind_sales_channel: serve_info.bind_sales_channel, // 服务是否绑定外部渠道
        out_sales_channel_list: serve_info.out_sales_channel_list, // 服务绑定了哪些服务

        // 以下同步理疗师信息
        physical_name: physio.name || '',
        physio_id: physio.id || '',
        physical_avatar: physio.avatar || '',
        role_name: physio.role_name || '',
        level_name: physio.level_name || '',
      };
      this.reserve_info.duration.minute = Number(serve_info.duration || 0) || null;

      let list = this.reserve_info.services;
      this.$set(list, 0, { ...list[0], ...item });
    },
    chooseService () {
      this.chooseServiceVisible = true
    },
    chooseTime () {
      this.timeVisible = true
    },
    previewImage(imgUrl) {
      if (!imgUrl) return
      uni.previewImage({
        urls: [imgUrl]
      })
    },
    confirm () {
      this.getReserveUpdate()
    },
    onOpen () {
    },
    onClose () {
      this.closeDrawer()
    },
    closeDrawer () {
      this.$emit('input', false)
      this.clearData()
    },
    clearData () {
      this.reserve_info.remark = ''
    },
    getReserveUpdate () {
      this.confirmLoading = true
      let service = this.reserve_info.services[0]
      let params = {
        uid: this.detail.uid,
        id: this.detail.id,
        duration: this.reserve_info.duration.minute,
        remark: this.reserve_info.remark,
        reserve_date: this.reserve_info.reserve_date,
        reserve_time: this.reserve_info.reserve_time,
        services: [
          {
            goods_service_id: service.goods_service_id,
            physio_id: service.physio_id,
            quantity: '1',
          }
        ]
      }
      ReserveService.getReserveUpdate(params).then( res => {
        this.$emit('updateSuccess')
        this.closeDrawer()
      } ).finally(() => {
        this.confirmLoading = false
      })
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  .item-title {
    font-weight: 600;
    font-size: 32rpx;
    color: #333333;
    line-height: 42rpx;
    margin-bottom: 24rpx;
  }
  // 预约人信息
  .user-info-box {
    padding: 0rpx 32rpx;
    background: #F9FAFB;
    border-radius: 8rpx;
    margin-bottom: 42rpx;
    .info-item {
      padding: 32rpx 0rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #EBEDF0;
      &:last-child {
        border-bottom: none;
      }
      .info-item-label {
        font-weight: 400;
        font-size: 28rpx;
        color: #999999;
        line-height: 40rpx;
      }
      .info-label-value {
        font-weight: 400;
        font-size: 28rpx;
        color: #333333;
        line-height: 40rpx;
      }
    }
  }

  // 预约时间
  .reserve-time-box {
    margin-bottom: 48rpx;
    box-sizing: border-box;
    margin-top: 24rpx;
    background: #FFFFFF;
    border-radius: 8rpx;
    border: 1px solid #DCDFE6;
    padding: 32rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .reserve-time-label {
      font-weight: 400;
      font-size: 28rpx;
      color: #999999;
      line-height: 40rpx;
    }
    .reserve-time-value {
      display: flex;
      align-items: center;

      .reserve-time-text {
        font-weight: 400;
        font-size: 28rpx;
        color: #333333;
        line-height: 40rpx;
      }
    }
  }

  // 预约服务
  .reserve-service-box {
    background: #F9FAFB;
    border-radius: 8rpx;
    padding: 24rpx 32rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .reserve-service-left {
      .service-info {
        display: flex;
        align-items: center;
        .service-name {
          font-weight: 500;
          font-size: 28rpx;
          color: #333333;
          line-height: 40rpx;
        }
        .service-minute {
          border-radius: 4rpx;
          border: 1rpx solid #DCDFE6;
          padding: 0 8rpx;
          font-weight: 400;
          font-size: 22rpx;
          color: #666666;
          line-height: 32rpx;
          min-width: fit-content;
          margin-left: 8rpx;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }

      .physical-info {
        margin-top: 24rpx;
        display: flex;
        align-items: center;
        .avatar {
          width: 40rpx;
          min-width: 40rpx;
          height: 40rpx;
          border-radius: 50%;
          object-fit: cover;
        }
        .physical-name {
          margin-left: 8rpx;
          font-weight: 400;
          font-size: 24rpx;
          color: #444444;
          line-height: 36rpx;
        }
        .physical-tag {
          border: 1px solid #155BD4;
          min-width: fit-content;
          padding: 0 8rpx;
          height: 32rpx;
          margin-left: 8rpx;
          border-radius: 4rpx;
          font-weight: 400;
          font-size: 22rpx;
          color: #155BD4;
          line-height: 32rpx;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .physical-level {
          margin-left: 8rpx;
          min-width: 40rpx;
          height: 32rpx;
          border-radius: 4rpx;
          border: 1px solid #DCDFE6;
          font-weight: 400;
          font-size: 22rpx;
          color: #666666;
          line-height: 32rpx;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
    .reserve-service-right {
      margin-left: 40rpx;
      display: flex;
      align-items: center;
      .service-price {
        font-weight: 600;
        font-size: 24rpx;
        color: #EE3838;
        line-height: 44rpx;
        display: flex;
        align-items: baseline;
        .price-unit {
          font-size: 24rpx;
          margin-right: 4rpx;
          position: relative;
        }
        .price-int {
          font-size: 32rpx;
        }
        .price-dot {
          font-size: 24rpx;
          position: relative;
        }
      }
    }
  }

  // 备注
  .remark-box {
    position: relative;
    .title {
      margin: 48rpx 0rpx 24rpx 0rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: #333333;
      line-height: 40rpx;
    }

    .remark-area {
      font-size: 28rpx;
      height: 360rpx;
      background: #FFFFFF;
      border-radius: 8rpx;
      border: 1px solid #DCDFE6;
      width: inherit;
      padding: 20rpx 24rpx 60rpx 24rpx;
    }

    .word-limit {
      position: absolute;
      right: 24rpx;
      bottom: 20rpx;
      font-weight: 400;
      font-size: 26rpx;
      color: #BBBBBB;
      line-height: 36rpx;
    }
  }
}
.footer {
  width: 100%;
}
.more-img {
  width: 28rpx;
  min-width: 28rpx;
  height: 28rpx;
  margin-left: 16rpx;
}
</style>
