<template>
  <view>
    <view class="reserve-detail" v-if="show">
      <view class="detail-title">预约详情</view>
      <scroll-view class="detail-scroll" :scroll-y="true">
        <view class="detail-body">

          <view class="detail-item detail-item-viewider">
            <view class="label">预约用户</view>
            <view class="text" @click.stop="seeUserInfo">
              <view class="reserve-user-box">
                <view class="left">
                  <img :src="reserve_user.avatar || default_avatar(reserve_user.sex)" class="avatar" />
                  <view class="info-box">
                    <view class="info-top">
                      <view class="name">{{ reserve_user.name }}</view>
                      <view class="sex">{{ reserve_user.sex_desc }}</view>
                      <view class="line" v-if="reserve_user.sex_desc && reserve_user.age"></view>
                      <view class="age">{{ reserve_user.age ? `${reserve_user.age}岁` : '' }}</view>
                    </view>
                    <view class="mobile">{{ reserve_user.mobile }}</view>
                  </view>
                </view>
                <img src="https://static.rsjxx.com/image/2025/0630/155239_44370.png" class="arrow-img"/>
              </view>
            </view>
          </view>

          <view class="detail-item">
            <view class="label">预约时间</view>
            <view
              v-if="detail.reserve_date && detail.reserve_time"
              class="text"
            >
              {{detail.reserve_date}}&nbsp;{{detail.reserve_time}}
            </view>
            <view v-else class="text">-</view>
          </view>

          <!-- 预约理疗师 -->
          <view class="detail-item" v-if="detail.type === 'PHYSIO'">
            <view class="label">预约服务</view>
            <view class="text">
              <view class="reserve-service-box">
                <view class="reserve-service-left">
                  <view class="service-info">
                    <view class="service-name">{{ services.goods_service.name }}</view>
                    <view class="service-minute">{{ detail.duration.minute }}分钟</view>
                  </view>

                  <view class="physical-info">
                    <img class="avatar" :src="services.physio.avatar || default_avatar(services.physio.sex)">
                    <view class="physical-name">{{ services.physio.name }}</view>
                    <view class="physical-tag">{{ services.physio.role_name }}</view>
                    <view class="physical-level" v-if="services.physio.level_name">{{ services.physio.level_name }}</view>
                  </view>
                </view>
                <view class="reserve-service-right">
                  <view class="service-price">
                    <span class="price-unit">¥</span>
                    <span class="price-int">{{ getPrice(services.goods_service.price)[0] }}</span>
                    <span class="price-dot">.{{ getPrice(services.goods_service.price)[0] }}</span>
                  </view>
<!--                  <img class="arrow-img" src="https://static.rsjxx.com/image/2025/0630/155239_44370.png">-->
                </view>
              </view>
            </view>
          </view>

          <!-- 预约医生 -->
          <view class="detail-item" v-if="detail.type === 'DOCTOR'">
            <view class="label">预约医生</view>
            <view class="text">
              <view class="doctor-box">
                <img :src="services.physio.avatar || default_avatar(services.physio.sex)" class="avatar" />
                <view class="doctor-name">{{ services.physio.name }}</view>
              </view>
            </view>
          </view>

          <view class="detail-item detail-item-viewider" v-if="detail.type != 'DOCTOR'">
            <view class="label">占用时长</view>
            <view class="text">
              <view class="flex flex-item-align" >
                <view>{{ detail.duration.minute ? `${detail.duration.minute}分钟` : '-' }}</view>
                <view style="color: #999999" v-if="Number(detail.prepare_duration.minute || 0)">
                  <text>（</text>
                  <text>{{ detail.service_duration.minute }}分钟</text>
                  <text>+{{ detail.prepare_duration.minute }}分钟准备</text>
                  <text>）</text>
                </view>
              </view>
            </view>
          </view>

          <view class="detail-item">
            <view class="label">预约状态</view>
            <view class="text">{{ detail.reserve_status_desc || '-' }}</view>
          </view>

          <view class="detail-item">
            <view class="label">支付状态</view>
            <view class="text" :style="{ color: detail.is_payed !== '1' ? '#EE3838' : ''}">{{ detail.is_payed_desc || '-' }}</view>
          </view>

          <view class="detail-item detail-item-viewider">
            <view class="label">下单方式</view>
            <view class="text">{{ detail.source_desc || '-' }}</view>
          </view>

          <view class="detail-item" v-if="detail.source === 'rst_weapp'">
            <view class="label">顾客备注</view>
            <view class="text">{{ detail.user_remark || '-' }}</view>
          </view>

          <view class="detail-item">
            <view class="label">门店备注</view>
            <view class="text">{{ detail.remark || '-' }}</view>
          </view>
        </view>
      </scroll-view>
      <view class="detail-footer">
        <button
          v-if="detail.reserve_status != '3' && detail.reserve_status != '4'"
          class="ui-btn-drawer-base ui-btn-drawer-default flex-1"
          @click="cancelReserve"
        >
          取消预约
        </button>
        <button
          class="ui-btn-drawer-base ui-btn-drawer-default ui-btn-distance-l flex-1 edit-btn"
          @click="editReserve"
          v-if="detail.is_arrival !== '1' &&
              detail.is_payed !== '1' &&
              detail.reserve_status != '3' &&
              detail.reserve_status != '4' &&
              detail.reserve_status != '5' &&
              detail.type != 'DOCTOR' &&
              detail.source !== 'rst_weapp'"
        >
          修改预约单
        </button>
        <button
          v-if="detail.is_arrival !== '1' &&
                detail.reserve_status != '3' &&
                detail.reserve_status != '4' &&
                detail.reserve_status != '5'"
          class="ui-btn-drawer-base ui-btn-drawer-primary ui-btn-distance-l flex-1"
          @click="arriveReserve"
        >
          确认到店
        </button>
        <button
          :loading="pay_loading"
          type="primary"
          class="ui-btn-drawer-base ui-btn-drawer-primary ui-btn-distance-l flex-1"
          @click="pay"
          v-if="
            detail.is_arrival == '1' &&
            detail.is_payed != '1' &&
            detail.reserve_status != '3' &&
            detail.reserve_status != '4' &&
            detail.type != 'DOCTOR' &&
            detail.source != 'rst_weapp'"
          >
          收款
        </button>

        <button
          :loading="confirm_loading"
          type="primary"
          class="ui-btn-drawer-base ui-btn-drawer-primary ui-btn-distance-l flex-1"
          @click="finished"
          v-if="
            detail.reserve_status != '3' &&
            detail.reserve_status != '4' &&
            detail.type != 'DOCTOR' &&
            detail.is_arrival == '1' &&
            detail.is_payed == '1'"
          >
          完成服务
        </button>
      </view>

      <c-loading :show="loading" />
      <cancel-reserve-pop
        v-model="showCancelReserve"
        :type="type"
        :id="selectedId"
        @success="$emit('success')"
      />

      <!-- 修改预约单 -->
      <edit-reserve-drawer v-model="editReserveVisible" :detail="detail" @updateSuccess="$emit('success')"></edit-reserve-drawer>
    </view>
    <view class="reserve-detail" v-else>
      <view class="empty">
        <image src="@/static/image/user/empty.png"></image>
        <view>暂无数据</view>
      </view>
    </view>
    <!-- 到店人信息 -->
    <user-info-drawer v-model="userInfoVisible" :uid="detail.uid"></user-info-drawer>
    <!-- 支付 -->
    <pay-drawer v-model="payVisible" :order_id="order_id" @close="$emit('success')"></pay-drawer>
    <!-- 完成服务 -->
    <confirm-dialog
      ref="finishedRef"
      title="完成服务"
      message="确认服务已完成?"
      @confirm="confirmFinished">

    </confirm-dialog>
  </view>
</template>

<script>
import ReserveService from "@/api/reserve";
import S from "@/utils/utils";
import CancelReservePop from "./cancel-reserve-pop.vue";
import editReserveDrawer from './edit-reserve-drawer.vue'
import userInfoDrawer from './user-info-drawer.vue'
import payDrawer from '@/components/pay-drawer/index.vue'
import ConfirmDialog from '@/components/confirm-dialog/index.vue';
import CLoading from "../../../components/cLoading/loading.vue";

export default {
  name: "right-detail",
  components: {
    CLoading,
    CancelReservePop,
    editReserveDrawer,
    userInfoDrawer,
    payDrawer,
    ConfirmDialog
  },
  props: {
    detail: {
      type: Object,
      default: () => ({})
    },
    loading: {
      type: Boolean,
    },
    show: Boolean,
  },
  computed: {
    default_avatar () {
      return sex => {
        return S.defaultAvatar(sex)
      }
    },
    getPrice () {
      return item => {
        return S.splitPrice(item)
      }
    },
    reserve_user () {
      return this.detail?.reserve_user || {}
    },
    services () {
      return this.detail?.services[0] || {}
    },
    getServiceName() {
      return this.detail?.services?.[0]?.goods_service?.name || '-';
    },
    getServiceDuration() {
      return this.detail?.services?.[0]?.goods_service?.duration_text || '-';
    },
    getPhysioName() {
      return this.detail?.services?.[0]?.physio?.name || '-';
    },
    getPhysioLevel() {
      return this.detail?.services?.[0]?.physio?.mdp_level_name || '-';
    },
  },
  data() {
    return {
      showCancelReserve: false,
      selectedId: '',
      type: '1',
      editReserveVisible: false,
      userInfoVisible: false,
      payVisible: false,
      order_id: '',
      pay_loading: false,
      confirm_loading: false,
    }
  },
  methods: {
    finished () {
      this.$refs.finishedRef.open();
    },
    confirmFinished () {
      this.confirm_loading = true
      let params = {
        id: this.detail.id,
      }
      ReserveService.getReserveFinished(params).then( res => {
        this.$emit('success')
      } ).finally(() => {
        this.confirm_loading = false
      })
    },
    seeUserInfo () {
      this.userInfoVisible = true
    },
    editReserve () {
      this.editReserveVisible = true
    },
    cancelReserve(){
      this.selectedId = this.detail.id;
      this.type = '1';
      this.showCancelReserve = true;
    },
    arriveReserve(){
      this.selectedId = this.detail.id;
      this.type = '2';
      this.showCancelReserve = true;
    },

    pay () {
      if ( +this.detail.order_id ){
        this.order_id = this.detail.order_id
        this.payVisible = true
      }else{
        this.pay_loading = true
        let service_item = this.detail?.services[0] || {}
        let params = {
          id: this.detail.id,
          services: [
            {
              goods_service_id: service_item.goods_service_id,
              physio_id: service_item.physio_id,
              quantity: '1',
            }
          ]
        }
        ReserveService.getReserveConfirm(params).then( res => {
          this.order_id = res.order_id
          this.payVisible = true
        } ).finally(() => {
          this.pay_loading = false
        })
      }

    },
  }
}
</script>

<style scoped lang="scss">
.reserve-detail {
  width: 880rpx;
  height: 100%;
  padding: 32rpx;
  background: #FFFFFF;
  z-index: 0;
  display: flex;
  flex-shrink: 0;
  flex-direction: column;
  align-items: center;
  position: relative;
  border-left: 1PX solid #EFF1F5;
  .detail-title {
    width: 100%;
    font-weight: 600;
    font-size: 32rpx;
    color: #333333;
    line-height: 48rpx;
  }
  .detail-scroll {
    width: calc(100% - 64rpx);
    height: calc(100% - 200rpx);
    padding: 48rpx 64rpx;
    .detail-body {
      width: 100%;
      height: fit-content;
      display: flex;
      flex-direction: column;
      //.line {
      //  width: 100%;
      //  height: 1PX;
      //  background: #EBEDF0;
      //  margin-bottom: 32rpx;
      //}
      .detail-item-viewider {
        padding-bottom: 32rpx;
        border-bottom: 2rpx solid #EBEDF0;
      }
      .detail-item {
        width: 100%;
        display: flex;
        align-items: center;
        font-size: 26rpx;
        color: #999999;
        line-height: 40rpx;
        margin-bottom: 32rpx;
        .label {
          width: 104rpx;
          flex-shrink: 0;
          text-align: right;
          margin-right: 32rpx;
        }
        .text {
          color: #333333;
          width: 100%;
        }

        // 预约用户
        .reserve-user-box {
          width: 100%;
          padding: 24rpx 32rpx;
          background: #F9FAFB;
          border-radius: 8rpx;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .left {
            flex: 1;
            display: flex;
            align-items: center;
            .avatar {
              width: 80rpx;
              min-width: 80rpx;
              height: 80rpx;
              border-radius: 50%;
              object-fit: cover;
            }
            .info-box {
              margin-left: 24rpx;
              .info-top {
                display: flex;
                align-items: center;
                .name {
                  font-weight: 600;
                  font-size: 28rpx;
                  color: #155BD4;
                  line-height: 40rpx;
                }
                .sex {
                  margin-left: 16rpx;
                }
                .sex, .age {
                  font-weight: 400;
                  font-size: 24rpx;
                  color: #999999;
                  line-height: 36rpx;
                }
                .age {
                  min-width: fit-content;
                }
                .line {
                  height: 20rpx;
                  background: #999999;
                  width: 2rpx;
                  margin: 0 10rpx;
                }
              }
              .mobile {
                margin-top: 6rpx;
                font-weight: 400;
                font-size: 24rpx;
                color: #333333;
                line-height: 32rpx;
              }
            }
          }
        }

        // 预约服务
        .reserve-service-box {
          background: #F9FAFB;
          border-radius: 8rpx;
          padding: 24rpx 32rpx;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .reserve-service-left {
            .service-info {
              display: flex;
              align-items: center;
              .service-name {
                font-weight: 500;
                font-size: 28rpx;
                color: #333333;
                line-height: 40rpx;
              }
              .service-minute {
                border-radius: 4rpx;
                border: 1rpx solid #DCDFE6;
                padding: 0 8rpx;
                font-weight: 400;
                font-size: 22rpx;
                color: #666666;
                line-height: 32rpx;
                min-width: fit-content;
                margin-left: 8rpx;
                display: flex;
                justify-content: center;
                align-items: center;
              }
            }

            .physical-info {
              margin-top: 24rpx;
              display: flex;
              align-items: center;
              .avatar {
                width: 40rpx;
                min-width: 40rpx;
                height: 40rpx;
                border-radius: 50%;
                object-fit: cover;
              }
              .physical-name {
                margin-left: 8rpx;
                font-weight: 400;
                font-size: 24rpx;
                color: #444444;
                line-height: 36rpx;
              }
              .physical-tag {
                border: 1px solid #155BD4;
                min-width: fit-content;
                padding: 0 8rpx;
                height: 32rpx;
                margin-left: 8rpx;
                border-radius: 4rpx;
                font-weight: 400;
                font-size: 22rpx;
                color: #155BD4;
                line-height: 32rpx;
                display: flex;
                justify-content: center;
                align-items: center;
              }
              .physical-level {
                margin-left: 8rpx;
                min-width: 40rpx;
                height: 32rpx;
                border-radius: 4rpx;
                border: 1px solid #DCDFE6;
                font-weight: 400;
                font-size: 22rpx;
                color: #666666;
                line-height: 32rpx;
                display: flex;
                align-items: center;
                justify-content: center;
              }
            }
          }
          .reserve-service-right {
            margin-left: 40rpx;
            display: flex;
            align-items: center;
            .service-price {
              font-weight: 600;
              font-size: 24rpx;
              color: #EE3838;
              line-height: 44rpx;
              display: flex;
              align-items: baseline;
              .price-unit {
                font-size: 24rpx;
                margin-right: 4rpx;
                position: relative;
              }
              .price-int {
                font-size: 32rpx;
              }
              .price-dot {
                font-size: 24rpx;
                position: relative;
              }
            }
          }
        }

        // 预约医生
        .doctor-box {
          width: 100%;
          background: #F9FAFB;
          border-radius: 8rpx;
          padding: 24rpx 32rpx;
          display: flex;
          align-items: center;
          .avatar {
            width: 80rpx;
            min-width: 80rpx;
            height: 80rpx;
            border-radius: 50%;
            object-fit: cover;
          }
          .doctor-name {
            margin-left: 24rpx;
            font-weight: 400;
            font-size: 28rpx;
            color: #333333;
            line-height: 40rpx;
          }
          .level {
            display: inline-flex;
            margin-left: 8rpx;
            border-radius: 4rpx;
            border: 1rpx solid #DCDFE6;
            padding: 0rpx 8rpx;
            font-weight: 400;
            font-size: 22rpx;
            color: #666666;
            line-height: 32rpx;
            min-width: 40rpx;
            height: 32rpx;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
  }
  .detail-footer {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: sticky;
    bottom: 0;
    background: #FFFFFF;
    padding: 32rpx 0;

    .edit-btn {
      border-color: #155BD4;
      color: #155BD4;
    }
  }
}

.empty {
  width: 100%;
  height: 100%;
  background: #FFFFFF;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  > view {
    font-size: 26rpx;
    margin-top: 24rpx;
    color: #666666;
  }
}

.arrow-img {
  width: 24rpx;
  min-width: 24rpx;
  height: 24rpx;
  margin-left: 16rpx;
}
</style>
