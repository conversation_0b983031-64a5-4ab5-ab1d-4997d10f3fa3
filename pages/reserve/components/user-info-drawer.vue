<template>
  <cus-drawer v-model="drawerVisible" footerHide :maskClick="true" @on-open="onOpen" @on-close="onClose">
    <view slot="title" class="title">
      <view>{{ title }}</view>
      <!-- 特殊定位分割线 -->
      <view class="divider-line"></view>
    </view>
    <view slot="content" class="content">
      <view class="reserve-box">
          <view class="head">预约历史</view>
          <scroll-view :scroll-y="true" class="reserve-scroll-box">
            <uni-table class="reserve-table" stripe emptyText="暂无更多数据" >
              <!-- 表头行 -->
              <uni-tr class="header-tr">
                <uni-th v-for="(head_item, head_index) in columns" :width="head_item.width" :key="head_index" :align="head_item.align || 'center'">{{ head_item.label }}</uni-th>
              </uni-tr>
              <!-- 表格数据行 -->
              <uni-tr  v-for="(item, index) in list" :key="index">
                <uni-td>
                  <view v-if="item.create_time">
                    <view>{{ getTime(item.create_time).date }}</view>
                    <view>{{ getTime(item.create_time).hour }}</view>
                  </view>
                  <view v-else>-</view>
                </uni-td>
                <uni-td>{{ item.goods_name || '-' }}</uni-td>

                <uni-td>
                  <view v-if="item.type === 'DOCTOR'">-</view>
                  <view v-else>{{ Number(item.duration.minute || 0) > 0 ? `${item.duration.minute}分钟` : '-' }}</view>
                </uni-td>
                <uni-td>{{ item.is_payed_desc || '-' }}</uni-td>
                <uni-td>{{ item.status_desc || '-' }}</uni-td>
              </uni-tr>
            </uni-table>
        </scroll-view>
      </view>

      <view class="info-wrapper">
        <scroll-view scroll-y class="scroll-box">
          <view class="info-box">
            <img :src="user.avatar || default_avatar" class="avatar">
            <view class="info">
              <view class="info-top">
                <view class="info-name">{{ user.real_name || '-' }}</view>
                <img v-if="user.auth_status == '2'" src="https://static.rsjxx.com/image/2025/0630/171657_40674.png" class="synced-icon">
              </view>
              <view class="info-bottom">
                <view class="sex">{{ user.sex_text }}</view>
                <view class="line" v-if="user.sex_text && user.age"></view>
                <view class="age">{{ user.age ? `${user.age}岁` : '' }}</view>
              </view>
            </view>
          </view>

          <view class="tag-box">
            <view class="tag-item" v-for="item in user.label_list || []" :key="item.tag_id">{{ item.tag_name }}</view>
          </view>

          <view class="item-box">
            <view class="item-label">联系电话</view>
            <view class="item-value">{{ user.mobile || '-' }}</view>
          </view>

          <view class="item-box">
            <view class="item-label">出生日期</view>
            <view class="item-value">{{ user.birthday || '-' }}</view>
          </view>

          <view class="item-box">
            <view class="item-label">身份证号</view>
            <view class="item-value">{{ user.card_no || '-' }}</view>
          </view>

          <view class="item-box">
            <view class="item-label">注册名称</view>
            <view class="item-value">{{ user.nickname || '-' }}</view>
          </view>

          <view class="item-box">
            <view class="item-label">注册时间</view>
            <view class="item-value">{{  format(user.register_time) || '-' }}</view>
          </view>

          <view class="item-box">
            <view class="item-label">用户来源</view>
            <view class="item-value">{{ user.source_text || '-' }}</view>
          </view>

          <view class="item-box">
            <view class="item-label">联系地址</view>
            <view class="item-value">{{ getAddress || '-' }}</view>
          </view>

          <view class="item-box">
            <view class="item-label">会员名称</view>
            <view class="item-value">
              <view v-if="user.vip_info && !user.vip_info.length">-</view>
              <view v-else v-for="item in user.vip_info || []" :key="item.user_type">{{ item.desc }}</view>
            </view>
          </view>
        </scroll-view>
      </view>
      <c-loading :show="tableLoading" />
    </view>
  </cus-drawer>
</template>

<script>
import ReserveService from "@/api/reserve";
import dayjs from "dayjs";
import cusDrawer from '@/components/cus-drawer/index.vue'
import S from '@/utils/utils'
import CLoading from "../../../components/cLoading/loading.vue";
export default {
  name: 'user-info-drawer',
  components: {
    CLoading,
    cusDrawer
  },
  mixins: [],
  props: {
    value: {
      type: Boolean,
      default: () => false
    },
    title: {
      type: String,
      default: '到店人信息'
    },
    uid: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      drawerVisible: false,
      tableLoading: false,
      columns: [
        { label: '预约时间', key: 'info', align: 'left', width: 100 },
        { label: '预约项目', key: 'goods_type_text', align: 'left', width: 80 },
        { label: '服务时长', key: 'price', align: 'left', width: 60 },
        { label: '支付状态', key: 'num', align: 'left', width: 60 },
        { label: '预约状态', key: 'total', align: 'left', width: 60 },
      ],
      list: [],
      user: {},
    };
  },
  computed: {
    default_avatar () {
      return S.defaultAvatar(this.user?.sex)
    },
    format () {
      return day => {
        return day ? dayjs(day*1000).format('YYYY-MM-DD HH:mm:ss') : ''
      }
    },
    getAddress () {
      let address = this.user?.address || {}
      return `${address?.city?.name || ''}${address?.prov?.name || ''}${address?.county?.name || ''}${address?.other || ''}`
    },
    getTime () {
      return time => {
        return {
          date: dayjs(time * 1000).format('YYYY-MM-DD'),
          hour: dayjs(time * 1000).format('HH:mm:ss'),
        }
      }
    },
  },
  watch: {
    value (val) {
      if ( val ) {
        this.drawerVisible = true
        this.getReservehistorylist()
      }else{
        this.drawerVisible = false
        this.closeDrawer()
      }
    },
  },
  created() {},
  mounted() {},
  methods: {
    closeDrawer () {
      this.$emit('input', false)
      this.clearData()
    },
    clearData () {
    },
    onOpen () {
    },
    onClose () {
      this.closeDrawer()
    },

    getReservehistorylist () {
      this.tableLoading = true;
      let params = {
        page: 1,
        page_size: 2000,
        uid: this.uid,
      }
      ReserveService.getReservehistorylist(params).then( res => {
        this.list = res.list
        this.user = res.user
      } ).finally(() => {
        this.tableLoading = false;
      })
    },
  },
};
</script>

<style lang="scss" scoped>
.title {
  position: relative;
  .divider-line {
    position: absolute;
    top: 41px;
    left: 450px;
    width: 1px;
    background: #EBEDF0;
    height: 100vh;
  }
}
.content {
  display: flex;
  height: 100%;
  .scroll-box {
    height: 100%;
  }
  .reserve-box {
    flex: 1;
    height: 100%;
    padding-right: 38rpx;
    .reserve-scroll-box {
      height: calc(100% - 100rpx);
    }
    .head {
      font-weight: 600;
      font-size: 32rpx;
      color: #333333;
      line-height: 48rpx;
      padding-bottom: 32rpx;
    }
  }
  .info-wrapper {
    padding-left: 40rpx;
    width: 496rpx;
    min-width: 496rpx;
  }
  .info-box {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;
    .avatar {
      width: 96rpx;
      min-width: 96rpx;
      height: 96rpx;
      border-radius: 50%;
      object-fit: cover;
    }
    .info {
      margin-left: 24rpx;
      .info-top {
        display: flex;
        align-items: center;
        .info-name {
          font-weight: 600;
          font-size: 32rpx;
          color: #333333;
          line-height: 48rpx;
        }
        .synced-icon {
          margin-left: 8rpx;
          width: 32rpx;
          min-width: 32rpx;
          height: 32rpx;
        }
      }
      .info-bottom {
        margin-top: 8rpx;
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
        line-height: 36rpx;
        display: flex;
        align-items: center;

        .line {
          margin: 0 10rpx;
          background: #999999;
          width: 2rpx;
          height: 22rpx;
        }
      }
    }
  }

  .tag-box {
    margin-bottom: 48rpx;
    .tag-item {
      display: inline-flex;
      border-radius: 4rpx;
      border: 1rpx solid #DCDFE6;
      padding:0 8rpx;
      font-weight: 400;
      font-size: 22rpx;
      color: #666666;
      line-height: 32rpx;
      margin-right: 8rpx;
      margin-bottom: 8rpx;
    }
  }

  .item-box {
    margin-bottom: 24rpx;
    .item-label {
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      line-height: 36rpx;
    }
    .item-value {
      margin-top: 4rpx;
      font-weight: 400;
      font-size: 24rpx;
      color: #333333;
      line-height: 36rpx;
      text-align: justify;
    }
  }
}



::v-deep .reserve-table {
  //height: 100%;
  th,td {
    border: none;
  }
  td {
    word-break: break-all;
  }
  .uni-table {
    position: absolute;
  }
  .uni-table-th {
    font-weight: 600;
    font-size: 26rpx;
    color: #303133;
    line-height: 40rpx;
  }
  .header-tr {
    background: #F5F6F8;
    box-shadow: 0px 2px 4px 0px rgba(20,21,22,0.06);
    position: sticky;
    top: 0rpx;
  }
}
</style>
