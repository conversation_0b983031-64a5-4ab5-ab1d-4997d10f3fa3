<script>
import dayjs from 'dayjs'
import CDatePicker from "./components/c-date-picker.vue";
import RightDetail from "./components/right-detail.vue";
import ReserveService from "../../api/reserve";
import CancelReservePop from "./components/cancel-reserve-pop.vue";
import CLoading from "../../components/cLoading/loading.vue";
export default {
  name: "reserve-page",
  components: {
    CLoading,
    CancelReservePop,
    RightDetail,
    CDatePicker,
  },
  data() {
    return {
      showFormDatePicker: false,
      form: {
        date: dayjs().format('YYYY-MM-DD'),
      },
      page: {
        page: 1,
        page_size: 99999,
      },
      dateRange: [],
      reserveMaps: {},
      activeId: '',
      tableLoading: false,
      detailLoading: false,
      reserveDetail: {},
      showCancelReserve: false,
      actionType: '1',
      selectedId: '',
    }
  },
  computed: {
    /**
     * reserve_status 1:待到店 2:已到店 3:已完成 4:已取消 5:待确认
     * is_arrival 1:已到店 2:待到店
     * is_payed 0:未付款 1: 已付款
     * */
    getStatusColor () {
      return card => {
        if (card.reserve_status == '2' && card.is_payed != '1') {
          // 已到店，未支付
          return '#EE3838'
        }else if(card.reserve_status == '1'){
          // 待到店
          return '#EE3838'
        }else if (card.reserve_status === '2') {
          // 已到店
          return '#155BD4'
        }else if (card.reserve_status === '3' || card.reserve_status === '4') {
          // 已完成/已取消
          return '#999'
        }else{
          // 其他
          return '#999'
        }
      }
    },
    // 组合预约状态
    getStatusDesc () {
      return card => {
        let desc = ''
        if (card.reserve_status === '2' && card.is_payed != '1' && card.type !== 'DOCTOR') {
          // 已到店，未支付
          desc = `${card?.reserve_status_desc}，${card?.is_payed_desc}`
        }else{
          desc = card?.reserve_status_desc
        }
        return desc


        // if (card?.is_payed == '1' && card?.reserve_status != '3') {
        //   desc = card?.is_payed_desc
        // }else if(){
        //   desc = `${card?.reserve_status_desc}，${card?.is_payed_desc}`
        // }else{
        //   desc = card?.reserve_status_desc
        // }
        // return desc || '-'
      }
    },
    isToday() {
      return dayjs(dayjs().format('YYYY-MM-DD')).isSame(dayjs(this.form.date))
    },
    reserveMapsList() {
      return Object.fromEntries(
        Object.entries(this.reserveMaps).filter(([_, reserve]) => reserve?.list?.length !== 0)
      );
    },
    swipeOptions() {
      return key => {
        const actions = [{
          text: '取消预约',
          keys: [1,2],
          style: {
            backgroundColor: '#EE3838',
            fontSize: '26rpx',
          }
        },{
          text: '确认到店',
          keys: [1],
          style: {
            backgroundColor: '#155BD4',
            fontSize: '26rpx',
          }
        }]
        return actions?.filter(action => action.keys.includes(+key))
      }
    },
    getReserveEndTime() {
      return (card, type) => {
        if (!card?.reserve_time) return '-';
        if (type === 'st') return dayjs.unix(card.reserve_time).format('HH:mm');
        const h = +card?.service_duration?.hour || 0
        const m = +card?.service_duration?.minute || 0
        const currentTime = dayjs.unix(card.reserve_time).add(h * 60 + m, 'minute');
        return currentTime.format('HH:mm')
      }
    },
    isShowService() {
      return card => Object.keys(card?.services?.[0]?.goods_service || {}).length > 0
    },
    isShowPhysio() {
      return card => Object.keys(card?.services?.[0]?.physio || {}).length > 0
    },
    getServeName() {
      return card => card?.services?.[0]?.goods_service?.name || '-'
    },
    getRhysioName() {
      return card => card?.services?.[0]?.physio?.name || '-'
    },
    getPhysioRole() {
      return card => card?.services?.[0]?.physio?.role_name || '-'
    },
    getPhysioRoleLevel() {
      return card => card?.services?.[0]?.physio?.mdp_level_name || ''
    },
  },
  watch: {
    activeId: {
      immediate: true,
      handler(newVal) {
        if (!newVal) return
        this.getUserReserveDetail(newVal)
      }
    }
  },
  created() {
    this.tableLoading = true
  },
  mounted() {
    this.getUserReserveTimeRange()
    this.getUserReserveOptions()
  },
  methods: {
    dayjs,
    getUserReserveTimeRange() {
      ReserveService.getUserReserveTimeRange().then(res => {
        this.dateRange = res?.dates || []
      })
    },
    getUserReserveOptions() {
      ReserveService.getUserReserveOptions()
      .then(res => {
        const list_status_desc = (res?.list_status_desc || {})
        for (const key in list_status_desc) {
          const obj = {
            key: key,
            desc: list_status_desc[key]?.desc || '',
            list: []
          }
          this.$set(this.reserveMaps, key, obj)
        }
        this.getUserReserveList()
      })
    },
    getUserReserveList() {
      this.activeId = ''
      this.reserveDetail = {}

      for (const key in this.reserveMaps) {
        this.$set(this.reserveMaps[key], 'list', [])
      }
      this.tableLoading = true
      ReserveService.getUserReserveList({
        ...this.page,
        arrival_st: this.form.date + ' 00:00:00',
        arrival_et: this.form.date + ' 23:59:59',
      }).then(res => {
        // 未选择服务项目和技师的，暂时不展示
        const list = res?.list?.filter(card => {
          const goods_service = card?.services?.[0]?.goods_service || {}
          const physio = card?.services?.[0]?.physio || {}
          return Object.keys(goods_service).length > 0 && Object.keys(physio).length > 0
        })
        for (const key in this.reserveMaps) {
          const targetList = list?.filter(item => +item.reserve_status === +key)
          this.$set(this.reserveMaps[key], 'list', targetList)
        }
        const keys = Object.keys(this.reserveMaps) || []
        for (let i = 0; i < keys.length; i++) {
          if (this.reserveMaps[i]?.list?.length > 0) {
            this.activeId = this.reserveMaps[i]?.list?.[0]?.id || ''
            break
          }
        }
      }).finally(() => {
        this.tableLoading = false
        this.selectedId = ''
      })
    },
    getUserReserveDetail(id) {
      if (!id) return
      this.detailLoading = true
      ReserveService.getUserReserveDetail({
        id
      }).then(res => {
        this.reserveDetail = res || {}
        this.detailLoading = false
      }).catch(err => {
        uni.showToast({
          title: err.errmsg,
          type: 'error',
        })
        this.reserveDetail = {}
        this.detailLoading = false
      })
    },
    changeReserveDate(type, val) {
      if (type === 'left') {
        if (!dayjs(dayjs().format('YYYY-MM-DD')).isBefore(dayjs(this.form.date))) return;
        this.form.date = dayjs(this.form.date).subtract(1, 'day').format('YYYY-MM-DD')
        this.getUserReserveList()
      }
      if (type === 'right') {
        this.form.date = dayjs(this.form.date).add(1, 'day').format('YYYY-MM-DD')
        this.getUserReserveList()
      }
      if (type === 'input') {
        if (this.form.date === val) return
        this.form.date = val
        this.getUserReserveList()
      }
      if (type === 'today' && !this.isToday) {
        this.form.date = dayjs().format('YYYY-MM-DD')
        this.getUserReserveList()
      }
    },
    onClick(e, id) {
      if (e.index === 0) {
        this.showCancelReserve = true
        this.selectedId = id;
        this.actionType = '1'
      }
      if (e.index === 1) {
        this.showCancelReserve = true
        this.selectedId = id;
        this.actionType = '2'
      }
    }
  }
}
</script>

<template>
  <view class="reserve-container">
    <view class="reserve-header-box">
      <view class="reserve-header">
        <view class="left-arrow" @click="changeReserveDate('left')"></view>
        <view class="reserve-date" @click="showFormDatePicker = true">
          <text>{{ form.date }}</text>
          <image class="calendar" src="@/static/image/reserve/date.png" mode="aspectFit"></image>
        </view>
        <view class="right-arrow" @click="changeReserveDate('right')"></view>
        <view :class="['back-today', isToday && 'disabled']" @click="changeReserveDate('today')">回到今天</view>
        <view :class="['back-today']" @click="getUserReserveList">刷新</view>
      </view>
    </view>
    <view class="reserve-body">
      <view class="reserve-list" v-if="!tableLoading">
        <scroll-view class="list-scroll" :scroll-y="true">
          <view v-if="Object.keys(reserveMapsList).length > 0" class="list-wrap">
            <view class="list-item" v-for="map in reserveMapsList" :key="map.key">
              <view class="list-title">{{ map.desc || '-' }}</view>
              <uni-swipe-action>
                <uni-swipe-action-item
                  :right-options="swipeOptions(map.key)"
                  v-for="card in map.list"
                  :key="card.id"
                  @click="_ => onClick(_, card.id)"
                >
                  <view
                    :class="['list-card', activeId === card.id && 'active']"
                    @click="activeId = card.id"
                  >
                    <view class="time">
                      <view>{{ getReserveEndTime(card, 'st')}}</view>
                      <view>～</view>
                      <view>{{ getReserveEndTime(card, 'et') }}</view>
                    </view>
                    <view class="line"></view>
                    <view class="info">
                      <view class="info-top">
                        <view class="title" v-if="isShowService(card)">{{ getServeName(card) }}</view>
                        <view class="title" v-else>未选择服务</view>
                        <template v-if="isShowPhysio(card)">
                          <view class="name">{{ getRhysioName(card) }} </view>
                          <view class="career">{{ getPhysioRole(card) }}</view>
                          <view v-if="getPhysioRoleLevel(card)" class="level">{{ getPhysioRoleLevel(card) }}</view>
                        </template>
                        <view v-else class="name">未分配技师</view>
                      </view>
                      <view class="info-bottom">
                        <view class="name">{{ card.reserve_user && card.reserve_user.name || '-' }}</view>
                        <view class="line"></view>
                        <view class="mobile">{{ card.reserve_user && card.reserve_user.mobile || '-' }}</view>
                      </view>
                    </view>
                    <view class="status">
                      <view class="status-desc">
                        <view class="point" :style="{ background: getStatusColor(card) }"></view>
                        <view class="text" :style="{ color: getStatusColor(card) }">{{ getStatusDesc(card) || '-' }}</view>
                      </view>
                      <view class="source">{{ card.source_desc || '-' }}</view>
                    </view>
                  </view>
                </uni-swipe-action-item>
              </uni-swipe-action>
            </view>
          </view>
          <view v-else class="list-wrap empty">
            <image class="empty-img" src="@/static/image/user/empty.png"></image>
            <view class="empty-text">暂无数据</view>
          </view>
          <c-loading :show="tableLoading" />
        </scroll-view>
      </view>
      <right-detail
        v-if="Object.keys(reserveDetail).length > 0"
        :show="Object.keys(reserveDetail).length > 0 && !tableLoading"
        :detail="reserveDetail"
        :loading="detailLoading && !tableLoading"
        @success="getUserReserveList"
      />
      <c-loading :show="tableLoading" />
    </view>
    <c-date-picker
      v-model="showFormDatePicker"
      :date="form.date"
      :date-rang="dateRange"
      type="date"
      title="选择预约时间"
      :start-date="dayjs().format('YYYY-MM-DD')"
      @on-ok="val => changeReserveDate('input', val)"
    />

    <cancel-reserve-pop v-model="showCancelReserve" :type="actionType" :id="selectedId" @success="getUserReserveList" />
  </view>
</template>

<style scoped lang="scss">
.reserve-container {
  width: 100%;
  height: 100vh;
  position: relative;
  .reserve-header-box {
    width: 100%;
    height: 112rpx;
    position: sticky;
    background: #FFFFFF;
  }
  .reserve-header {
    height: 106rpx;
    box-shadow: 0 6rpx 12rpx 0 rgba(20,21,22,0.1);
    //z-index: 1;
    display: flex;
    align-items: center;
    padding: 24rpx;
    box-sizing: border-box;

    .left-arrow,
    .right-arrow {
      width: 64rpx;
      height: 64rpx;
      background: #FFFFFF;
      border-radius: 8rpx;
      border: 1PX solid #DCDFE6;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      position: relative;
      transition: all 0.2s ease;
      cursor: pointer;

      &:active {
        transform: scale(0.95);
        opacity: 0.8;
        background: #F5F7FA;
      }

      &::after {
        content: '';
        width: 12rpx;
        height: 12rpx;
        border-top: 2rpx solid #666;
        border-right: 2rpx solid #666;
        position: absolute;
      }
    }

    .left-arrow::after {
      transform: rotate(-135deg);
      margin-left: 4rpx;
    }

    .right-arrow::after {
      transform: rotate(45deg);
      margin-right: 4rpx;
    }

    .back-today {
      width: 128rpx;
      height: 64rpx;
      background: #FFFFFF;
      border-radius: 8rpx;
      border: 1PX solid #DCDFE6;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 26rpx;
      color: #333;
      transition: all 0.2s ease;
      cursor: pointer;


      &:not(.disabled):active {
        transform: scale(0.95);
        opacity: 0.8;
        background: #F5F7FA;
      }

      &.disabled {
        border-color: #cccccc;
        color: #cccccc;
        cursor: not-allowed;
      }
    }
    .reserve-date {
      width: 264rpx;
      height: 64rpx;
      background: #FFFFFF;
      border-radius: 8rpx;
      border: 1PX solid #DCDFE6;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 8rpx;
      font-size: 26rpx;
      color: #333333;
      cursor: pointer;
      .calendar {
        width: 32rpx;
        height: 32rpx;
        margin-left: 26rpx;
      }
    }

    .back-today {
      width: 152rpx;
      height: 64rpx;
      background: #FFFFFF;
      border-radius: 8rpx;
      border: 1PX solid #DCDFE6;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28rpx;
      color: #333;
      margin-left: 16rpx;
    }
  }
  .reserve-body {
    width: 100%;
    height: calc(100% - 112rpx);
    display: flex;
    z-index: 1;
    .reserve-list {
      flex: 1;
      height: 100%;
      .list-scroll {
        width: 100%;
        height: 100%;
        .list-wrap {
          width: 100%;
          height: fit-content;
          padding-bottom: 32rpx;
          .list-item {
            width: 100%;
            background: #F5F6F8;
            .list-title {
              font-size: 24rpx;
              color: #999999;
              line-height: 36rpx;
              padding: 24rpx 32rpx;
            }
            .list-card {
              width: 100%;
              background: #FFFFFF;
              padding: 0 32rpx 0 48rpx;
              display: flex;
              align-items: center;
              &.active {
                background: #EFF1F5;
                .info {
                  border-bottom: none;
                }
              }
              .time {
                width: 76rpx;
                font-weight: 600;
                font-size: 30rpx;
                color: #333333;
                line-height: 44rpx;
                text-align: center;
              }
              .line {
                width: 4rpx;
                height: 84rpx;
                background: #EBEDF0;
                margin: 0 32rpx;
              }
              .info {
                flex: 1;
                height: 100%;
                padding: 30rpx 0;
                border-bottom: 1PX solid #DCDFE6;
                .info-top {
                  width: 100%;
                  display: flex;
                  align-items: center;
                  .title {
                    font-weight: 600;
                    font-size: 30rpx;
                    color: #333333;
                    margin-right: 48rpx;
                  }
                  .name {
                    font-size: 24rpx;
                    color: #444444;
                    margin-right: 8rpx;
                  }
                  .career {
                    padding: 4rpx 8rpx;
                    border-radius: 4rpx;
                    border: 1PX solid #155BD4;
                    font-size: 22rpx;
                    color: #155BD4;
                    margin-right: 8rpx;
                  }
                  .level {
                    padding: 4rpx 8rpx;
                    border-radius: 4rpx;
                    border: 1PX solid #DCDFE6;
                    font-size: 22rpx;
                    color: #666666;
                  }
                }
                .info-bottom {
                  width: fit-content;
                  margin-top: 20rpx;
                  padding: 10rpx 16rpx;
                  display: flex;
                  align-items: center;
                  background: linear-gradient( 90deg, #F5F6F8 0%, rgba(245,246,248,0) 100%);
                  .name {
                    font-size: 24rpx;
                    color: #444444;
                  }
                  .line {
                    width: 1PX;
                    height: 24rpx;
                    background: #EBEDF0;
                    margin: 0 8rpx;
                  }
                  .mobile {
                    font-size: 24rpx;
                    color: #444444;
                  }
                }
              }
              .status {
                width: 250rpx;
                height: 100%;
                display: flex;
                flex-direction: column;
                align-items: flex-end;
                .status-desc {
                  width: 100%;
                  display: flex;
                  align-items: center;
                  justify-content: flex-end;
                  margin-bottom: 28rpx;
                  .point {
                    width: 12rpx;
                    height: 12rpx;
                    background: #EE3838;
                    border-radius: 50%;
                    margin-right: 14rpx;
                  }
                  .text {
                    font-size: 26rpx;
                    color: #EE3838;
                  }
                }
                .source {
                  font-weight: 300;
                  font-size: 24rpx;
                  color: #999999;
                }
              }
            }
            .list-card:last-child {
              .info {
                border-bottom: none;
              }
            }
          }
        }
      }
    }
  }
}
.empty {
  width: 100%;
  height: 100% !important;
  padding-bottom: 0 !important;
  background: #FFFFFF;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  .empty-img {
    width: 322px;
    min-width: 322px;
    height: 320px;
  }
  .empty-text {
    margin-top: -20rpx;
    font-size: 26rpx;
    color: #666666;
  }
}
</style>
