<script>
import CTooltip from "../../../components/cTooltip/index.vue";
import UserService from "../../../api/user";
import UseCard from "./use-card.vue";
import CLoading from "../../../components/cLoading/loading.vue";

export default {
  name: "CouponList",
  components: {CLoading, UseCard, CTooltip},
  data() {
    return {
      serv_type_desc: [],
      serv_type: '',
      detail: {
        card_list: []
      },
      couponListInfo: {
        total_card_num: 0,
        total_serv_type_num: 0
      },
      list: [],
      total: 0,
      page: {
        page: 1,
        page_size: 20,
      },
      tableLoading: true,
      showNoMoreText: false,
    }
  },
  props: {},
  mounted() {
    this.getUserCardOptions();
    this.getUserCardList();
  },
  methods: {
    getUserCardList(type) {
      const query = this.$store.getters['global/getQuery'];
      if (!query.id)  return;
     this.tableLoading = true;
      UserService.getUserCardList({
        uid: query.id,
        serv_type: this.serv_type || '',
        ...this.page
      }).then(res => {
        if (type === 'scroll') {
          this.list = this.list.concat(res?.list || []);
        } else {
          this.list = res?.list || []
        }
        this.total = +res?.total || 0
        this.couponListInfo.total_card_num = +res?.total_card_num || 0
        this.couponListInfo.total_serv_type_num = +res?.total_serv_type_num || 0
        this.couponListInfo.used_total = +res?.used_total || 0
        this.couponListInfo.expired_total = +res?.expired_total || 0
        if (this.total < 10) {
          this.showNoMoreText = false
        } else {
          this.showNoMoreText = this.page.page * this.page.page_size >= this.total
        }
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    getUserCardOptions() {
      const query = this.$store.getters['global/getQuery'];
      if (!query.id)  return;
      UserService.getUserCardOptions({
        uid: query.id
      }).then(res => {
        this.serv_type_desc = Object.keys(res?.serv_type_desc)?.map(key => ({
          value: key,
          text: res?.serv_type_desc?.[key]?.desc_original || ''
        })) || []
        this.serv_type_desc.unshift({value: '', text: "全部类型"})
      })
    },
    openDetailDrawer(coupon) {
      if (['EXPIRED', 'CLOSED'].includes(coupon.status) || +coupon.surplus_num == 0) return;

      this.tableLoading = true;
      UserService.getUserCardDetail({
        batch_id: coupon.id,
      }).then(res => {
        this.$refs.useCard.openDrawer();
        this.detail = res || {};
        this.$set(this.detail, 'card_list', res?.card_list || []);
      }).catch(err => {
        uni.showToast({
          title: err.errmsg,
          type: 'error',
        })
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    closeDrawer() {
      this.detail = {
        card_list: []
      }
      this.showNoMoreText = false;
    },
    changType(val) {
      this.page.page = 1;
      this.getUserCardList()
    },
    scrolltolower() {
      const { page, page_size } = this.page;
      if (page * page_size >= this.total) return
      this.page.page += 1;
      this.getUserCardList('scroll')
    },
    previewImage(imgUrl) {
      if (!imgUrl) return
      uni.previewImage({
        urls: [imgUrl]
      })
    },
  }
}
</script>

<template>
  <view class="coupon-container">
    <view class="coupon-header">
      <view class="header-type">
        <uni-data-select
          class="coupon-list-select"
          v-model="serv_type"
          :localdata="serv_type_desc"
          :clear="false"
          @change="changType"
        ></uni-data-select>
      </view>
      <view class="header-desc">
        {{ couponListInfo.used_total  || 0 }} 张有效卡券，共 {{ couponListInfo.total_card_num }} 次服务可用
      </view>
    </view>
    <view class="coupon-list-wrap">
      <scroll-view class="coupon-list-scroll" :scroll-y="true" @scrolltolower="scrolltolower">
        <view class="coupon-list">
          <view class="coupon-list-item" v-for="coupon in list" :key="coupon.id">
            <view class="left-content">
              <view class="coupon-title" v-if="coupon.card_name">
                <c-tooltip :text="coupon.card_name" />
              </view>
              <view class="coupon-title" v-else>-</view>
              <view class="coupon-info">
                <view class="coupon-info-item">
                  <view class="item-label">类型</view>
                  <view class="item-text">：{{ coupon.serv_type_text || '-' }}</view>
                </view>
                <view class="coupon-info-item">
                  <view class="item-label">总次数</view>
                  <view class="item-text">：{{ coupon.card_num || 0 }}</view>
                </view>
                <view class="coupon-info-item">
                  <view class="item-label">有效期至</view>
                  <view class="item-text">：{{ coupon.expire_time || '-' }}</view>
                </view>
              </view>
              <image v-if="coupon.status === 'EXPIRED'" class="count-status-img" src="@/static/image/user/expire.png" mode="aspectFit"></image>
              <image v-if="+coupon.surplus_num === 0" class="count-status-img" src="@/static/image/user/use_finished.png" mode="aspectFit"></image>
              <image v-if="coupon.status === 'CLOSED'" class="count-status-img" src="@/static/image/user/invalid.png" mode="aspectFit"></image>
            </view>
            <view class="right-content">
              <view class="coupon-count">
                {{ coupon.surplus_num || 0 }}
              </view>
              <view class="count-text">次可用</view>
              <view
                :class="['coupon-use', (['EXPIRED', 'CLOSED'].includes(coupon.status) || !(+coupon.surplus_num > 0)) && 'disabled']"
                @click="openDetailDrawer(coupon)"
              >
                出示使用
              </view>
            </view>
          </view>
        </view>
        <view v-if="showNoMoreText" class="no-more-data">没有更多数据了</view>
        <view v-if="list.length === 0 && !tableLoading" class="empty">
          <image class="empty-img" src="@/static/image/user/empty.png"></image>
          <view class="empty-text">暂无数据</view>
        </view>
      </scroll-view>

      <c-loading :show="tableLoading" />
    </view>
    <use-card ref="useCard" :detail="detail" @close="closeDrawer" />
  </view>
</template>

<style scoped lang="scss">
.coupon-container {
  width: 100%;
  height: 100%;
  position: relative;
  background: #F5F6F8;
  .coupon-header {
    width: 100%;
    height: 74rpx;
    margin-bottom: 24rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .header-type {
      width: 240rpx;
      background: #FFFFFF;
      border-radius: 8rpx;
      ::v-deep .uni-select {
        border: none !important;
      }
      ::v-deep .uni-select__input-text {
        text-align: center;
      }
      ::v-deep .uni-icons {
        padding-right: 12rpx;
      }
    }
    .header-desc {
      font-size: 24rpx;
      color: #999999;
      line-height: 36rpx;
    }
  }
  .coupon-list-wrap {
    width: 100%;
    height: calc(100% - 98rpx);
    .coupon-list-scroll {
      width: 100%;
      height: 100%;
      .coupon-list {
        width: 100%;
        height: fit-content;
        display: flex;
        flex-wrap: wrap;
        .coupon-list-item {
          width: calc(50% - 12rpx);
          height: 260rpx;
          margin-right: 24rpx;
          margin-bottom: 24rpx;
          padding: 24rpx 32rpx;
          display: flex;
          justify-content: space-between;
          background-image: url("../../../static/image/user/card-bg.png");
          background-repeat: no-repeat;
          background-position: center center;
          background-size: 100% 100%;
          .left-content {
            width: 71.5%;
            height: 100%;
            position: relative;
            .coupon-title {
              width: 90%;
              font-weight: 600;
              font-size: 30rpx;
              color: #333333;
              line-height: 44rpx;
            }
            .coupon-info {
              width: 100%;
              margin-top: 24rpx;
              .coupon-info-item {
                width: 100%;
                display: flex;
                align-items: center;
                font-size: 24rpx;
                color: #999999;
                line-height: 36rpx;
                margin-bottom: 12rpx;
                .item-label {
                  width: 96rpx;
                  text-align: justify;
                  text-align-last: justify;
                }
              }
            }
            .count-status-img {
              width: 180rpx;
              height: 100%;
              position: absolute;
              top: 0;
              right: 26rpx;
              display: flex;
              align-items: center;
              .image {
                width: 180rpx;
                height: 180rpx;
              }
            }
          }
          .right-content {
            width: 27%;
            height: 100%;
            padding-left: 8rpx;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            .coupon-count {
              width: 100%;
              font-weight: 600;
              font-size: 48rpx;
              color: #333333;
              line-height: 68rpx;
              text-align: center;
            }
            .count-text {
              font-size: 24rpx;
              color: #999999;
              line-height: 36rpx;
              text-align: center;
            }
            .coupon-use {
              width: 136rpx;
              height: 48rpx;
              margin-top: 24rpx;
              background: #155BD4;
              border-radius: 28rpx;
              font-size: 24rpx;
              color: #FFFFFF;
              line-height: 48rpx;
              text-align: center;
            }
            .coupon-use.disabled {
              background: #E0E1E3;
              color: #FFFFFF;
            }
          }
        }
        .coupon-list-item:nth-child(2n) {
          margin-right: 0;
        }
        .coupon-list-item:last-child {
          margin-right: auto;
        }
      }
    }
  }
}
.no-more-data {
  width: 100%;
  text-align: center;
  padding: 24rpx;
  font-size: 24rpx;
  color: #999999;
}
.coupon-list-select {
  ::v-deep .uni-select__selector-item {
    display: flex;
    justify-content: center;
  }
}
.empty {
  width: 100%;
  height: 100%;
  background: #FFFFFF;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  .empty-text{
    margin-top: -20rpx;
    font-size: 26rpx;
    color: #666666;
  }
  .empty-img {
    width: 322px;
    min-width: 322px;
    height: 320px;
  }
}
</style>
