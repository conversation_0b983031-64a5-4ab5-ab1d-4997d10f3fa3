<script>
import CTooltip from "../../../components/cTooltip/index.vue";
import UserService from "../../../api/user";
import TkiQrcode from "../../../components/tki-qrcode/tki-qrcode.vue";
import { format_time } from "../../../utils/helper";
import UseCard from "./use-card.vue";
import CLoading from "../../../components/cLoading/loading.vue";

export default {
  name: "exchangeList",
  components: {CLoading, UseCard, TkiQrcode, CTooltip},
  data() {
    return {
      screenWidth: 600,
      list: [],
      total: 0,
      page: {
        page: 1,
        page_size: 10,
      },
      tips_msg: '',
      detail: {
        service_card_list: [],
        service_list: []
      },
      tabKey: '1',
      selectedService: {
        count: 1,
      },
      selectedCard: {
        card_list: []
      },
      tableLoading: false,
      showNoMoreText: false,
    }
  },
  props: {},
  computed: {
    format_time() {
      return (time, format) => format_time(time, format);
    },
  },
  mounted() {
    this.getSystemInfo();
    this.getUserExchangeList()
  },
  methods: {
    getUserExchangeList(type) {
      const query = this.$store.getters['global/getQuery'];
      if (!query.id)  return;
      this.tableLoading = true;
      UserService.getUserExchangeList({
        ...this.page,
        uid: query.id,
      }).then(res => {
        this.tips_msg = res?.tips_msg || '';
        if (type === 'scroll') {
          this.list = this.list.concat(res?.list || []);
        } else {
          this.list = res?.list || []
        }
        this.total = +res?.total || 0;
        if (this.total < 10) {
          this.showNoMoreText = false
        } else {
          this.showNoMoreText = this.page.page * this.page.page_size >= this.total
        }
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    scrolltolower() {
      const { page, page_size } = this.page;
      if (page * page_size >= this.total) return
      this.page.page += 1;
      this.getUserExchangeList('scroll')
    },
    getSystemInfo() {
      const systemInfo = uni.getSystemInfoSync();
      this.screenWidth = (systemInfo?.screenWidth || 300) * 0.41;
    },
    openDetailDrawer(exchange, tabKey = '1' ) {
      // if (['EXPIRED', 'CLOSED'].includes(exchange.status) || +exchange.surplus_num == 0) return;

      this.tableLoading = true;
      UserService.getUserExchangeDetail({
        id: exchange.id,
      }).then(res => {
        this.$refs.exchangeDetail.open()
        this.detail = res || {};
        this.$set(this.detail, 'service_card_list', res?.service_card_list || []);
        this.$set(this.detail, 'service_list', res?.service_list || []);
        this.tabKey = tabKey;
        if (tabKey === '2') {
          this.closePop()
        }
      }).catch(err => {
        uni.showToast({
          title: err.errmsg,
          type: 'error',
        })
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    closeDetailDrawer() {
      this.$refs.exchangeDetail.close()
    },
    drawerChange(visible) {
      if (!visible) {
        this.detail = {
          card_list: []
        }
      }
    },
    previewImage(imgUrl) {
      if (!imgUrl) return
      uni.previewImage({
        urls: [imgUrl]
      })
    },
    openPop(service) {
      this.$refs.exchangePopup.open();
      this.selectedService = service;
      this.$set(this.selectedService, 'count', 1);
    },
    closePop() {
      this.$refs.exchangePopup.close();
      this.selectedService = {};
      this.$set(this.selectedService, 'count', 1);
    },
    confirmExchange() {
      UserService.exchangeCardByDetail({
        id: this.detail.id,
        gs_id: this.selectedService.id,
        times: this.selectedService.count,
      }).then(() => {
        uni.showToast({
          title: '兑换成功',
        })
        this.openDetailDrawer(this.detail, '2')
      }).catch(err => {
        uni.showToast({
          title: err.errmsg,
          type: 'error',
        })
      })
    },
    openUseCardDrawer(coupon) {
      if (['EXPIRED', 'CLOSED', 'ALL'].includes(coupon.status)) return;

      this.tableLoading = true;
      UserService.getUserCardDetail({
        batch_id: coupon.id,
      }).then(res => {
        this.selectedCard = res || {};
        this.$set(this.selectedCard, 'card_list', res?.card_list || []);
        this.$refs.useCard.openDrawer();
      }).catch(err => {
        uni.showToast({
          title: err.errmsg,
          type: 'error',
        })
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    closeUseCardDrawer() {
      this.selectedCard = { card_list: [] };
    },
  }
}
</script>

<template>
  <view class="exchange-container">
    <view class="exchange-header">
      <view class="header-desc">
        {{ tips_msg || '共 0 张有效通兑券，剩余 0 次服务可兑换' }}
      </view>
    </view>
    <view class="exchange-list-wrap">
      <scroll-view class="exchange-list-scroll" :scroll-y="true" @scrolltolower="scrolltolower">
        <view class="exchange-list">
          <view class="exchange-list-item" v-for="exc in list" :key="exc.id">
            <view class="left-content">
              <view class="exchange-title" v-if="exc.name">
                <c-tooltip :text="exc.name" />
              </view>
              <view class="exchange-info">
                <view class="exchange-info-item">
                  <view class="item-label">总次数</view>
                  <view class="item-text">：{{ exc.num || 0 }}</view>
                </view>
                <view class="exchange-info-item">
                  <view class="item-label">截止兑换</view>
                  <view class="item-text">：{{ format_time(exc.expire_time, 'YY年MM月DD日') || '-' }}</view>
                </view>
                <view class="exchange-info-item">
                  <view class="item-label">兑换范围</view>
                  <view class="item-text" v-if="exc.services_name">
                    <view class="flex">
                      <view>：</view>
                      <c-tooltip :text="exc.services_name" />
                    </view>
                  </view>
                  <view class="item-text" v-else>：</view>
                </view>
              </view>
              <image v-if="exc.status === 'EXPIRED'" class="count-status-img" src="@/static/image/user/expire.png" mode="aspectFit"></image>
              <image v-if="+exc.surplus_num === 0" class="count-status-img" src="@/static/image/user/use_finished.png" mode="aspectFit"></image>
              <image v-if="exc.status === 'CLOSED'" class="count-status-img" src="@/static/image/user/invalid.png" mode="aspectFit"></image>
            </view>
            <view :class="['right-content', (['EXPIRED', 'CLOSED'].includes(exc.status) || !(+exc.surplus_num > 0)) && 'disabled']">
              <view class="exchange-count">
                {{ exc.surplus_num || 0 }}
              </view>
              <view class="count-text">次可兑</view>
              <view class="exchange-use" @click="openDetailDrawer(exc)">查看详情</view>
            </view>
          </view>
        </view>
        <view v-if="showNoMoreText" class="no-more-data">没有更多数据了</view>
        <view v-if="list.length === 0 && !tableLoading" class="empty">
          <image class="empty-img" src="@/static/image/user/empty.png"></image>
          <view class="empty-text">暂无数据</view>
        </view>
        <c-loading :show="tableLoading" />
      </scroll-view>
    </view>

    <uni-drawer
      ref="exchangeDetail"
      :width="screenWidth"
      mode="right"
      :mask-click="false"
      @change="drawerChange"
    >
      <view class="detail-drawer">
        <view class="detail-header">
          <view class="detail-header-title">通兑券详情</view>
          <uni-icons class="delete-icon" type="closeempty" size="16" color="#AAAAAA" @click="closeDetailDrawer"></uni-icons>
        </view>
        <view class="detail-content">
          <scroll-view class="detail-content-scroll" :scroll-y="true">
            <view class="exc-content-header">
              <image class="pro-img" :src="detail.main_img" @click.stop="previewImage(detail.main_img)" mode="aspectFit"></image>
              <view class="base-info">
                <view class="title">{{ detail.name || '-' }}</view>
                <view class="time">剩余可兑
                  <text style="color: #EE3838; margin: 0 4rpx;">{{ detail.surplus_num || 0 }}</text>
                  次</view>
              </view>
            </view>
            <view class="exchange-detail-dashed-line"></view>
            <view class="exchange-detail-base">
              <view class="exchange-detail-base-item">
                <view class="label">总次数</view>
                <view class="text"> {{ detail.num || 0}} </view>
              </view>
              <view class="exchange-detail-base-item">
                <view class="label">截止日期</view>
                <view class="text"> {{ format_time(detail.expire_time, 'YY年MM月DD日') || '-' }} </view>
              </view>
              <view class="exchange-detail-base-item">
                <view class="label">兑换范围</view>
                <view class="text">{{ detail.services_name }}</view>
              </view>
            </view>
            <view class="exchange-detail-line"></view>
            <view class="detail-exchange-list">
              <view :class="['detail-exchange-list-tabs', tabKey ==='1' ? 'tab-left' : 'tab-right' ]">
                <view class="detail-exchange-list-tab" :class="{ 'tab-active': tabKey ==='1' }" @click="tabKey = '1'">可兑换服务</view>
                <view class="detail-exchange-list-tab" :class="{ 'tab-active': tabKey ==='2' }" @click="tabKey = '2'">待使用服务 <text v-if="+detail.wait_card_num > 0">({{detail.wait_card_num}})</text></view>
              </view>
              <view class="exchange-card-list" v-if="tabKey === '1'">
                <view class="exchange-card-list-item" v-for="service in detail.service_list" :key="service.id">
                  <view class="left-exchange-info">
                    <view class="title">{{ service.card_name || '-' }}</view>
                    <view class="desc">
                      最多兑换
                      <text style="color:#EE3838; margin: 0 4rpx">{{ service.max_num || 0}}</text>
                      次，已兑换
                      <text style="color:#EE3838; margin: 0 4rpx">{{ service.already_num || '-' }}</text>
                      次
                    </view>
                  </view>
                  <view class="right-btn disabled" v-if="service.service_status === 'ALL'">已兑完</view>
                  <view class="right-btn" v-if="['WAIT', 'PART'].includes(service.service_status)" @click.stop="openPop(service)">兑换</view>
                  <view class="right-btn disabled" v-if="service.service_status === 'EXPIRED'">已过期</view>
                  <view class="right-btn disabled" v-if="service.service_status === 'CLOSED'">已失效</view>
                </view>
              </view>
              <view class="exchange-card-list" v-if="tabKey === '2'">
                <view class="coupon-list-item" v-for="coupon in detail.service_card_list" :key="coupon.id">
                  <view class="left-content">
                    <view class="coupon-title" v-if="coupon.card_name">
                      <c-tooltip :text="coupon.card_name" />
                    </view>
                    <view class="coupon-title" v-else>-</view>
                    <view class="coupon-info">
                      <view class="coupon-info-item">
                        <view class="item-label">类型</view>
                        <view class="item-text">：{{ coupon.serv_type_text || '-' }}</view>
                      </view>
                      <view class="coupon-info-item">
                        <view class="item-label">总次数</view>
                        <view class="item-text">：{{ coupon.card_num || 0 }}</view>
                      </view>
                      <view class="coupon-info-item">
                        <view class="item-label">有效期至</view>
                        <view class="item-text">：{{ format_time(coupon.expire_time, 'YY年MM月DD日') || '-' }}</view>
                      </view>
                    </view>
                    <image v-if="coupon.status === 'EXPIRED'" class="count-status-img" src="@/static/image/user/expire.png" mode="aspectFit"></image>
                    <image v-if="+coupon.surplus_num === 0" class="count-status-img" src="@/static/image/user/use_finished.png" mode="aspectFit"></image>
                    <image v-if="coupon.status === 'CLOSED'" class="count-status-img" src="@/static/image/user/invalid.png" mode="aspectFit"></image>
                  </view>
                  <view class="right-content">
                    <view class="coupon-count">
                      {{ coupon.card_num - coupon.used_num || 0 }}
                    </view>
                    <view class="count-text">次可用</view>
                    <view
                      class="coupon-use"
                      v-if="['WAIT', 'PART'].includes(coupon.status)"
                      @click.stop="openUseCardDrawer(coupon)"
                    >
                      出示使用
                    </view>
                    <view
                      v-else
                      :class="['coupon-use', 'disabled']"
                    >
                      {{ coupon.status_text }}
                    </view>
                  </view>
                </view>
              </view>

              <view v-if="(detail.service_list.length === 0 && tabKey === '1') || (detail.service_card_list.length === 0 && tabKey === '2')" class="empty" style="padding-bottom: 80rpx;">
                <image class="empty-img" src="@/static/image/user/empty.png"></image>
                <view class="empty-text">{{ tabKey === '1' ? '您还没有兑换服务哦～' : '您还没有可使用服务哦～'}}</view>
              </view>
            </view>
          </scroll-view>
        </view>
      </view>
    </uni-drawer>
    <use-card ref="useCard" :detail="selectedCard" @close="closeUseCardDrawer"/>
    <uni-popup ref="exchangePopup" :mask-click="false">
      <view class="pop-container">
        <view class="pop-container-header">
          <view class="title">兑换服务</view>
          <uni-icons class="delete-icon" type="closeempty" size="16" color="#AAAAAA" @click="closePop"></uni-icons>
        </view>
        <view class="pop-container-content">
          <view class="card-tip">
            服务兑换完成后，无法撤回。请确认无误后再进行兑换。
          </view>
          <view class="card-prod">
            <view class="title">{{ selectedService.card_name || '-' }}</view>
            <view class="desc">剩余可兑换 <text style="color: #8C7A67; margin: 0 4rpx">
              {{ selectedService.surplus_num }}
            </text> 次</view>
          </view>
          <view class="card-count">
            <view class="title">兑换次数</view>
            <view class="count">
              <uni-number-box :min="1" :max="+selectedService.surplus_num || 1" :step="1" background="#fff" v-model="selectedService.count" />
            </view>
          </view>
        </view>
        <view class="pop-container-footer">
          <view class="cancel-btn" @click.stop="closePop">取消</view>
          <view class="ok-btn" @click.stop="confirmExchange">确认兑换</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<style scoped lang="scss">
.exchange-container {
  width: 100%;
  height: 100%;
  background: #F5F6F8;
  .exchange-header {
    width: 100%;
    height: 74rpx;
    margin-bottom: 24rpx;
    display: flex;
    align-items: center;
    .header-desc {
      font-size: 24rpx;
      color: #999999;
      line-height: 36rpx;
    }
  }
  .exchange-list-wrap {
    width: 100%;
    height: calc(100% - 98rpx);
    .exchange-list-scroll {
      width: 100%;
      height: 100%;
      .exchange-list {
        width: 100%;
        height: fit-content;
        display: flex;
        flex-wrap: wrap;
        .exchange-list-item {
          width: calc(50% - 12rpx);
          height: 260rpx;
          margin-right: 24rpx;
          margin-bottom: 24rpx;
          padding: 24rpx 32rpx;
          display: flex;
          justify-content: space-between;
          background-image: url("../../../static/image/user/card-bg.png");
          background-repeat: no-repeat;
          background-position: center center;
          background-size: 100% 100%;
          .left-content {
            width: 71.5%;
            height: 100%;
            position: relative;
            .exchange-title {
              width: 90%;
              font-weight: 600;
              font-size: 30rpx;
              color: #333333;
              line-height: 44rpx;
            }
            .exchange-info {
              width: 100%;
              margin-top: 24rpx;
              .exchange-info-item {
                width: 100%;
                display: flex;
                align-items: center;
                font-size: 24rpx;
                color: #999999;
                line-height: 36rpx;
                margin-bottom: 12rpx;
                .item-label {
                  width: 100rpx;
                  min-width: 100rpx;
                  text-align: justify;
                  text-align-last: justify;
                }
                .item-text {
                  width: calc(100% - 100rpx);
                }
              }
            }
            .count-status-img {
              width: 180rpx;
              height: 100%;
              position: absolute;
              top: 0;
              right: 26rpx;
              display: flex;
              align-items: center;
              .image {
                width: 180rpx;
                height: 180rpx;
              }
            }
          }
          .right-content {
            width: 27%;
            height: 100%;
            padding-left: 8rpx;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            .exchange-count {
              width: 100%;
              font-weight: 600;
              font-size: 48rpx;
              color: #EE3838;
              line-height: 68rpx;
              text-align: center;
            }
            .count-text {
              font-size: 24rpx;
              color: #EE3838;
              line-height: 36rpx;
              text-align: center;
            }
            .exchange-use {
              width: 136rpx;
              height: 48rpx;
              margin-top: 24rpx;
              background: #155BD4;
              border-radius: 28rpx;
              font-size: 24rpx;
              color: #FFFFFF;
              line-height: 48rpx;
              text-align: center;
              border: 1PX solid #155BD4;
            }
          }
          .right-content.disabled {
            .exchange-count {
              color: #CCCCCC;
            }
            .count-text {
              color: #CCCCCC;
            }
            .exchange-use {
              background: #FFFFFF;
              color: #666666;
              border-color: #DCDFE6;
            }
          }
        }
        .exchange-list-item:nth-child(2n) {
          margin-right: 0;
        }
        .exchange-list-item:last-child {
          margin-right: auto;
        }
      }
    }
  }
}
.detail-drawer {
  width: 100%;
  height: 100vh;

  .detail-header {
    padding: 0 40rpx;
    width: 100%;
    height: 120rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #FFFFFF;
    box-shadow: 0 6rpx 12rpx 0 rgba(20, 21, 22, 0.04);
  }

  .detail-content {
    width: 100%;
    height: calc(100% - 120rpx);
    padding: 32rpx;

    .detail-content-scroll {
      width: 100%;
      height: 100%;
      position: relative;
      .exc-content-header {
        width: 100%;
        min-height: 160rpx;
        display: flex;
        align-items: center;
        .pro-img {
          width: 160rpx;
          height: 160rpx;
          background: #F5F6F8;
          border-radius: 8rpx;
          margin-right: 24rpx;
        }
        .base-info {
          flex: 1;
          min-height: 160rpx;
          max-height: 100%;
          .title {
            font-weight: 600;
            font-size: 30rpx;
            color: #333333;
            line-height: 44rpx;
            white-space: wrap;
            word-break: break-all;
          }
          .time {
            font-size: 28rpx;
            color: #999999;
            line-height: 36rpx;
            margin-top: 24rpx;
          }
        }
      }
      .exchange-detail-dashed-line {
        margin: 30rpx 0;
        width: 100%;
        border-bottom: 1PX dashed #EBEDF0;
      }
      .exchange-detail-line {
        margin: 30rpx 0;
        width: 100%;
        border-bottom: 1PX solid #EBEDF0;
      }
      .exchange-detail-base {
        width: 100%;
        .exchange-detail-base-item {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 24rpx;
          font-size: 26rpx;
          line-height: 40rpx;
          .label {
            color: #999999;
            min-width: fit-content;
          }
          .text {
            margin-left: 20rpx;
            color: #333333;
            white-space: normal;
          }
        }
      }
      .detail-exchange-list {
        width: 100%;
        .tab-left {
          background-image: url("../../../static/image/user/tab-left.png");
        }
        .tab-right {
          background-image: url("../../../static/image/user/tab-right.png");
        }
        .detail-exchange-list-tabs {
          width: 100%;
          height: 80rpx;
          background-repeat: no-repeat;
          background-position: center center;
          background-size: 100% 100%;
          display: flex;
          align-items: center;
          margin-bottom: 24rpx;
          .detail-exchange-list-tab {
            width: 50%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            font-size: 28rpx;
            color: #999999;
          }
          .tab-active {
            color: #333333;
          }
        }
        .exchange-card-list {
          width: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          .exchange-card-list-item {
            width: 100%;
            height: 160rpx;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 32rpx;
            background: #FBFCFF;
            border-radius: 8rpx;
            border: 1PX solid rgba(137,156,174,0.4);
            margin-bottom: 24rpx;
            .left-exchange-info {
              width: calc(100% - 136rpx);
              height: 100%;
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              .title {
                font-weight: 500;
                font-size: 30rpx;
                color: #333333;
                line-height: 44rpx;
              }
              .desc {
                font-size: 24rpx;
                color: #999999;
                line-height: 36rpx;
              }
            }
            .right-btn {
              width: 136rpx;
              height: 48rpx;
              display: flex;
              align-items: center;
              justify-content: center;
              background: #155BD4;
              border-radius: 28rpx;
              font-size: 24rpx;
              color: #FFFFFF;
            }
            .right-btn.disabled {
              background: #E0E1E3;
              color: #FFFFFF;
            }
          }
          .coupon-list-item {
            border-radius: 12rpx;
            width: calc(100% - 136rpx);
            height: 240rpx;
            margin-right: 24rpx;
            margin-bottom: 24rpx;
            padding: 24rpx 32rpx;
            display: flex;
            justify-content: space-between;
            background-image: url("../../../static/image/user/card-bg.png");
            background-repeat: no-repeat;
            background-position: center center;
            background-size: 100% 100%;
            border: 1PX solid #EBEDF0;
            .left-content {
              width: 71.5%;
              height: 100%;
              position: relative;
              .coupon-title {
                width: 90%;
                font-weight: 600;
                font-size: 30rpx;
                color: #333333;
                line-height: 44rpx;
              }
              .coupon-info {
                width: 100%;
                margin-top: 24rpx;
                .coupon-info-item {
                  width: 100%;
                  display: flex;
                  align-items: center;
                  font-size: 24rpx;
                  color: #999999;
                  line-height: 36rpx;
                  margin-bottom: 12rpx;
                  .item-label {
                    width: 96rpx;
                    text-align: justify;
                    text-align-last: justify;
                  }
                }
              }
              .count-status-img {
                width: 180rpx;
                height: 100%;
                position: absolute;
                top: 0;
                right: 26rpx;
                display: flex;
                align-items: center;
                .image {
                  width: 180rpx;
                  height: 180rpx;
                }
              }
            }
            .right-content {
              width: 27%;
              height: 100%;
              padding-left: 8rpx;
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              .coupon-count {
                width: 100%;
                font-weight: 600;
                font-size: 48rpx;
                color: #333333;
                line-height: 68rpx;
                text-align: center;
              }
              .count-text {
                font-size: 24rpx;
                color: #999999;
                line-height: 36rpx;
                text-align: center;
              }
              .coupon-use {
                width: 136rpx;
                height: 48rpx;
                margin-top: 24rpx;
                background: #155BD4;
                border-radius: 28rpx;
                font-size: 24rpx;
                color: #FFFFFF;
                line-height: 48rpx;
                text-align: center;
              }
              .coupon-use.disabled {
                background: #E0E1E3;
                color: #FFFFFF;
              }
            }
          }
        }
      }
    }
  }
}
.pop-container {
  width: 1040rpx;
  background: #FFFFFF;
  border-radius: 8rpx;
  .pop-container-header {
    width: 100%;
    height: 114rpx;
    padding: 0 28rpx 0 40rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .title {
      font-weight: 600;
      font-size: 32rpx;
      color: #333333;
    }
    .delete-icon {
      width: 24rpx;
      height: 24rpx;
      padding: 12rpx;
    }
  }
  .pop-container-content {
    width: 100%;
    min-height: 500rpx;
    border-top: 1PX solid #EBEDF0;
    border-bottom: 1PX solid #EBEDF0;
    display: flex;
    flex-direction: column;
    align-items: center;
    .card-tip {
      width: 100%;
      height: 80rpx;
      min-height: 80rpx;
      padding: 0 40rpx;
      background: rgba(255,170,0,0.12);
      font-size: 24rpx;
      color: #FFAA00;
      display: flex;
      align-items: center;
    }
    .card-prod {
      width: 676rpx;
      height: 200rpx;
      min-height: 200rpx;
      background-image: url("../../../static/image/user/exchange-card.png");
      background-repeat: no-repeat;
      background-position: center center;
      background-size: 100% 100%;
      padding-left: 214rpx;
      display: flex;
      flex-direction: column;
      justify-content: center;
      margin-top: 64rpx;
      margin-bottom: 16rpx;
      .title {
        font-weight: 500;
        font-size: 34rpx;
        color: #5D3D2C;
        line-height: 48rpx;
        margin-bottom: 16rpx;
      }
      .desc {
        font-weight: 300;
        font-size: 26rpx;
        color: #8C7A67;
        line-height: 40rpx;
      }
    }
    .card-count {
      width: 676rpx;
      height: 124rpx;
      min-height: 124rpx;
      background-image: url("../../../static/image/user/card-count.png");
      background-repeat: no-repeat;
      background-position: center center;
      background-size: 100% 100%;
      padding: 16rpx 32rpx 0 32rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 96rpx;
      .title {
        font-size: 28rpx;
        color: #333333;
        line-height: 40rpx;
      }
      .count {
        ::v-deep .uni-numbox__value {
          margin: 0 1PX;
          color: #333333 !important;
        }
      }
    }
  }
  .pop-container-footer {
    width: 100%;
    height: 114rpx;
    display: flex;
    align-items: center;
    .cancel-btn {
      margin-left: auto;
      width: 120rpx;
      height: 64rpx;
      background: #FFFFFF;
      border-radius: 8rpx;
      border: 1PX solid #DCDFE6;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 26rpx;
      color: #666666;
    }
    .ok-btn {
      margin-left: 16rpx;
      margin-right: 40rpx;
      width: 168rpx;
      height: 64rpx;
      background: #155BD4;
      border-radius: 8rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 26rpx;
      color: #FFFFFF;
    }
  }
}
.no-more-data {
  width: 100%;
  text-align: center;
  padding: 24rpx;
  font-size: 24rpx;
  color: #999999;
}
.empty {
  width: 100%;
  height: 100%;
  background: #FFFFFF;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  .empty-text{
    margin-top: -20rpx;
    font-size: 26rpx;
    color: #666666;
  }
  .empty-img {
    width: 322px;
    min-width: 322px;
    height: 320px;
  }
}
</style>
