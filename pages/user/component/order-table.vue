<script>
import UserService from "../../../api/user";
import { $operator } from '../../../utils/operation'
import dayjs from "dayjs";
import payDrawer from '@/components/pay-drawer/index.vue'
import CLoading from "../../../components/cLoading/loading.vue";

export default {
  name: 'order-table',
  components: {
    CLoading,
    payDrawer
  },
  computed: {
    getVirtualImage() {
      return item => {
        if (item.is_ry_order == '1') {
          return 'https://img-sn-i01s-cdn.rsjxx.com/image/2025/0219/101535_43264.jpeg-B.200';
        } else {
          return 'https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0923/100618_15681.png-B.200';
        }
      };
    },
    dayjs() {
      return dayjs
    },
    $operator() {
      return $operator
    },
    getPointColor() {
      return order => {
        const status_text = order.status_text
        switch (status_text) {
          case '待付款':
          case '待发货':
          case '支付中':
            return '#ffad33';
          case '已发货':
          case '已完成':
            return '#19be6b';
          case '已关闭':
            return '#C0C4CC';
          case '部分付款':
            return '#155BD4';
          default:
            return '#C0C4CC';
        }
      }
    },
  },
  data() {
    return {
      total: 10,
      page: 1,
      list: [],
      showNoMoreText: false,
      tableLoading: false,

      payVisible: false,
      order_id: '',
    }
  },
  created() {
    this.init()
  },
  methods: {
    init () {
      this.getOrderList()
    },
    pay (order) {
      this.order_id = order.order_id
      this.payVisible = true
    },
    scrolltolower() {
      console.log(this.page * 20 , this.total, 'total')
      if (this.page * 20 >= this.total) return
      this.page += 1
      this.getOrderList('scroll')
    },
    getOrderList(type) {
      const query = this.$store.getters['global/getQuery'];
      if (!query.id)  return;
      this.tableLoading = true
      UserService.getUserOrderList({
        page: this.page,
        page_size: 20,
        uid: query.id
      }).then((res) => {
        if (type === 'scroll') {
          this.list = this.list.concat(res?.list || []);
        } else {
          this.list = res?.list;
        }
        this.total = +res?.total || 0;
        if (this.total <= 15) {
          this.showNoMoreText = false
        } else {
          this.showNoMoreText = this.page * 20 >= this.total
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    previewImage(imgUrl) {
      if (!imgUrl) return
      uni.previewImage({
        urls: [imgUrl]
      })
    },
  }
}
</script>

<template>
  <view class="table">
    <view class="table-th-row">
      <view class="th-col">
        商品
      </view>
      <view class="th-col">
        应付
      </view>
      <view class="th-col">
        实付
      </view>
      <view class="th-col">
        订单号
      </view>
      <view class="th-col">
        下单时间
      </view>
      <view class="th-col">
        状态
      </view>
    </view>
    <scroll-view
      class="table-scroll"
      :scroll-y="true"
      :scroll-x="false"
      @scrolltolower="scrolltolower"
    >
      <view class="table-body">
        <view class="table-td-row" v-for="order in list" :key="order.order_id">
          <view class="td-col">
            <view class="goods_img">
              <view class="prod-img" v-for="attr in order.attrs" @click.stop="previewImage(attr.image_src)" :key="attr.id">
                <image :src="attr.is_goods == '0' ? getVirtualImage(attr) : attr.image_src" mode="aspectFit"></image>
                <view v-if="attr.quantity > 1" class="prod-num">×{{ attr.quantity }}</view>
              </view>
            </view>
          </view>
          <view class="td-col" style="color: #333333">
            ¥{{ $operator.toPrecision(order.payment_fee || 0, 2).toFixed(2) }}
          </view>
          <view class="td-col" style="color: #333333">
            ¥{{ $operator.toPrecision(order.received_payment || 0, 2).toFixed(2)}}
          </view>
          <view class="td-col" style="color: #999999">
            {{ order.out_trade_no || '-' }}
          </view>
          <view class="td-col" style="color: #999999">
            {{ dayjs(order.order_time * 1000).format('YYYY-MM-DD HH:mm:ss') || '-' }}
          </view>
          <view class="td-col">
            <view>
              <view class="status_text">
                <view
                  class="point"
                  :style="{
                    backgroundColor:  getPointColor(order)
                  }"
                />
                <view class="text">{{ order.status_text }}</view>
              </view>
              <view
                class="status_pay"
                @click="pay(order)"
                v-if="order.class === 'shop_order' && [1000].includes(+order.status)"
              >
                立即支付
              </view>
              <view class="status_text_tip" v-if="order.closed_type_text">({{ order.closed_type_text }})</view>
            </view>
          </view>
        </view>
      </view>
      <view v-if="showNoMoreText" class="no-more-data">没有更多数据了</view>
      <view v-if="list.length === 0 && !tableLoading" class="empty">
        <image class="empty-img" src="@/static/image/user/empty.png"></image>
        <view class="empty-text">暂无数据</view>
      </view>
    </scroll-view>
    <c-loading :show="tableLoading" />
    <!-- 支付 -->
    <pay-drawer v-model="payVisible" :order_id="order_id" @success="init"></pay-drawer>
  </view>
</template>

<style scoped lang="scss">
.table {
  width: 100%;
  height: 100%;
  position: relative;
  .table-th-row {
    width: 100%;
    height: 88rpx;
    background: #FFFFFF;
    display: flex;
    position: relative;
    z-index: 1;
    box-shadow: $ui-table-shadow-base;
    .th-col {
      width: 15%;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      font-weight: 500;
      padding: 0 24rpx;
      font-size: 26rpx;
      color: #333333;
    }
    .th-col:nth-child(1) {
      width: 20%;
    }
    .th-col:nth-child(5) {
      width: 20%;
    }
  }
  .table-scroll {
    width: 100%;
    height: calc(100% - 88rpx);
    .table-body {
      width: 100%;
      .table-td-row {
        width: 100%;
        min-height: 40rpx;
        background: #FFFFFF;
        padding: 16rpx 0;
        display: flex;
        .td-col {
          width: 15%;
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          justify-content: flex-start;
          padding: 0 24rpx;
          font-size: 26rpx;
          color: #333333;
          word-break: break-all;
        }
        .td-col:nth-child(1) {
          width: 20%;
        }
        .td-col:nth-child(5) {
          width: 20%;
        }
      }
      .table-td-row:nth-child(2n - 1) {
        background: #F9FAFB;
      }
    }
  }
}
.goods_img {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  .prod-img {
    width: 72rpx;
    height: 72rpx;
    border-radius: 4rpx;
    overflow: hidden;
    margin: 4rpx 8rpx 4rpx 0;
    position: relative;
    > image {
      width: 100%;
      height: 100%;
    }
    .prod-num {
      position: absolute;
      bottom: 0;
      left: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 28rpx;
      background: rgba(0,0,0,0.3);
      border-radius: 0 0 4rpx 4rpx;
      font-size: 22rpx;
      color: #FFFFFF;
    }
  }
  .prod-img:last-child {
    margin-right: auto;
  }
}
.status_text {
  width: 100%;
  display: flex;
  align-items: center;
  .point {
    width: 14rpx;
    height: 14rpx;
    background: #EE3838;
    border-radius: 50%;
    margin-right: 12rpx;
  }
  .text {
    font-size: 26rpx;
    color: #333333;
    line-height: 36rpx;
  }
}
.status_text_tip {
  font-size: 24rpx;
  color: #999999;
  line-height: 36rpx;
}
.status_pay {
  width: 136rpx;
  height: 48rpx;
  background: #155BD4;
  border-radius: 24rpx;
  font-size: 24rpx;
  color: #FFFFFF;
  line-height: 48rpx;
  text-align: center;
  margin-top: 8rpx;
}
.no-more-data {
  width: 100%;
  text-align: center;
  padding: 24rpx;
  font-size: 24rpx;
  color: #999999;
}

.empty {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  .empty-text{
    margin-top: -20rpx;
    font-size: 26rpx;
    color: #666666;
  }
  .empty-img {
    width: 322px;
    min-width: 322px;
    height: 320px;
  }
}
</style>
