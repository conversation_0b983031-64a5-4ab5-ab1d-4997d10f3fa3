<script>
import TkiQrcode from "../../../components/tki-qrcode/tki-qrcode.vue";

export default {
  name: 'use-card',
  components: {TkiQrcode},
  props: {
    detail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      screenWidth: 600,
    }
  },
  mounted() {
    this.getSystemInfo()
  },
  methods: {
    openDrawer() {
      this.$refs.cardDetail.open()
    },
    closeDrawer() {
      this.$refs.cardDetail.close()
    },
    drawerChange(visible) {
      if (visible) {
        this.$emit("open")
      } else {
        this.$emit("close")
      }
    },
    previewImage(imgUrl) {
      if (!imgUrl) return
      uni.previewImage({
        urls: [imgUrl]
      })
    },
    getSystemInfo() {
      const systemInfo = uni.getSystemInfoSync();
      this.screenWidth = (systemInfo?.screenWidth || 300) * 0.41;
    },
  }
}
</script>

<template>
  <uni-drawer
    ref="cardDetail"
    :width="screenWidth"
    mode="right"
    :mask-click="false"
    @change="drawerChange"
  >
    <view class="detail-drawer">
      <view class="detail-header">
        <view class="detail-header-title">卡券详情</view>
        <uni-icons class="delete-icon" type="closeempty" size="16" color="#AAAAAA" @click="closeDrawer"></uni-icons>
      </view>
      <view class="detail-content">
        <scroll-view class="detail-content-scroll" :scroll-y="true">
          <view class="coupon-detail-header">
            <image v-if="detail.goods" class="pro-img" :src="detail.goods.img" @click.stop="previewImage(detail.goods.img)" mode="aspectFit"></image>
            <view class="base-info">
              <view class="title">{{ detail.card_name || '-' }}</view>
              <view v-if="detail.goods" class="time">{{ detail.goods.name}}</view>
            </view>
          </view>
          <view class="coupon-detail-line"></view>
          <view class="coupon-code">
            <view class="pro-img">
              <tki-qrcode
                ref="qrcode"
                cid="card_no_detail"
                :val="detail.out_trade_no"
                :size="248"
                unit="rpx"
                background="#fff"
                foreground="#000"
                pdground="#000"
                :onval="true"
                :loadMake="true"
              />
            </view>
            <view class="code-tip">可出示此卡券码核销使用</view>
          </view>
          <view class="coupon-detail-s-line"></view>
          <view class="coupon-list-info">
            <view class="list-header">
              <view class="label">券码信息</view>
              <view class="desc">
                共 {{ detail.card_num || 0 }} 次，剩余 <view>{{ detail.surplus_num || 0}}</view> 次可用
              </view>
            </view>
            <view class="list-wrap" :style="{ height: detail.show || detail.card_list.length <= 5 ? 'fit-content' : '160rpx'}">
              <view :class="['list-item', 'STATUS_HAS_USE' === card.status && 'disabled']" v-for="card in detail.card_list" :key="card.card_no">
                <view class="point"></view>
                <view class="card-code">{{ card.card_no || '-' }}</view>
                <view class="card-status">{{ card.status_text || '-' }}</view>
              </view>
            </view>
            <view v-if="!detail.show && detail.card_list.length > 5" @click="detail.show = !detail.show" class="show-more">
              展开完整信息
              <image src="@/static/image/user/down.png" mode="aspectFit"></image>
            </view>
            <view v-if="detail.show && detail.card_list.length > 5" @click="detail.show = !detail.show" class="hide-more">
              收起
              <image src="@/static/image/user/down.png" mode="aspectFit"></image>
            </view>
          </view>
          <view class="coupon-detail-s-line"></view>
          <view class="coupon-other-info">
            <view class="title">其他信息</view>
            <view class="other-info-list">
              <view class="item">
                <view class="label">来源订单</view>
                <view class="text">{{ detail.out_trade_no || '-' }}</view>
              </view>
              <view class="item">
                <view class="label">手机号码</view>
                <view class="text">{{ detail.mobile || '-' }}</view>
              </view>
              <view class="item">
                <view class="label">付款时间</view>
                <view class="text">{{ detail.pay_time || '-' }}</view>
              </view>
              <view class="item">
                <view class="label">订单金额</view>
                <view class="text">¥ {{ detail.total_fee || '0.00' }}</view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
  </uni-drawer>
</template>

<style scoped lang="scss">
.detail-drawer {
  width: 100%;
  height: 100vh;
  .detail-header {
    padding: 0 40rpx;
    width: 100%;
    height: 120rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #FFFFFF;
    box-shadow: 0 6rpx 12rpx 0 rgba(20,21,22,0.04);
  }
  .detail-content {
    width: 100%;
    height: calc(100% - 120rpx);
    padding: 32rpx;
    .detail-content-scroll {
      width: 100%;
      height: 100%;
      .coupon-detail-header {
        width: 100%;
        min-height: 160rpx;
        display: flex;
        align-items: center;
        .pro-img {
          width: 160rpx;
          height: 160rpx;
          background: #F5F6F8;
          border-radius: 8rpx;
          margin-right: 24rpx;
        }
        .base-info {
          flex: 1;
          min-height: 160rpx;
          max-height: 100%;
          .title {
            font-weight: 600;
            font-size: 30rpx;
            color: #333333;
            line-height: 44rpx;
            white-space: wrap;
            word-break: break-all;
          }
          .time {
            font-size: 24rpx;
            color: #999999;
            line-height: 36rpx;
            margin-top: 24rpx;
          }
        }
      }
      .coupon-detail-line {
        margin: 30rpx 0;
        width: 100%;
        border-bottom: 1PX dashed #EBEDF0;
      }
      .coupon-code {
        width: 100%;
        height: fit-content;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .image {
          width: 248rpx;
          height: 248rpx;
        }
        view {
          margin-top: 0rpx;
          font-size: 24rpx;
          color: #999999;
          line-height: 36rpx;
        }
        .code-tip {
          margin-top: 24rpx;
        }
      }
      .coupon-detail-s-line {
        width: 100%;
        height: 1PX;
        background: #EBEDF0;
        margin: 30rpx 0;
      }
      .coupon-list-info {
        width: 100%;
        height: fit-content;
        .list-header {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 32rpx;
          .label {
            font-weight: 600;
            font-size: 30rpx;
            color: #333333;
            line-height: 44rpx;
          }
          .desc {
            display: flex;
            align-items: center;
            font-size: 24rpx;
            color: #999999;
            line-height: 36rpx;
            > view {
              color: #EE3838;
              margin: 0 6rpx;
            }
          }
        }
        .list-wrap {
          width: 100%;
          height: 160rpx;
          overflow: hidden;
          .list-item {
            width: 100%;
            display: flex;
            align-items: center;
            font-size: 26rpx;
            color: #333333;
            line-height: 40rpx;
            margin-top: 24rpx;
            .point {
              width: 4rpx;
              height: 4rpx;
              background: #000000;
              border-radius: 50%;
              margin-right: 8rpx;
            }
            .card-code {
              margin-right: 16rpx;
            }
            .text {
              margin-right: 16rpx;
            }
            .card-status {
              margin-left: auto;
            }
          }
          .list-item:first-child {
            margin-top: 0;
          }
          .list-item.disabled {
            color: #999999 !important;
            .point {
              background: #999999;
            }
          }
        }
        .show-more {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24rpx;
          color: #155BD4;
          line-height: 36rpx;
          margin-top: 24rpx;
          > image {
            width: 24rpx;
            height: 24rpx;
            margin-left: 4rpx;
          }
        }
        .hide-more {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24rpx;
          color: #155BD4;
          line-height: 36rpx;
          margin-top: 24rpx;
          > image {
            width: 24rpx;
            height: 24rpx;
            margin-left: 4rpx;
            transform: rotate(180deg);
          }
        }
      }
      .coupon-other-info {
        width: 100%;
        height: fit-content;
        padding-bottom: 40rpx;
        .title {
          font-weight: 600;
          font-size: 30rpx;
          color: #333333;
          line-height: 44rpx;
          margin-bottom: 32rpx;
        }
        .other-info-list {
          width: 100%;
          .item {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 24rpx;
            .label {
              font-size: 26rpx;
              color: #999999;
              line-height: 40rpx;
            }
            .text {
              font-size: 26rpx;
              color: #333333;
              line-height: 40rpx;
            }
          }
        }
      }
    }
  }
}
</style>
