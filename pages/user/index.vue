<script>
import UserDetail from "./detail.vue";
import UserList from "./list.vue";

export default {
  name: "index",
  components: {UserDetail, UserList},
  data() {
    return {
      pageTabKey: 'list', // list 列表  detail 详情
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    changePageTab(targetKey) {
      this.pageTabKey = targetKey;
    }
  },
}
</script>

<template>
  <view>
    <user-list v-show="pageTabKey === 'list'" @changePageTab="changePageTab" />
    <user-detail v-if="pageTabKey === 'detail'" @changePageTab="changePageTab" />
  </view>
</template>

<style scoped lang="scss">
</style>
