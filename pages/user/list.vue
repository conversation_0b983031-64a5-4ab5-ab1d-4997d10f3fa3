<template>
  <view>
    <view class="search-box" v-if="!pageLoading">
      <view class="search-item">
        <uni-easyinput v-model="form.keyword" placeholder="用户名/手机号" @input="input"></uni-easyinput>
      </view>
      <view class="search-item">
        <uni-data-select
          v-model="form.user_type"
          :localdata="user_type_desc"
          trim="both"
          primaryColor="#BBBBBB"
          placeholder="手机号状态"
        ></uni-data-select>
      </view>
      <view class="search-item">
        <uni-data-select
          v-model="form.inquiry_status"
          :localdata="inquiry_status_desc"
          trim="both"
          primaryColor="#BBBBBB"
          placeholder="初诊状态"
        ></uni-data-select>
      </view>
      <view class="search-btn">
        <u-button class="ui-btn-base btn" color="#155BD4" @click="onSearch">筛选</u-button>
      </view>
      <view class="search-clear" @click="resetForm">
        <image class="logo" src="/static/image/user/clear.png" mode="aspectFit"></image>
        <view>清除条件</view>
      </view>
      <view class="search-add-btn">
        <u-button type="primary" class="ui-btn-base custom-add-btn" icon="plus" color="#155BD4" @click="createUser">新增用户</u-button>
      </view>
    </view>
    <view class="list-wrap" v-if="!pageLoading">
      <view class="user-card-list">
        <scroll-view
          class="card-scroll"
          :scroll-top="scrollTop"
          scroll-y="true"
          @touchmove.stop
          @scrolltolower="loadingMore"
        >
          <view class="card-box">
            <view class="card-item" v-for="item in list" :key="item.uid">
              <view class="item-header" @click="toDetail(item)">
                <view class="item-avatar">
                  <image class="avatar" :src="item.avatar" mode="aspectFit"></image>
                  <image v-if="isVip(item.vip_info || [])" class="vip" src="@/static/image/user/980vip.png" mode="aspectFit"></image>
                </view>
                <view class="item-user">
                  <view class="user-wrap">
                    <view :class="['user-info', true ? 'user-info-add' : '']">
                      <view class="user-name">
                        <view class="name">
                          <!--  <c-tooltip :text="item.name" />-->
                          {{ item.real_name || '-' }}
                        </view>
                        <image v-if="+item.auth_status === 2" class="async" src="@/static/image/user/async.png"></image>
                      </view>
                      <view class="user-mobile">
                        <view class="status" v-if="+item.mobile_type === 2">暂存</view>
                        <view class="mobile">
                          {{ item.mobile || '-'}}
                        </view>
                      </view>
                    </view>
                    <view class="user-add" v-if="item.first_visit_status === '1'" @click.stop="openFirstVisitDrawer(item)">初诊表待补充</view>
                    <view class="user-has-add" v-else-if="item.first_visit_status === '2'" @click.stop="openFirstVisitDrawer(item)">初诊表已更新</view>
                  </view>
                  <scroll-view class="user-tags-scroll" :scroll-x="true">
                    <view class="user-tags">
                      <view class="tags" v-for="tag in item.label_list" :key="tag.id">
                        {{ tag.tag_name || '-' }}
                      </view>
                    </view>
                  </scroll-view>
                </view>
              </view>
              <view class="item-content item-data-scroll">
                <view class="item-data-scroll" >
                  <view class="item-data">
<!--                    <view class="data-item">-->
<!--                      <view :class="['count-box']">-->
<!--                        <view class="count red">22</view>-->
<!--                        <view class="count-text red">今日</view>-->
<!--                      </view>-->
<!--                      <view class="type-desc red">-->
<!--                        预-->
<!--                      </view>-->
<!--                    </view>-->
                    <view class="data-item" @click="toDetail(item, '2')">
                      <view class="count-box">
                        <view class="count">{{ item.xn_service_info.service_card_count || 0  }}</view>
                        <view class="count-text">次可用</view>
                      </view>
                      <view class="type-desc">
                        服
                      </view>
                    </view>
                    <view class="data-item" @click="toDetail(item, '3')">
                      <view class="count-box">
                        <view class="count">{{ item.tdj_service_info.service_card_count || 0 }}</view>
                        <view class="count-text">次可兑</view>
                      </view>
                      <view class="type-desc">
                        通
                      </view>
                    </view>
                    <view class="data-item" @click="toDetail(item, '4')">
                      <view class="money-box">
                        <view class="unit">￥</view>
                        <view class="amount">{{ getPriceAmount(item) }}</view>
                        <view class="point">.</view>
                        <view class="decimal">{{ getPriceDecimal(item) }}</view>
                      </view>
                      <view class="type-desc">
                        储
                      </view>
                    </view>
                  </view>
                </view>
              </view>
              <view class="item-line"></view>
              <view class="item-footer" @click="checkAiStatus(item)">
                咨询
              </view>
            </view>
            <view class="noMoreText" v-if="showNoMoreText">没有更多数据了</view>
          </view>
        </scroll-view>
      </view>
    </view>
    <c-loading :show="tableLoading" />

    <confirm-tip-dialog
      v-model="tipDialog.show"
      :title="tipDialog.title"
      :show-loading="tipDialog.showLoading"
      :ok-text="tipDialog.okText"
      :cancel-text="tipDialog.cancelText"
      :namespace="tipDialog.namespace"
      @on-ok="toAiConsult"
    />
    <create-user-drawer v-model="createVisible" :echoValue="echoValue" @successCreateRecord="createSuccess"></create-user-drawer>

    <!-- 初诊信息登记表 -->
    <first-visit-drawer v-model="firstVisitVisble" :row="current_row" @success="getList"></first-visit-drawer>
  </view>
</template>

<script>
import CTooltip from "../../components/cTooltip/index.vue";
import UButton from "../../uni_modules/uview-ui/components/u-button/u-button.vue";
import UserService from "../../api/user";
import patientService from "../../api/patient";
import {getUid} from "../../utils/runtime";
import createUserDrawer from "../ai-consult/components/consult-drawer/components/createUserDrawer.vue";
import ConfirmTipDialog from "../../components/confirm-tip-dialog/index.vue";
import firstVisitDrawer from '@/components/first-visit-drawer/index.vue'
import CLoading from "../../components/cLoading/loading.vue";

export default {
  name: "user-list",
  components: {CLoading, ConfirmTipDialog, createUserDrawer, UButton, firstVisitDrawer, CTooltip},
  props: {},
  data() {
    return {
      scrollTop: 0,
      status_options: [],
      user_type_desc: [],
      inquiry_status_desc: [],
      pageLoading: false,
      tableLoading: false,
      list: [],
      total: 0,
      form: {
        keyword: '', // 用户姓名/手机号
        user_type: '', // 手机号状态（1:正常手机号,2：临时手机号（140/141号段）,3：暂存手机号）
        inquiry_status: '', // 初诊状态（1.初诊表待填写 2.初诊表已填写 3.非初诊用户）
      }, // 查询列表所用form 表单参数
      page: {
        page: 1,
        pageSize: 15,
      },
      showNoMoreText: false,
      createVisible: false,
      echoValue: '',
      tipDialog: {
        show: false,
        showLoading: true,
        namespace: "",
        title: "",
        okText: "",
        cancelText: "",
        mr_id: '',
        pr_id: '',
      },

      firstVisitVisble: false,
      current_row: {}
    }
  },
  created() {
    this.list = []
    this.pageLoading = true
  },
  mounted() {
    this.getUserOptions()
    this.getList()
    const query = this.$store.getters['global/getQuery'];
    if (query.tabKey && query.source) {
      this.toDetail({uid: query.uid}, query.tabKey)
    }
  },
  computed: {
    isVip () {
      return list => {
        return list?.some(item => item.user_type == '1' || item.user_type == '4' )
      }
    },
    getPriceAmount() {
      return item => {
        if (!+item?.recharge_money) return 0;
        return Math.floor(+item?.recharge_money)
      }
    },
    getPriceDecimal() {
      return item => {
        if (!+item?.recharge_money) return '00';
        const decimal = Math.floor(item?.recharge_money * 100) % 100
        return decimal === 0 ? '00' : decimal
      }
    },
  },
  methods: {
    // 打开出诊表抽屉
    openFirstVisitDrawer(item) {
      this.current_row = item
      this.firstVisitVisble = true
    },

    resetForm() {
      this.form.keyword = ''
      this.form.user_type = ''
      this.form.inquiry_status = ''
      this.page.page = 1
      this.getList()
    },
    onSearch() {
      this.page.page = 1;
      this.getList()
    },
    loadingMore() {
      if (this.page.page * this.page.pageSize >= +this.total) return
      this.page.page += 1
      this.getList('scroll')
    },
    getList(type) {
      this.tableLoading = true
      UserService.getUserTableList({
        ...this.form,
        ...this.page,
      }).then(res => {
        if (type === 'scroll') {
          this.list = this.list.concat(res?.users || [])
        } else {
          this.list = res?.users || [];
        }
        this.total = +res?.total || 0;
        if (+this.total <= 15) {
          this.showNoMoreText = false
        } else {
          this.showNoMoreText = this.page.page * this.page.pageSize >= +this.total
        }
      }).finally(() => {
        this.pageLoading = false
        this.tableLoading = false
      })
    },
    getUserOptions() {
      UserService.getUserOptions({}).then(res => {
        this.inquiry_status_desc = Object.keys(res?.inquiry_status_desc)?.map(key => ({
          value: key,
          text: res?.inquiry_status_desc?.[key]?.desc || ''
        })) || []
        this.user_type_desc = Object.keys(res?.user_type_desc)?.map(key => ({
          value: key,
          text: res?.user_type_desc?.[key]?.desc || ''
        })) || []
      })
    },
    input(e) {
      console.log(e, 'e')
    },
    toDetail(item, tab = '') {
      // 设置路由参数
      this.$store.commit('global/SET_QUERY', {
        id: item.uid,
        tabKey: tab,
      })
      this.$emit('changePageTab', 'detail')
    },
    toAiConsult(namespace) {
      return new Promise((resolve) => {
        if (namespace === 'has-ai') {
          // 设置路由参数
          this.$store.commit('global/SET_QUERY', {
            menuId: '0',
            pt_id: this.tipDialog.pt_id,
            mr_id: this.tipDialog.mr_id
          })
          resolve(true)
        }
        if (namespace === 'no-ai') {
          let params = {
            pt_id: this.tipDialog.pt_id,
            mr_id: this.tipDialog.mr_id,
            created_mid: getUid()
          }
          patientService.createInquiryRecord(params).then(res => {
            // todo 检测
            // if (res.mr_id === '0' || !res.mr_id) {
            //   uni.showToast({
            //     title: `mr_id参数异常(${res.mr_id})`,
            //     icon: 'none'
            //   })
            //   return
            // }
            uni.showToast({
              title: '建档成功'
            })
            // 设置路由参数
            this.$store.commit('global/SET_QUERY', {
              menuId: '0',
              pt_id: this.tipDialog.pt_id,
              mr_id: res.mr_id
            })
          }).catch((err) => {
            uni.showToast({
              title: err.errmsg,
              type: 'error',
            })
          }).finally(() => {
            resolve(true)
          })
        }
      })
    },
    checkAiStatus(item) {
     this.tableLoading = true;
      UserService.checkUserHasMr({
        pt_id: item.pt_id,
      }).then(res => {
        this.tableLoading = false;
        if (+res?.mr_id > 0) {
          this.tipDialog.title = '当前用户正在咨询中，是否继续咨询'
          this.tipDialog.okText = '继续咨询'
          this.tipDialog.cancelText = '取消'
          this.tipDialog.namespace = 'has-ai'
          this.tipDialog.mr_id = res?.mr_id
          this.tipDialog.pt_id = item.pt_id
          this.tipDialog.show = true
          return
        }
        this.tipDialog.title = '是否对该用户创建一个快捷咨询'
        this.tipDialog.okText = '立即咨询'
        this.tipDialog.cancelText = '取消'
        this.tipDialog.namespace = 'no-ai'
        this.tipDialog.mr_id = res?.mr_id
        this.tipDialog.pt_id = item.pt_id
        this.tipDialog.show = true
      }).catch((err) => {
        uni.showToast({
          title: err.errmsg,
          type: 'error',
        })
        this.tableLoading = false;
      })
    },
    createUser() {
      this.createVisible = true
      this.echoValue = ''
    },
    createSuccess() {
      this.resetForm()
    },
  }
}
</script>

<style scoped lang="scss">
.search-box {
  width: 100%;
  background: #FFFFFF;
  padding: 24rpx;
  display: flex;
  align-items: center;
  .search-item {
    width: 440rpx;
    margin-right: 16rpx;
  }
  .search-btn {
    width: 140rpx;
    margin-right: 32rpx;
    .btn {
      height: 74rpx;
    }

  }
  .search-clear {
    width: fit-content;
    display: flex;
    align-items: center;
    > image {
      width: 32rpx;
      height: 32rpx;
      margin-right: 8rpx;
    }
    > view {
      font-size: 24rpx;
      color: #155BD4;
      transition: color 0.3s;
    }
    > view:active {
      color: rgba(21,91,212, 0.5);
    }
  }
  .search-add-btn {
    width: 208rpx;
    margin-left: auto;
    .custom-add-btn {
      height: 74rpx;
      ::v-deep .u-icon__icon {
        font-size: 24rpx !important;
        margin-right: 12rpx !important;
      }
    }
  }
}
.list-wrap {
  width: 100%;
  height: calc(100vh - 128rpx);
  .user-card-list {
    width: 100%;
    height: 100%;
    padding: 24rpx 12rpx;
    overflow: hidden;
    .card-scroll {
      width: 100%;
      height: 100%;
      .card-box {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        .card-item {
          width: calc(33.3333% - 24rpx);
          flex-shrink: 0;
          background: #ffffff;
          border-radius: 8rpx;
          margin-bottom: 24rpx;
          margin-left: 12rpx;
          margin-right: 12rpx;
          padding-top: 24rpx;
          .item-header {
            width: 100%;
            display: flex;
            position: relative;
            .item-avatar {
              width: 96rpx;
              height: 96rpx;
              flex-shrink: 0;
              margin-left: 32rpx;
              margin-right: 24rpx;
              .avatar {
                width: 100%;
                height: 100%;
                border-radius: 64rpx;
                overflow: hidden;
              }
              .vip {
                width: 80rpx;
                height: 34rpx;
                bottom: 30rpx;
                left: 6rpx;
                z-index: 1;
              }
              .vip-fans {
                bottom: 36rpx;
              }
            }
            .item-user {
              width: calc(100% - 152rpx);
              .user-wrap {
                width: 100%;
                display: flex;
                .user-info {
                  max-width: 280rpx;
                  .user-name {
                    width: 100%;
                    display: flex;
                    align-items: center;
                    .name {
                      width: fit-content;
                      max-width: 240rpx;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                    }
                    .async {
                      width: 32rpx;
                      height: 32rpx;
                      flex-shrink: 0;
                      margin-left: 8rpx;
                    }
                  }
                  .user-mobile {
                    width: 100%;
                    display: flex;
                    align-items: center;
                    margin: 8rpx 0;
                    .status {
                      flex-shrink: 0;
                      width: 60rpx;
                      height: 32rpx;
                      background: #FFF3DF;
                      border-radius: 4rpx;
                      font-size: 22rpx;
                      color: #FFA300;
                      line-height: 32rpx;
                      text-align: center;
                      margin-right: 12rpx;
                    }
                    .mobile {
                      flex: 1;
                      font-size: 26rpx;
                    }
                  }
                }
                .user-info-add {
                  max-width: 280rpx;
                }

                .user-add, .user-has-add {
                  width: 192rpx;
                  height: 56rpx;
                  background: #FFAA00;
                  border-radius: 8rpx 0rpx 0rpx 8rpx;
                  padding: 10rpx 24rpx;
                  font-weight: 400;
                  font-size: 24rpx;
                  color: #FFFFFF;
                  line-height: 36rpx;
                  margin-left: auto;
                  flex-shrink: 0;
                  text-align: center;
                }
                .user-has-add {
                  background: rgba(21,91,212,0.08);
                  color: #155BD4;
                }
              }
              .user-tags-scroll {
                width: calc(100% - 32rpx);
                height: 40rpx;
                .user-tags {
                  width: fit-content;
                  display: flex;
                  align-items: center;
                  .tags {
                    flex-shrink: 0;
                    padding: 0 8rpx;
                    border: 1px solid #DCDFE6;
                    font-size: 22rpx;
                    color: #666666;
                    line-height: 32rpx;
                    text-align: center;
                    margin-right: 8rpx;
                    border-radius: 4rpx;
                  }
                }
              }
            }
          }
          .item-content {
            width: 100%;
            height: 124rpx;
            margin: 24rpx 0;
            padding: 0 32rpx;
            position: relative;
            .item-data-scroll {
              width: 100%;
              padding: 16rpx 0;
              .item-data {
                width: 100%;
                height: 92rpx;
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex-wrap: nowrap;
                .data-item {
                  width: 33.33333%;
                  height: 100%;
                  display: flex;
                  flex-direction: column;
                  justify-content: space-between;
                  align-items: center;
                  flex-shrink: 0;
                  .count-box {
                    height: 44rpx;
                    display: flex;
                    align-items: baseline;
                    .count {
                      font-weight: 500;
                      font-size: 32rpx;
                      color: #333333;
                    }
                    .count-text {
                      font-size: 24rpx;
                      color: #999999;
                    }
                  }
                  .money-box {
                    height: 44rpx;
                    display: flex;
                    align-items: baseline;
                    font-size: 24rpx;
                    color: #333333;
                    font-weight: 500;
                    .amount {
                      font-size: 32rpx;
                    }
                  }
                  .type-desc {
                    width: 32rpx;
                    height: 32rpx;
                    background: #FFFFFF;
                    border-radius: 4rpx;
                    border: 2rpx solid #155BD4;
                    font-weight: 400;
                    font-size: 22rpx;
                    color: #155BD4;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    box-sizing: border-box;
                  }
                }
                .red {
                  color: #EE3838 !important;
                  border-color: #EE3838 !important;
                }
              }
            }
            .follow {
              position: absolute;
              width: 92rpx;
              height: 124rpx;
              right: 32rpx;
              top: 0;
              box-shadow: -24rpx 0 24rpx -24rpx rgba(0, 0, 0, 0.3);
              display: flex;
              align-items: center;
              justify-content: flex-end;
              background: #ffffff;
              z-index: 1;
              > view {
                font-size: 24rpx;
                color: #155BD4;
                line-height: 36rpx;
                transition: color 0.3s;
              }
              > view:active {
                color: rgba(21,91,212, 0.5);
              }
              > image {
                width: 24rpx;
                height: 24rpx;
              }
            }
          }
          .item-line {
            width: 100%;
            height: 1PX;
            background: #EBEDF0;
          }
          .item-footer {
            width: 100%;
            height: 88rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            font-size: 28rpx;
            color: #155BD4;
          }
          .item-footer:active {
            color: #ffffff;
            background: #115bd4;
          }
        }
        .card-item:last-child {
          margin-right: auto;
        }
      }
    }
  }

  .protocol-popup-wrap {
    width: 50vw;
    height: 60vh;
    border-radius: 10rpx;
    background: #ffffff;
    .protocol-header {
      width: 100%;
      padding: 0 32rpx;
      height: 120rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 42rpx;
      font-weight: 600;
      position: relative;
      .header-close {
        width: fit-content;
        position: absolute;
        right: 24rpx;
        top: 16rpx;
      }
    }
    .protocol-body {
      width: 100%;
      height: calc(100% - 368rpx);
      display: flex;
      justify-content: center;
      align-items: center;
      border-top: 1PX solid #DCDFE6;
      border-bottom: 1PX solid #DCDFE6;
    }
    .protocol-footer {
      width: 100%;
      height: 228rpx;
      .protocol-footer-wrap {
        width: 100%;
        padding: 16rpx 32rpx;
        display: flex;
        justify-content: flex-end;
        .label {
          font-size: 26rpx;
          padding-right: 12rpx;
        }
        .text {
          min-width: 124rpx;
          font-size: 26rpx;
          border-bottom: 1px solid #DCDFE6;
        }
        .btn {
          padding: 12rpx 28rpx;
          font-size: 26rpx;
          color: #FFFFFF;
          background: #155BD4;
          border-radius: 8rpx;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .cancel-btn {
          color: #666666;
          border: 1PX solid #666666;
          background: #ffffff;
        }
      }
    }
  }
  .sign-popup-wrap {
    width: 50vw;
    height: 680rpx;
    border-radius: 10rpx;
    background: #ffffff;
    .sign-header {
      width: 100%;
      padding: 0 32rpx;
      height: 120rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 42rpx;
      font-weight: 600;
    }
    .sign-body {
      width: 100%;
      height: calc(100% - 240rpx);
      padding: 32rpx;
      border-top: 1PX solid #DCDFE6;
      border-bottom: 1PX solid #DCDFE6;
      .sign-wrap {
        width: 100%;
        height: 100%;
        border: 1PX solid #F5F5F5;
      }
    }
    .sign-footer {
      width: 100%;
      padding: 0 32rpx;
      height: 120rpx;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      .btn {
        padding: 12rpx 28rpx;
        font-size: 26rpx;
        border-radius: 8rpx;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .cancel-btn {
        color: #666666;
        border: 1PX solid #666666;
        background: #ffffff;
        padding: 12rpx 36rpx;
      }
      .ok-btn {
        color: #ffffff;
        border: 1PX solid #115bd4;
        background: #115bd4;
        margin-left: 16rpx;
        font-size: 26rpx;
        color: #666666;
      }
    }
  }
}
.noMoreText {
  width: 100%;
  padding: 30rpx;
  font-size: 28rpx;
  display: flex;
  justify-content: center;
}
</style>
