import S from '../../utils/utils'
import {getUserInfo } from 'utils/runtime'

const state = {
  goods_list: [], // 购物车数据
}

const getters = {
  getGoodsList(state) {
    const goods_list = state.goods_list || []
    // 没有store 则读取本地缓存
    if (!goods_list || goods_list.length === 0) {
      const localGoodsList = S.getStorageSync(getUserInfo().mobile)
      if (localGoodsList) {
        return localGoodsList
      }
    }
    return state.goods_list
  }
}

const mutations = {
  // 清除所有商品
  CLEAR_LIST (state) {
    state.goods_list = []
  },
  // 清除所有商品，包括本地存储空间内的数据
  CLEAR_GOODS_LIST(state) {
    state.goods_list = []
    S.removeStorageSync(getUserInfo().mobile)
  },
  // 增加新的商品
  ADD_GOODS: (state, row) => {
    state.goods_list.push(row)
    S.setStorageSync(getUserInfo().mobile, state.goods_list)
  },
  // 指定的商品数量改变
  CHANGE_GOODS_NUM: (state, {index, cart_num}) => {
    state.goods_list[index].cart_num = cart_num
    S.setStorageSync(getUserInfo().mobile, state.goods_list)
  },
  // 删除指定id的商品
  DELETE_GOODS: (state, index) => {
    state.goods_list.splice(index, 1)
    S.setStorageSync(getUserInfo().mobile, state.goods_list)
  }
}

const actions = {
  // 增加商品，传入goods_id, 内部判断新增还是增加数量， 默认数量+1
  /**
   * type: change:直接替换， add: 数量+1 ，decrease: 数量-1
   * */
  updateGoods ({state, commit}, {type, row, num = 0}) {
    let index = state.goods_list.findIndex(item => item.id === row.id)
    if ( index === -1 ) {
      if ( type === 'change' ) {
        commit('ADD_GOODS', { ...row, cart_num: num })
      }else if (type === 'add') {
        commit('ADD_GOODS', { ...row, cart_num: 1 })
      }
    }else{
      if ( type === 'change' ) {
        console.log("=>(cart.js:46) index, num", index, num);
        commit('CHANGE_GOODS_NUM', { index, cart_num: num})
      }else if (type === 'add') {
        commit('CHANGE_GOODS_NUM', { index, cart_num: Number(state.goods_list[index].cart_num || 0) + 1})
      }else if ( type === 'decrease' ) {
        commit('CHANGE_GOODS_NUM', { index, cart_num: Number(state.goods_list[index].cart_num || 0) - 1})
      }
    }
  },
  deleteGoods ({state, commit}, goods_id) {
    let index = state.goods_list.findIndex(item => item.id === goods_id)
    if (index !== -1) {
      commit('DELETE_GOODS', index)
    }
  },
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};
