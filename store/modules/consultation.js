import {debounce, cloneDeep} from "lodash";
import request from "@/utils/request";
const init_record_info = {
  present_medical_history: '', //现病史
  engrave_text: '', //主诉
  diagnostic_methods: '', //望闻问切
  past: '', //既往史
  physique: '', // 体格检查
}
const init_treat_goods = {
  list: [],
};
const state = {
  record_info: {
    ...init_record_info,
  },
  treat_goods: cloneDeep(init_treat_goods),
  diaVisible: false,
  shouldSaveDraft: false,
}


const mutations = {
  CLEAR_ALL_LIST(state) {

    state.shouldSaveDraft = false;
    state.record_info = {
      ...init_record_info,
    };
    state.treat_goods = cloneDeep(init_treat_goods)
  },
  CHANGE_STATE: (state, payload) => {
    state[payload.type] = cloneDeep(payload.value);
  },
  CHANGE_DRAFT(state, { type, value }) {
    if (type) {
      state[type] = cloneDeep({ ...state[type], ...value });
    }
  },
  UPDATE_SAVE_STATUS(state, value) {
    state.shouldSaveDraft = value;
  },
}

const actions = {
  saveDraft({state, commit}, payload){
    commit('CHANGE_DRAFT',payload)
    savePresDraft(state, payload.mr_id,commit)
  }
}
const savePresDraft = debounce(
  async function (state, mr_id,commit) {
    if(!state.shouldSaveDraft){
      commit('UPDATE_SAVE_STATUS', true)
      return;
    }
    await request.post('/ipad/inquiry.draft.savepres', handleDraftData(state, mr_id)).then(
      res => {
      },
      err => {
        console.error(err.errmsg);
      }
    );
  },
  2000,
  { leading: false, trailing: true, maxWait: 4000 }
);
//处理要保存的草稿数据
function handleDraftData(state, mr_id) {
  return {
    mr_id,
    info: {
      treat_goods: state.treat_goods,
      record_info: state.record_info,
    },
  };
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
