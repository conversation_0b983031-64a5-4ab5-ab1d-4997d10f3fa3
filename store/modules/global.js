import { writePageRouteQuery, removePageRouteQuery, getPageRouteQuery } from '@/utils/runtime';
export default {
  namespaced: true,
  state: {
    query: {},
  },
  mutations: {
    SET_QUERY(state, query) {
      writePageRouteQuery(query)
      state.query = query
    },
    CLEAR_QUERY(state) {
      state.query = {}
      removePageRouteQuery()
    },
  },
  actions: {
    setQuery({ commit }, query) {
      commit("SET_QUERY", query)
    },
    clearQuery({ commit }) {
      commit("CLEAR_QUERY")
    }
  },
  getters: {
    getQuery(state) {
      const query = state.query
      // 没有store 则读取本地缓存
      if (!query || Object.keys(query).length === 0) {
        const localQuery = getPageRouteQuery()
        if (localQuery) {
          return localQuery
        }
      }
      return state.query
    }
  }
}
