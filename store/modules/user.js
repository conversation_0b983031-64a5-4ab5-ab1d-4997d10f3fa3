export default {
  namespaced: true,
  state: {
    token: uni.getStorageSync('token') || '',
    userInfo: null,
    count: 99
  },
  mutations: {
    SET_TOKEN(state, token) {
      state.token = token
      uni.setStorageSync('token', token)
    },
    SET_USER_INFO(state, info) {
      state.userInfo = info
    },
    CLEAR_USER(state) {
      state.token = ''
      state.userInfo = null
      uni.removeStorageSync('token')
    }
  },
  actions: {
    // 退出登录
    logout({ commit }) {
      commit('CLEAR_USER')
      uni.reLaunch({
        url: '/pages/login/index'
      })
    }
  }
} 