/* Loading 样式 */
.v-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.v-loading-global {
  position: fixed;
  z-index: 9999;
}

.v-loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.loading-icon {
  animation: rotate 1s linear infinite;
}

.loading-text {
  font-size: 28rpx;
  color: #666666;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
