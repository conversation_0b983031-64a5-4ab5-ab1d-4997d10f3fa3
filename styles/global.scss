@import "./mixins.scss";
/* 主题色 */
$color-primary: #155BD4;
$info-color: #2db7f5;
$success-color: #19be6b;
$warning-color: #ff9900;
$error-color: #ed4014;

/* 文字颜色 */
$text-color-primary: #303133;    // 主要文字
$text-color-regular: #606266;    // 常规文字
$text-color-secondary: #909399;  // 次要文字
$text-color-placeholder: #c0c4cc;// 占位文字
$text-color-disabled: #c0c4cc;   // 禁用文字

/* 边框颜色 */
$border-color-base: #dcdfe6;     // 一级边框
$border-color-light: #e4e7ed;    // 二级边框
$border-color-lighter: #ebeef5;  // 三级边框
$border-color-extra-light: #f2f6fc; // 四级边框

/* 背景色 */
$bg-color-page: #F6F6F6;     // 页面背景
$bg-color-white: #ffffff;    // 白色背景
$bg-color-black: #000000;    // 黑色背景

/* 字体大小 */
$font-size-mini: 24rpx;
$font-size-small: 26rpx;
$font-size-base: 28rpx;
$font-size-medium: 30rpx;
$font-size-large: 32rpx;
$font-size-huge: 36rpx;

/* 字体粗细 */
$font-weight-regular: 400;
$font-weight-medium: 500;
$font-weight-bold: 700;

/* 行高 */
$line-height-tight: 1.3;
$line-height-base: 1.5;
$line-height-loose: 1.7;

/* 圆角 */
$border-radius-small: 4rpx;
$border-radius-base: 8rpx;
$border-radius-medium: 12rpx;
$border-radius-large: 16rpx;
$border-radius-round: 40rpx;
$border-radius-circle: 50%;

/* 间距 */
$spacing-mini: 8rpx;
$spacing-small: 16rpx;
$spacing-base: 24rpx;
$spacing-large: 32rpx;
$spacing-huge: 40rpx;

/* 阴影 */
$box-shadow-base: 0 2rpx 4rpx rgba(0, 0, 0, 0.12), 0 0 6rpx rgba(0, 0, 0, 0.04);

$box-shadow-light: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.1);
$box-shadow-dark: 0 2rpx 4rpx rgba(0, 0, 0, 0.12), 0 0 6rpx rgba(0, 0, 0, 0.12);

/* 过渡 */
$transition-base: all .3s cubic-bezier(.645,.045,.355,1);
$transition-fade: opacity .3s cubic-bezier(.645,.045,.355,1);
$transition-transform: transform .3s cubic-bezier(.645,.045,.355,1);

/* z-index */
$z-index-normal: 1;
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;

/* 布局相关 */
$header-height: 88rpx;      // 头部高度
$side-bar-width: 120rpx;    // 侧边栏宽度
$right-section-width: 830rpx; // 右侧栏宽度
// 全局基础样式
page {
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  padding: 0;
}

// 常用flex布局类
.flex {
  display: flex;

  // 主轴方向
  &-row { flex-direction: row; }
  &-row-reverse { flex-direction: row-reverse; }
  &-column { flex-direction: column; }
  &-column-reverse { flex-direction: column-reverse; }

  // 换行
  &-wrap { flex-wrap: wrap; }
  &-nowrap { flex-wrap: nowrap; }
  &-wrap-reverse { flex-wrap: wrap-reverse; }

  // 主轴对齐
  &-start { justify-content: flex-start; }
  &-end { justify-content: flex-end; }
  &-center { justify-content: center; }
  &-between { justify-content: space-between; }
  &-around { justify-content: space-around; }
  &-evenly { justify-content: space-evenly; }

  // 交叉轴对齐
  &-items {
    &-start { align-items: flex-start; }
    &-end { align-items: flex-end; }
    &-center { align-items: center; }
    &-baseline { align-items: baseline; }
    &-stretch { align-items: stretch; }
  }

  // 多行对齐
  &-content {
    &-start { align-content: flex-start; }
    &-end { align-content: flex-end; }
    &-center { align-content: center; }
    &-between { align-content: space-between; }
    &-around { align-content: space-around; }
    &-stretch { align-content: stretch; }
  }

  // 自身对齐
  &-self {
    &-start { align-self: flex-start; }
    &-end { align-self: flex-end; }
    &-center { align-self: center; }
    &-baseline { align-self: baseline; }
    &-stretch { align-self: stretch; }
  }

  // 常用组合
  &-center-center {
    justify-content: center;
    align-items: center;
  }
  &-between-center {
    justify-content: space-between;
    align-items: center;
  }
  &-around-center {
    justify-content: space-around;
    align-items: center;
  }

  // flex 属性
  &-1 { flex: 1; }
  &-auto { flex: auto; }
  &-none { flex: none; }

  // gap
  &-gap-mini { gap: $spacing-mini; }
  &-gap-small { gap: $spacing-small; }
  &-gap-base { gap: $spacing-base; }
  &-gap-large { gap: $spacing-large; }
  &-gap-huge { gap: $spacing-huge; }
}

// 单独的 flex 属性类
.flex-grow { flex-grow: 1; }
.flex-shrink { flex-shrink: 1; }
.flex-no-grow { flex-grow: 0; }
.flex-no-shrink { flex-shrink: 0; }

// 常用间距类

// 文字对齐
.text {
  &-left { text-align: left; }
  &-center { text-align: center; }
  &-right { text-align: right; }
}




// 页面容器
uni-page-body {
  width: 100%;
  height: 100%;
}

// 视图盒模型
uni-view {
  box-sizing: border-box;
}


.uni-select__input-box .uni-select__input-placeholder, .uni-easyinput__placeholder-class{
  font-weight: 400;
  font-size: 13px;
  color: #BBBBBB;
  line-height: 18px;
}
.uni-stat__select {
  .uni-select,.uni-select__input-box{
    height: 37px;
  }
}

.base-title {
  font-weight: 600;
  font-size: 32rpx;
  color: #262626;
  line-height: 40rpx;
  flex: 1;
}

.base-box-shadow{
  box-shadow: 0rpx 6rpx 12rpx 0rpx rgba(20,21,22,0.04);
}

.show-word-limit{
  position: absolute;
  top: 50%;
  right: 40px;
  transform: translateY(-50%);
  color: #808695;
  font-size: 13px;
}

.ecs {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  text-overflow: ellipsis !important;
  overflow: hidden !important;
  word-break:break-all !important;
  white-space: pre-line !important;
}

.ecs-x {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-break: break-all;
  flex: 1;
}

.ecs-xs {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-break: break-all;
}

.ecs-2 {
  -webkit-line-clamp: 2;
}

.ecs-3 {
  -webkit-line-clamp: 3;
}

.ecs-4 {
  -webkit-line-clamp: 4;
}

.ecs-5 {
  -webkit-line-clamp: 5;
}


// 内部UI自定义走查
// 阴影
$ui-draw-shadow-base: 0rpx 6rpx 12rpx 0rpx rgba(20,21,22,0.06); // 抽屉头部阴影
$ui-table-shadow-base: 0rpx 6rpx 12rpx 0rpx rgba(20,21,22,0.04); // 表格头部阴影

// 按钮
$ui-btn-drawer-height: 80rpx; // 抽屉按钮高度
$ui-btn-base-height: 74rpx; // 基础按钮高度

// 圆角
$ui-border-radius-base: 8rpx;

// 按钮间距
$ui-btn-distabce: 24rpx;

// 确定好的通用的样式
// 按钮, $ui-btn-base为所有按钮默认的样式，默认高度37px, 抽屉按钮高度40px
// 普通按钮
.ui-btn-base {
   border-radius: $ui-border-radius-base;
   height: $ui-btn-base-height;
   background: #FFFFFF;
   border: 2rpx solid rgba(220, 223, 230, 0.8);
   box-sizing: border-box;
   display: flex;
   justify-content: center;
   align-items: center;
   font-weight: 400;
   font-size: 28rpx;
   color: #666666;
   margin: 0;
  &:active {
    transform: scale(0.97);
    opacity: 0.9;
  }
 }
.ui-btn-primary {
  background: #155BD4 !important;
  color: #FFFFFF !important;
}

// 抽屉
.ui-btn-drawer-base {
  border-radius: $ui-border-radius-base;
  height: $ui-btn-drawer-height;
  background: #FFFFFF;
  border: 2rpx solid rgba(220, 223, 230, 0.8);
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 400;
  font-size: 28rpx;
  color: #666666;
  margin: 0;
  &:active {
    transform: scale(0.97);
    opacity: 0.9;
  }
}

.ui-btn-drawer-default{
  min-width: 188rpx;
}
.ui-btn-drawer-primary {
  min-width: 188rpx;
  //width: 280rpx;
  background: #155BD4 !important;
  color: #FFFFFF !important;
}
.ui-btn-distance-l {
  margin-left: $ui-btn-distabce;
}
.ui-btn-distance-r {
  margin-right: $ui-btn-distabce;
}
