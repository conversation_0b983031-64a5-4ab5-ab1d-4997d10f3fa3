/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
@import '@/uni_modules/uview-ui/theme.scss';
@import "/styles/global.scss";
@import "/styles/uview.theme.scss";

/* uni-ui 主题变量 */
$uni-primary: $color-primary;
$uni-success: $success-color;
$uni-warning: $warning-color;
$uni-error: $error-color;
$uni-info: $info-color;
$uni-color-primary: $color-primary;
$uni-color-success: #4cd964;
$uni-color-warning: #F99C3F;
$uni-color-error: #dd524d;
/* uni-ui 文字基本颜色 */
$uni-text-color: $text-color-primary; // 基本色
$uni-text-color-inverse: #fff; // 反色
$uni-text-color-grey: $text-color-regular; // 辅助灰色
$uni-text-color-placeholder: $text-color-placeholder; // 占位符
$uni-text-color-disable: $text-color-disabled; // 禁用

/* uni-ui 背景颜色 */
$uni-bg-color: $bg-color-white;
$uni-bg-color-grey: $bg-color-page;
$uni-bg-color-hover: #f1f1f1;
$uni-bg-color-mask: rgba(0, 0, 0, 0.4);

/* uni-ui 边框颜色 */
$uni-border-color: $border-color-base;

/* uni-ui 尺寸变量 */
$uni-font-size-sm: $font-size-mini;
$uni-font-size-base: $font-size-base;
$uni-font-size-lg: $font-size-large;

$uni-spacing-row-sm: $spacing-mini;
$uni-spacing-row-base: $spacing-base;
$uni-spacing-row-lg: $spacing-large;

$uni-spacing-col-sm: $spacing-mini;
$uni-spacing-col-base: $spacing-base;
$uni-spacing-col-lg: $spacing-large;

/* uni-ui 圆角 */
$uni-border-radius-sm: $border-radius-small;
$uni-border-radius-base: $border-radius-base;
$uni-border-radius-lg: $border-radius-large;
$uni-border-radius-circle: $border-radius-circle;

/* uni-ui 阴影 */
$uni-shadow-sm: $box-shadow-base;
$uni-shadow-base: $box-shadow-light;
$uni-shadow-lg: $box-shadow-dark;

.spacing-base-l {
  margin-left: $spacing-base;
}

.spacing-base-r {
  margin-right: $spacing-base;
}

.spacing-base {
  margin-right: $spacing-base;
  margin-left: $spacing-base;
}

uni-button[type="primary"] {
  background-color: $color-primary;

  &[plain] {
    background-color: #F5F6F8;
    border-color: transparent;
  }
}

uni-button {
  font-size: $font-size-base;
  border: 1px solid rgba(0, 0, 0, .2);
}
uni-button::after {
  border: unset;
}
uni-button[type="warn"] {
  background-color: $uni-color-warning;
  border: 1px solid $uni-color-warning;
}

.button-hover[type="primary"] {
  background-color: darken($color-primary, 10%);
}

.button-hover[type="primary"] {
  background-color: darken($color-primary, 10%);

}

.button-hover[type="warn"] {
  background-color: darken($uni-color-warning, 10%);
  border: 1px solid darken($uni-color-warning, 10%);
}


.link-primary {
  color: $color-primary;
  font-size: 24rpx;
  line-height: 36rpx;

  &:active {
    color: lighten($color-primary, 20%);
  }
}

.empty-box{
  background: #fff;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
