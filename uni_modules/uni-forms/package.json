{"id": "uni-forms", "displayName": "uni-forms 表单", "version": "1.4.13", "description": "由输入框、选择器、单选框、多选框等控件组成，用以收集、校验、提交数据", "keywords": ["uni-ui", "表单", "校验", "表单校验", "表单验证"], "repository": "https://github.com/dcloudio/uni-ui", "engines": {"HBuilderX": ""}, "directories": {"example": "../../temps/example_temps"}, "dcloudext": {"sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": "https://www.npmjs.com/package/@dcloudio/uni-ui", "type": "component-vue"}, "uni_modules": {"dependencies": ["uni-scss", "uni-icons"], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y", "alipay": "n"}, "client": {"App": {"app-vue": "y", "app-nvue": "y"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "y", "百度": "y", "字节跳动": "y", "QQ": "y", "京东": "u"}, "快应用": {"华为": "u", "联盟": "u"}, "Vue": {"vue2": "y", "vue3": "y"}}}}}