// import {debounce as _debounce} from 'lodash'
import { bind } from 'lodash';
import Vue from 'vue';

/**
 * 格式化金额
 * 使用方法：
 * v-format-money="123456.789"           // 基本使用
 * v-debounce:500="handleClick"       // 指定延迟500ms
 * v-debounce.immediate="handleClick" // 立即执行
 * @param {number|string} amount - 金额
 * @param {number} [digits=2] - 小数位数
 * @returns {string} 格式化后的金额
 */

export const formatMoney = {
  bind(el, binding) {
    const format = (value) => {
      // 获取配置选项
      const options = {
        digits: binding.arg || 2,
        separator: binding.modifiers.separator,
        prefix: binding.modifiers.prefix || '￥',
        suffix: binding.modifiers.suffix ? ' 元' : '',
        zero: binding.modifiers.zero !== false, // 是否保留末尾0
        round: binding.modifiers.round !== false // 是否四舍五入
      };

      // 处理空值
      if (value === null || value === undefined || value === '') {
        const zero = '0'.repeat(options.digits);
        return `${options.prefix}0${options.digits ? '.' + zero : ''}${options.suffix}`;
      }

      try {
        let num = Number(value);

        // 处理四舍五入
        if (options.round) {
          const multiplier = Math.pow(10, options.digits);
          num = Math.round(num * multiplier) / multiplier;
        }

        // 将数字转为字符串并分割整数和小数部分
        let [intPart, decimalPart = ''] = num.toString().split('.');

        // 处理整数部分的千位分隔符
        if (options.separator) {
          intPart = intPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        }

        // 处理小数部分
        if (options.digits > 0) {
          // 如果需要补零
          if (options.zero) {
            decimalPart = decimalPart.padEnd(options.digits, '0');
          }
          // 截取指定位数
          decimalPart = decimalPart.slice(0, options.digits);
        } else {
          decimalPart = '';
        }

        // 组合最终结果
        const formattedValue = decimalPart ? `${intPart}.${decimalPart}` : intPart;
        el.innerHTML = `${options.prefix}${formattedValue}${options.suffix}`;
      } catch (error) {
        el.innerHTML = value.toString();
      }
    };

    format(binding.value);
    el._formatMoney = format;
  },

  update(el, binding) {
    el._formatMoney?.(binding.value);
  },

  unbind(el) {
    delete el._formatMoney;
  }
};

/**
 * 防抖指令 (Vue2版本)
 * 使用方法：
 * v-debounce:click="handleClick"           // 点击事件防抖
 * v-debounce:input.500="handleInput"       // 输入事件防抖，延迟500ms
 * v-debounce:scroll.immediate="handleScroll" // 滚动事件防抖，立即执行
 */
export const debounce = {
  // 指令的定义
  inserted(el, binding) {
    let timeoutId = null;

    // 获取传入的防抖时间，默认为300毫秒
    const [eventType,debounceTime] = binding.arg.split(':')||['input',300];
    // 触发防抖操作的事件类型，默认为input事件
    // 监听目标元素的指定事件，并进行防抖处理
    el.addEventListener(eventType, () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        // 执行传入的回调函数
        binding.expression && binding.value();
      }, debounceTime);
    });
  },

  // 可选的其他钩子函数，如unbind等
  unbind(el) {
    el.removeEventListener(eventType);
  }
};

// Loading 指令
export const loading = {
  bind(el, binding) {
    const isGlobal = binding.modifiers.global;

    // 创建 loading 元素
    const loadingEl = document.createElement('div');
    loadingEl.className = `v-loading${isGlobal ? ' v-loading-global' : ''}`;

    // 创建 loading 内容
    const spinnerEl = document.createElement('div');
    spinnerEl.className = 'v-loading-spinner';
    spinnerEl.innerHTML = `
      <view class="loading-icon">
        <uni-icons type="spinner-cycle" size="32" color="#115bd4"></uni-icons>
      </view>
      <view class="loading-text">加载中...</view>
    `;

    loadingEl.appendChild(spinnerEl);

    // 如果不是全局loading，需要给父元素设置定位
    if (!isGlobal) {
      const position = getComputedStyle(el).position;
      if (!position || position === 'static') {
        el.style.position = 'relative';
      }
    }

    el.loadingElement = loadingEl;
    el.appendChild(loadingEl);

    // 初始状态
    if (binding.value) {
      el.loadingElement.style.display = 'flex';
    } else {
      el.loadingElement.style.display = 'none';
    }
  },

  update(el, binding) {
    if (binding.value !== binding.oldValue) {
      el.loadingElement.style.display = binding.value ? 'flex' : 'none';
    }
  },

  unbind(el) {
    if (el.loadingElement) {
      el.loadingElement.remove();
      delete el.loadingElement;
    }
  }
};

// 注册指令
Vue.directive('format-money', formatMoney);
Vue.directive('debounce', debounce);
Vue.directive('loading', loading);
