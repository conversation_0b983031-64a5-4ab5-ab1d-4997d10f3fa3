/**
 * 环境变量管理工具
 * 用于获取和管理从Android Studio配置的环境变量
 */

class EnvManager {
  constructor() {
    this.envVars = {};
    this.isInitialized = false;
  }

  /**
   * 初始化环境变量
   * 从本地存储中读取Android原生配置的环境变量
   */
  init() {
    try {
      // 从本地存储获取环境变量
      this.envVars = {
        ENV_TYPE: uni.getStorageSync('ENV_TYPE') || 'TEST',
        API_BASE_URL: uni.getStorageSync('API_BASE_URL') || '',
        DEBUG_MODE: uni.getStorageSync('DEBUG_MODE') || false,
      };

      // 获取所有以APP_开头的自定义环境变量
      const keys = uni.getStorageInfoSync().keys || [];
      keys.forEach(key => {
        if (key.startsWith('APP_')) {
          this.envVars[key] = uni.getStorageSync(key);
        }
      });

      this.isInitialized = true;
      console.log('环境变量初始化完成:', this.envVars);
    } catch (error) {
      console.error('环境变量初始化失败:', error);
    }
  }

  /**
   * 获取环境变量值
   * @param {string} key 环境变量名
   * @param {any} defaultValue 默认值
   * @returns {any} 环境变量值
   */
  get(key, defaultValue = null) {
    if (!this.isInitialized) {
      this.init();
    }
    return this.envVars[key] !== undefined ? this.envVars[key] : defaultValue;
  }

  /**
   * 获取当前环境类型
   * @returns {string} 环境类型 (DEV/TEST/UAT/PROD)
   */
  getEnvType() {
    return this.get('ENV_TYPE', 'TEST');
  }

  /**
   * 获取API基础URL
   * @returns {string} API基础URL
   */
  getApiBaseUrl() {
    return this.get('API_BASE_URL', '');
  }

  /**
   * 是否为调试模式
   * @returns {boolean} 是否为调试模式
   */
  isDebugMode() {
    return this.get('DEBUG_MODE', false);
  }

  /**
   * 是否为开发环境
   * @returns {boolean} 是否为开发环境
   */
  isDevelopment() {
    return this.getEnvType() === 'DEV';
  }

  /**
   * 是否为测试环境
   * @returns {boolean} 是否为测试环境
   */
  isTest() {
    return this.getEnvType() === 'TEST';
  }

  /**
   * 是否为UAT环境
   * @returns {boolean} 是否为UAT环境
   */
  isUat() {
    return this.getEnvType() === 'UAT';
  }

  /**
   * 是否为生产环境
   * @returns {boolean} 是否为生产环境
   */
  isProduction() {
    return this.getEnvType() === 'PROD';
  }

  /**
   * 获取所有环境变量
   * @returns {object} 所有环境变量
   */
  getAll() {
    if (!this.isInitialized) {
      this.init();
    }
    return { ...this.envVars };
  }

  /**
   * 设置环境变量（仅在内存中，不持久化）
   * @param {string} key 环境变量名
   * @param {any} value 环境变量值
   */
  set(key, value) {
    this.envVars[key] = value;
  }

  /**
   * 重新加载环境变量
   */
  reload() {
    this.isInitialized = false;
    this.init();
  }
}

// 创建单例实例
const envManager = new EnvManager();

export default envManager;
