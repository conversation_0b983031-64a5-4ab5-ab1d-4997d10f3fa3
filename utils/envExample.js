/**
 * 环境变量使用示例
 * 展示如何在不同场景下使用环境变量
 */

// 在Vue组件中使用环境变量的示例
export const vueComponentExample = {
  mounted() {
    // 获取环境类型
    const envType = this.$env.getEnvType();
    console.log('当前环境:', envType);

    // 判断环境
    if (this.$env.isProduction()) {
      console.log('生产环境');
    } else if (this.$env.isDevelopment()) {
      console.log('开发环境');
    }

    // 获取API基础URL
    const apiUrl = this.$env.getApiBaseUrl();
    if (apiUrl) {
      console.log('API基础URL:', apiUrl);
    }

    // 获取调试模式
    const isDebug = this.$env.isDebugMode();
    console.log('调试模式:', isDebug);

    // 获取自定义环境变量
    const customVar = this.$env.get('APP_CUSTOM_VAR', 'default_value');
    console.log('自定义变量:', customVar);

    // 获取所有环境变量
    const allEnvVars = this.$env.getAll();
    console.log('所有环境变量:', allEnvVars);
  }
};

// 在普通JS文件中使用环境变量的示例
import envManager from './env.js';

export const jsFileExample = () => {
  // 确保环境变量已初始化
  envManager.init();

  // 根据环境配置不同的行为
  if (envManager.isProduction()) {
    // 生产环境逻辑
    console.log('执行生产环境逻辑');
  } else {
    // 非生产环境逻辑
    console.log('执行开发/测试环境逻辑');
  }

  // 根据调试模式决定是否输出日志
  if (envManager.isDebugMode()) {
    console.log('调试信息: 详细的调试日志');
  }
};

// 在API请求中使用环境变量的示例
export const apiRequestExample = () => {
  const baseUrl = envManager.getApiBaseUrl() || envManager.get('DEFAULT_API_URL', 'https://api.example.com');
  
  return uni.request({
    url: `${baseUrl}/api/data`,
    method: 'GET',
    header: {
      'Content-Type': 'application/json',
      // 可以根据环境添加不同的请求头
      ...(envManager.isDebugMode() && { 'X-Debug-Mode': 'true' })
    }
  });
};

// 条件编译与环境变量结合使用的示例
export const conditionalCompilationExample = () => {
  let config = {};

  // #ifdef APP-PLUS
  // App环境下使用原生环境变量
  config = {
    envType: envManager.getEnvType(),
    apiUrl: envManager.getApiBaseUrl(),
    debug: envManager.isDebugMode()
  };
  // #endif

  // #ifdef H5
  // H5环境下可能需要不同的配置
  config = {
    envType: process.env.NODE_ENV === 'development' ? 'DEV' : 'PROD',
    apiUrl: process.env.VUE_APP_API_URL || 'https://api.example.com',
    debug: process.env.NODE_ENV === 'development'
  };
  // #endif

  // #ifdef MP-WEIXIN
  // 微信小程序环境
  config = {
    envType: 'PROD', // 小程序通常是生产环境
    apiUrl: 'https://api.example.com',
    debug: false
  };
  // #endif

  return config;
};
