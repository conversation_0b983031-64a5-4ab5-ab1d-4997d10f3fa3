import config from '@/config';
import utils from '@/utils/utils';
import { getUserInfo } from '@/utils/runtime';


// 请求计数器
let requestCount = 0;

// 显示loading
const showLoading = () => {
  if (requestCount === 0) {
    // uni.showLoading({
    // 	title: '加载中...',
    // 	mask: true
    // })
  }
  requestCount++;
};

// 隐藏loading
const hideLoading = () => {
  requestCount--;
  if (requestCount === 0) {
    uni.hideLoading();
  }
};

// 添加请求拦截器
uni.addInterceptor('request', {
  invoke(options) {
    try {
      // 显示loading
      if (!options.hideLoading) {
        showLoading();
      }

      // 生成环境参数和验签
      const systemInfo = uni.getSystemInfoSync();
      const userInfo = getUserInfo() || {}; // 添加默认值防止undefined
      const baseInfo = uni.getAppBaseInfo()

      const evnParams = {
        timezone: 8,
        resolution: systemInfo.screenWidth + '*' + systemInfo.screenHeight,
        channel: 'pad',
        os: 'pad',
        device_id: systemInfo.deviceId,
        uidentity: userInfo.mer_uid || '',
        clinic_id: userInfo.clinic_id || '',
        spm: '',
        ts: utils.dayjs().unix(),
        app_version: baseInfo.appVersion,
        ifsign: utils.makeApiSign(options.data || {}),
      };
      // // 对象类型参数进行双重JSON.stringify处理
      Object.keys(evnParams).forEach(key => {
        if (typeof evnParams[key] === 'object') {
          evnParams[key] = JSON.stringify(JSON.stringify(evnParams[key]));
        }
      });

      // 设置IFENV请求头
      if (!options.header) {
        options.header = {};
      }
      options.header['IFENV'] = JSON.stringify(evnParams);
    } catch (error) {
      console.error('请求拦截器错误:', error);
      // 不阻塞请求，继续执行
    }
  },
  complete() {},
});

uni.addInterceptor('response', {
  success(res) {
    // 隐藏loading
    hideLoading();
    return res;
  },
  fail(err) {
    // 隐藏loading
    hideLoading();
    return err;
  },
});

// Vue 2 转 Vue 3, 在 main.js 中写入以下代码即可
function isPromise(obj) {
  return !!obj && (typeof obj === 'object' || typeof obj === 'function') && typeof obj.then === 'function';
}

uni.addInterceptor({
  returnValue(res) {
    if (!isPromise(res)) {
      return res;
    }
    return new Promise((resolve, reject) => {
      res.then(res => {
        if (!res) {
          resolve(res);
          return;
        }
        if (res[0]) {
          reject(res[0]);
        } else {
          resolve(res[1]);
        }
      });
    });
  },
});
