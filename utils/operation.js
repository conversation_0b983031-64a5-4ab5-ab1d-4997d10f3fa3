import { plus, minus, times, divide as div, round } from './number-precision';

function add(a, b, p = 2) {
  return round(plus(a, b), p);
}

function subtract(a, b, p = 2) {
  return round(minus(a, b), p);
}

function multiply(a, b, p = 2) {
  return round(times(a, b), p);
}

function divide(a, b, p = 2) {
  return round(div(a, b), p);
}

function toPrecision(num, p = 2) {
 return  round(num, p);
}

export const $operator = {
  add,
  subtract,
  multiply,
  divide,
  toPrecision,
};
