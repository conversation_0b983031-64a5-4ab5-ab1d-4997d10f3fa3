import store from '@/store'

// 检查登录状态
export const checkLogin = () => {
  const token = store.state.user.token
  if (!token) {
    uni.navigateTo({
      url: '/pages/login/index'
    })
    return false
  }
  return true
}

// 检查权限
export const checkPermission = (permission) => {
  const userInfo = store.state.user.userInfo
  if (!userInfo || !userInfo.permissions) {
    return false
  }
  return userInfo.permissions.includes(permission)
} 