import config from '@/config'
import utils from '@/utils/utils'
import {getUserInfo, getRefreshToken,getAccessToken, removeLoginInfo} from "@/utils/runtime"

// token 刷新相关
let isRefreshing = false
let requests = []
let isTokenInvalid = false
class Request {
  constructor() {
    this.baseURL = config.BASE_URL
    this.timeout = config.TIMEOUT
  }

  async refreshTokenRequest() {
    try {
      if (isRefreshing) {
        return new Promise((resolve) => {
          requests.push((token) => {
            resolve(token)
          })
        })
      }

      isRefreshing = true
      const refreshToken = getRefreshToken()
      const token = getAccessToken()

      if (!refreshToken) {
        throw new Error('No refresh token')
      }

      const response = await uni.request({
        url: this.baseURL + '/ipad/member.refreshtoken',
        method: 'POST',
        header: {
          'Authorization': `Bearer ${refreshToken}`,
        },
        data: {
          token: token
        }
      })

      const res = response.data
      if (res.errcode == '0') {
        const { token, refresh_token } = res.data
        utils.setStorageSync('accessToken', token)
        utils.setStorageSync('refreshToken', refresh_token)
        requests.forEach(cb => cb(token))
        requests = []
        return token
      } else {
        throw new Error(res.errmsg || '刷新token失败')
      }
    } catch (error) {
      requests = []
      removeLoginInfo()
      uni.showToast({
        title: '登录已过期，请重新登录',
        icon: 'none'
      })
      return Promise.reject(error)
    } finally {
      isRefreshing = false
    }
  }

  async request(options = {}) {
    // 如果URL已经包含baseURL，则不再拼接
    if (!options.url.startsWith('http')) {
      options.url = this.baseURL + options.url
    }
    options.timeout = options.timeout || this.timeout
    options.method = options.method || 'GET'

    // 设置通用header
    if (!options.header) {
      options.header = {}
    }

    const token = utils.getStorageSync('accessToken')

    // 设置token
    if (token) {
      options.header['Authorization'] = `Bearer ${token}`
    }

    // 如果正在刷新token且不是刷新token的请求，则将请求加入队列
    if (isRefreshing && !options.url.includes('refreshtoken')) {
      return new Promise((resolve) => {
        requests.push((token) => {
          const retryConfig = {...options}
          retryConfig.header['Authorization'] = `Bearer ${token}`
          resolve(this.request(retryConfig))
        })
      })
    }

    try {
      const response = await uni.request(options)

      // 统一处理错误情况
      if (response.statusCode !== 200) {
        let title = '请求失败'
        // 处理网络错误情况
        if (response.errMsg) {
          if (response.errMsg.includes('timeout')) {
            title = '请求超时，请重试'
          } else if (response.errMsg.includes('fail')) {
            title = '请求异常，请稍后重试'
          } else {
            title = `${title}(${response.statusCode})`
          }
        }

        !options.hideError && uni.showToast({
          title,
          icon: 'none'
        })
        return Promise.reject({
          errMsg: title,
          statusCode: response.statusCode,
          data: response.data
        })
      }

      const res = response.data
      const errcode = res.errcode?.toString()

      // 2. 处理token过期
      if (errcode === '7001') {
        if (!options.url.includes('refreshtoken')) {
          try {
            const newToken = await this.refreshTokenRequest()
            options.header['Authorization'] = `Bearer ${newToken}`
            return this.request(options)
          } catch (error) {
            return Promise.reject(error)
          }
        }
      }

      // 3. 处理需要登出的错误码
      if (/^7[0-9]{3}$/.test(errcode)) {
        isTokenInvalid = true
        !options.hideError && uni.showToast({
          title: res.errmsg || res.message,
          icon: 'none'
        })
        removeLoginInfo()
        return Promise.reject(res)
      }

      // 4. 处理其他业务错误
      if (errcode && errcode !== '0') {
        !options.hideError && uni.showToast({
          title: res.errmsg || res.message,
          icon: 'none'
        })
        return Promise.reject(res)
      }

      // 5. 返回成功数据
      return res.data
    } catch (err) {
      // 这里基本不会执行到，保险起见还是处理一下
      const title = '请求异常，请稍后重试'
      !options.hideError && uni.showToast({
        title,
        icon: 'none'
      })
      return Promise.reject(err)
    }
  }

  get(url, data = {}, options = {}) {
    return this.request({
      url,
      data,
      method: 'GET',
      ...options
    })
  }

  post(url, data = {}, options = {}) {
    return this.request({
      url,
      data,
      method: 'POST',
      ...options
    })
  }

  put(url, data = {}, options = {}) {
    return this.request({
      url,
      data,
      method: 'PUT',
      ...options
    })
  }

  delete(url, data = {}, options = {}) {
    return this.request({
      url,
      data,
      method: 'DELETE',
      ...options
    })
  }

  upload(url, {
    filePath,
    name = 'file',
    formData = {},
    header = {}
  } = {}) {
    return new Promise((resolve, reject) => {
      uni.uploadFile({
        url: config.UPLOAD_URL + url,
        filePath,
        name,
        formData,
        header,
        success: (response) => {
          resolve(response)
        },
        fail: (error) => {
          reject(error)
        }
      })
    })
  }
}

export default new Request()
