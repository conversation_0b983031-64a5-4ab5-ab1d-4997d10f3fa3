import utils from "@/utils/utils";
import $store from '../store/index'

export function getUserInfo() {
	return utils.getStorageSync('userInfo') || {}
}

export function getUserMrId() {
	return utils.getStorageSync('userMrId') || {}
}

export function getAccessToken() {
	return utils.getStorageSync('accessToken') || ''
}

export function getRefreshToken() {
	return utils.getStorageSync('refreshToken') || ''
}

export function getUid() {
	return getUserInfo().mer_uid || ''
}

export function getClinicId() {
	return getUserInfo().clinic_id || ''
}

export function getIsOpc() {
	return getUserInfo().is_opc || '0'
}

export function isDigitalStore() {
	return getIsOpc() === '1'
}

export function writeLoginInfo(userInfo, accessToken, refreshToken) {
	utils.setStorageSync('userInfo', userInfo)

  console.log("=>(runtime.js:30) setStorageSync/userInfo", userInfo);
	utils.setStorageSync('accessToken', accessToken)
	if (refreshToken) {
		utils.setStorageSync('refreshToken', refreshToken)
	}
}

export function getPageRouteQuery(query) {
  return utils.getStorageSync('routeQuery')
}
export function writePageRouteQuery(query) {
  utils.setStorageSync('routeQuery', query)
}

export function removePageRouteQuery(query) {
  utils.removeStorageSync('routeQuery')
}


export function removeLoginInfo() {
  $store.commit('cart/CLEAR_LIST')
	utils.removeStorageSync('userInfo')
	utils.removeStorageSync('accessToken')
	utils.removeStorageSync('refreshToken')
	utils.removeStorageSync('routeQuery')
	uni.reLaunch({
		url: '/pages/login/index'
	})
}
