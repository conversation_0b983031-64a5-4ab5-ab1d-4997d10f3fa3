import utils from "./utils";
import config from '@/config'
import { fetchEventSource, EventStreamContentType } from '@microsoft/fetch-event-source';

const prettyObject = msg => {
	const obj = msg;
	if (typeof msg !== 'string') {
		msg = JSON.stringify(msg, null, '  ');
	}
	if (msg === '{}') {
		return obj.toString();
	}
	if (msg.startsWith('```json')) {
		return msg;
	}
	return ['```json', msg, '```'].join('\n');
};

const sseFetch = (url, data, callback, end) => {
	// 环境参数
	url = config.SSE_BASE_URL + url;

	let evnParams = {
		timezone: 8,
		resolution: document.body.clientWidth + '*' + document.body.clientHeight,
		channel: 'pad',
		os: 'pad',
		device_id: data.device_id,
		uidentity: data.uid,
		clinic_id: data.clinic_id,
		spm: '',
		ts: utils.dayjs().unix(),
		// "app_version": "20250115235143",
		// "his_version": "v3",
		// "his_version_1": "v3",
	};
	const Authorization = `Bearer ${data.token}`

	data = JSON.parse(JSON.stringify(data));
	delete data.token;
	delete data.uid;
	delete data.device_id;
	data.ifsign = utils.makeApiSign(data)
	//将数组和对象类型的数据转换为JSON字符串
	Object.keys(data).forEach(item => {
		if (typeof data[item] == 'object') {
			data[item] = JSON.stringify(data[item]);
		}
		if (typeof data[item] == 'undefined') {
			data[item] = '';
		}
	});
	// 默认情况下，axios将JavaScript对象序列化为JSON。
	// 要以application / x-www-form-urlencoded格式发送数据
	// 可以使用URLSearchParams API
	const params = new URLSearchParams();
	for (let k in data) params.append(k, data[k]);

	// 超时时间
	const REQUEST_TIMEOUT_MS = 25 * 1000;
	const abortAnswerController = new AbortController();
	// 启动请求异常定时器， 如果超时还没拿到任何返回，主动关闭
	const requestTimeoutId = setTimeout(() => {
		abortAnswerController.abort();
		end &&
		end({
			done: true,
			msg: null,
		});
	}, REQUEST_TIMEOUT_MS);
	let requestBackTimeoutId = null;

	// status 表示是否正在获取回答
	let status = false;
	// 需要输出的文本
	let responseText = '';
	// 剩余的文本
	let remainText = '';
	// 是否已经完成(正常或者异常都算完成)
	let finished = false;
	let errorMsg = null;
	// 是否已经开始
	let started = false;
	// animate response to make it looks smooth
	const animateResponseText = () => {
		if (finished && remainText.length <= 0) {
			// 请求成功响应时，清除定时器
			clearTimeout(requestBackTimeoutId);
			end &&
			end({
				done: false,
				msg: null,
			});
			return;
		}
		if (abortAnswerController.signal.aborted) {
			// 请求成功响应时，清除定时器
			clearTimeout(requestBackTimeoutId);
			end &&
			end({
				done: true,
				msg: null,
			});
			return;
		}
		// ------ 处理字符串的逻辑 start ------
		const fetchCount = Math.max(1, Math.round(remainText.length / 60));
		const fetchText = remainText.slice(0, fetchCount);
		responseText += fetchText;
		remainText = remainText.slice(fetchCount);
		// ------ 处理字符串的逻辑 end ------

		// 注意宏任务、微任务问题
		callback(responseText);
		// 如果还没有完成，并且剩余内容也还有，就重新给定时器赋值
		// 如果REQUEST_TIMEOUT_MS内 还没有新的内容进来，那么就主动关闭fetch请求
		if (!finished && remainText.length > 0) {
			// 清除定时器
			clearTimeout(requestBackTimeoutId);
			requestBackTimeoutId = setTimeout(() => {
				abortAnswerController.abort();
				end &&
				end({
					done: true,
					msg: null,
				});
			}, REQUEST_TIMEOUT_MS);
		}
		// 如果还没有完成， 就继续递归输出， 哪怕此时没有内容， 假如一直没有
		// 如果已经完成了，但是还有剩余内容，继续递归输出
		(!finished || remainText.length !== '') && requestAnimationFrame(animateResponseText);
	};
	const chatPayload = {
		method: 'POST',
		headers: {
			'content-type': 'application/x-www-form-urlencoded',
			IFENV: JSON.stringify(evnParams),
			// accept: 'text/event-stream',
			'Authorization': Authorization,
			'Cache-Control': 'no-cache',
		},
		body: params,
		signal: abortAnswerController.signal,
	};

	const finish = msg => {
		status = false;
		finished = true;
		errorMsg = msg;
	};

	const error = e => {
		status = false;
		finished = true;
		console.error(e);
		abortAnswerController.abort();
	};

	status = true;
	fetchEventSource(url, {
		...chatPayload,
		async onopen(res) {
			clearTimeout(requestTimeoutId);
			const contentType = res.headers.get('content-type');
			if (contentType?.startsWith('text/plain')) {
				responseText = await res.clone().text();
				return finish();
			}

			if (!res.ok || !res.headers.get('content-type')?.startsWith(EventStreamContentType) || res.status !== 200) {
				const responseTexts = [responseText];
				let extraInfo = await res.clone().text();
				try {
					const resJson = await res.clone().json();
					extraInfo = prettyObject(resJson);
				} catch {}

				if (res.status === 401) {
					responseTexts.push('登录已过期。');
				}

				if (extraInfo) {
					responseTexts.push(extraInfo);
				}

				responseText = responseTexts.join('\n\n');

				return finish();
			}
		},
		onmessage(msg) {
			// 如果还没有完成，并且剩余内容也还有，就重新给定时器赋值
			// 如果REQUEST_TIMEOUT_MS内 还没有新的内容进来，那么就主动关闭fetch请求
			// 清除定时器
			clearTimeout(requestBackTimeoutId);
			requestBackTimeoutId = setTimeout(() => {
				abortAnswerController.abort();
				end &&
				end({
					done: true,
					msg: null,
				});
			}, REQUEST_TIMEOUT_MS);
			if (msg.data === '[DONE]' || finished) {
				return finish();
			}
			const text = msg.data;
			if (!text) return;
			try {
				const json = JSON.parse(text);
				const content = json?.data?.content;
				if (json.errcode === 0 && content) {
					remainText += content;
					if (!started) {
						// start animaion 递归处理数据， 处理时机使用requestAnimationFrame
						animateResponseText();
						started = true;
					}
					return;
				}
				if (json?.errcode === 0 && json?.data?.is_finish) {
					finish();
					return;
				}
				if (json?.errcode !== 0 && json.errcode !== 4001) {
					finish();
					end &&
					end({
						done: true,
						msg: json.errmsg,
					});
				}
				if (json.errcode === 4001) {
					finish();
					end &&
					end({
						done: true,
						msg: null,
					});
				}
			} catch (e) {
				console.error('[Request] parse error', e, text, msg);
			}
		},
		onclose() {
			finish();
		},
		onerror(e) {
			error(e);
		},
		openWhenHidden: true,
	});
};
export { sseFetch };
