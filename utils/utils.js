import config from '@/config'
import Dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import cloneDeep from 'lodash/cloneDeep';
Dayjs.extend(duration)
let util = {};
import {md5} from './js-md5';

util.makeApiSign = (args) => {
  const signKey = config.mapiSignKey
  let params = [];
  const copyArgs = cloneDeep(args)
  for (let key in copyArgs) {
    if (typeof copyArgs[key] === 'function') continue;
    if(typeof copyArgs[key] === 'object'){
      copyArgs[key] = JSON.stringify(args[key])
    }
    if (key != 'ifsign') params.push(copyArgs[key]);
  }
  let paramsStr = params.sort().join('');
  return md5(paramsStr + signKey);
};

util.getStorageSync = key => {
  return uni.getStorageSync(config.STORAGE_KEY + key);
}
util.setStorageSync = (key, value) => {
  return uni.setStorageSync(config.STORAGE_KEY + key, value);
}
util.removeStorageSync = key => {
  return uni.removeStorageSync(config.STORAGE_KEY + key);
}
/**
 * 生成随机字符串
 * @param len
 * @returns {string}
 */
util.random = function (len = 13) {
  const $chars = 'abcdefghjkmnpqrstuvwxyzABCDEFGHJKMNPQRSTUVWXYZ123456789'
  const maxPos = $chars.length
  let str = ''
  for (let i = 0; i < len; i++) {
    str += $chars.charAt(Math.floor(Math.random() * maxPos))
  }
  return str
}
util.dayjs = Dayjs;
util.isEmptyObject = (o) => {
  for (let p in o) {
    if (p !== undefined) {
      return false;
    }
  }
  return true;
}
util.formatDuration = (date, needMonth) => {
  const now = Dayjs(); // 当前时间
  const startDate = Dayjs(date); // 输入时间
  const diffInMilliseconds = now.diff(startDate); // 时间差（毫秒）
  const timeDuration = Dayjs.duration(diffInMilliseconds); // 转为 duration 对象

  const years = Math.floor(timeDuration.asYears());
  const months = timeDuration.months();
  const days = timeDuration.days();

  let result = '';

  // 判断年份
  if (years === 0) {
    return '1岁';
  }
  if (years === 1) {
    result += '1岁';
  } else if (years > 1) {
    result += years + '岁';
  }

  // 判断是否需要显示月数和天数
  if (needMonth) {
    if (months === 1) {
      result += '1个月';
    } else if (months > 1) {
      result += months + '个月';
    }
    if (days === 1) {
      result += '1天';
    } else if (days > 1) {
      result += days + '天';
    }
  }

  return result;
};

util.descToArrHandle = obj => {
  let arr = [];
  let kArr = Object.keys(obj);
  kArr.map((item, i) => {
    arr.push({
      id: item,
      desc: obj[item].desc || '',
      kw: obj[item].kw || '',
      sort: obj[item].sort || '',
      ...obj[item],
    });
  });
  return arr;
}

/**
 * 格式化金额
 * @param {number|string} value - 需要格式化的金额
 * @param {string} prefix - 货币符号前缀，默认为 ¥
 * @param {number} decimals - 保留小数位数，默认为 2
 * @returns {string} 格式化后的金额字符串
 */
util.formatMoney = (value, prefix = '¥', decimals = 2) => {
  if (value === '' || value === null || value === undefined) {
    return '-'
  }

  // 转换为数字
  const num = parseFloat(value)
  if (isNaN(num)) {
    return '-'
  }

  // 格式化数字
  const formatted = num.toFixed(decimals)

  // 如果是0，直接返回带前缀的0
  if (num === 0) {
    return `${prefix}0.${'0'.repeat(decimals)}`
  }

  return `${prefix}${formatted}`
}

util.splitPrice = (data = 0) => {
  let price = Number(data || 0).toFixed(2)
  let price_list = price.split('.')
  return price_list
}


util.defaultAvatar = (sex) => {
  let avatar = {
    'man': 'https://img-sn01.rsjxx.com/image/2025/0616/114550_79591.png',
    'woman': 'https://img-sn01.rsjxx.com/image/2025/0306/150751_62458.png'
  }
  return sex === '2' ? avatar.man : avatar.woman
}

export default util;
